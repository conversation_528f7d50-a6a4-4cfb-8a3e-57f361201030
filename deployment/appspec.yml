version: 0.0
os: linux
permissions:
    - {
          object: /var/www/erp-server/deploy,
          type: [directory],
          pattern: '**',
          mode: '755',
          owner: 'svd_admin',
          group: 'nginx',
      }
    - {
          object: /var/www/erp-server/deploy/vendor,
          type: [file],
          pattern: '**',
          mode: '640',
          owner: 'svd_admin',
          group: 'nginx',
      }
    - {
          object: /var/www/erp-server/deploy/bin,
          type: [file],
          pattern: 'console',
          mode: '750',
          owner: 'svd_admin',
          group: 'nginx',
      }
    - {
          object: /var/www/erp-server/deploy/public,
          type: [file],
          pattern: '**',
          mode: '640',
          owner: 'svd_admin',
          group: 'nginx',
      }
    - {
          object: /var/www/erp-server/deploy/bin/scripts,
          type: [file],
          pattern: '**',
          mode: '750',
          owner: 'svd_admin',
          group: 'nginx',
      }
files:
    - { source: erp-server, destination: /var/www/erp-server/deploy }
hooks:
    ApplicationStop:
        - { location: 'deploy-scripts/stop-server', timeout: 300, runas: root }
        - { location: 'deploy-scripts/stop-services', timeout: 60, runas: root }
    BeforeInstall:
        - { location: 'deploy-scripts/before-install', timeout: 60, runas: root }
    AfterInstall:
        - { location: 'deploy-scripts/after-install', timeout: 60, runas: root }
    ApplicationStart:
        - { location: 'deploy-scripts/start-services', timeout: 60, runas: root }
        - { location: 'deploy-scripts/start-server', timeout: 300, runas: root }
