# Shared YAML config pulled from another repo
include:
    -   project: 'son-video/support'
        ref: master
        file: '/.gitlab-ci-template.yml'

workflow:
    rules:
        # pipeline should only run for merge requests, scheduled pipelines or triggered manually from the Gitlab CI pipeline page
        -   if: $CI_PIPELINE_SOURCE =~ /^merge_request_event|schedule|web$/
        # or for branch `dev, master and validation`.
        -   if: $CI_COMMIT_BRANCH =~ /^(dev|master|validation)$/

default:
    image: $CI_REGISTRY/son-video/erp-server/dev:7.4
    interruptible: true
    tags:
        - saas-linux-small-amd64

variables:
    COMPOSER_CACHE_DIR: $CI_PROJECT_DIR/.composer-cache
    MYSQL_ROOT_PASSWORD: root
    # Filesystem for docker jobs
    DOCKER_DRIVER: overlay2
    # Files shared between jobs
    ARTIFACTS_DIR: $CI_PROJECT_DIR/artifacts
    CI_TESTS_STAGE:
        value: 'EXECUTE'
        description: "Indicates the action to do regarding tests"
        options:
            - 'EXECUTE'
            - 'SKIP'
    PHP_DEPENDENCIES:
        value: 'DEFAULT'
        description: "REGENERATE the CI rule regarding the generation of the PHP Dependencies (cached by default)"
        options:
            - 'DEFAULT'
            - 'REGENERATE'

stages:
    - install
    - test
    - build-deploy
    - deploy

# same composer.lock hash should always use the same vendors
.cache-php: &cache_php
    key:
        files:
            - composer.lock
    paths:
        - vendor/
        - public/bundles/

.run-test:
    rules:
        -   if: $CI_TESTS_STAGE != "SKIP" && $CI_COMMIT_BRANCH != "validation"

#####################################
# Install Stage
#####################################

install-php-dependencies:
    stage: install
    rules:
        -   if: $PHP_DEPENDENCIES == "REGENERATE"
        # trigger this job only if composer.lock has changed, or manual (see ruleschanges in doc)
        -   changes:
                - composer.lock
    extends:
        - .with-ssh
    cache:
        -   <<: *cache_php
            policy: pull-push
        # store dependency managers' caches for all branches
        -   key: ${CI_JOB_NAME}
            # must be inside $CI_PROJECT_DIR for gitlab-runner caching
            paths:
                - .composer-cache/ # defined through COMPOSER_CACHE_DIR (see composer doc)
            policy: pull-push
    script:
        # exit if cache already contains the vendor dir
        - if [[ "$PHP_DEPENDENCIES" != "REGENERATE" ]]; then test -d vendor && exit 10; fi
        - if [[ "$PHP_DEPENDENCIES" == "REGENERATE" ]]; then echo "REGENERATE has been forced from the UI"; fi
        # Environment variables for tests
        - rm .env.test
        - cp .env.gitlab .env.test
        - rm -rf vendor/
        # install
        - composer install --no-cache
    allow_failure:
        exit_codes: 10


#####################################
# Test Stage
#####################################

# build-test not necessary, all vendors are provided through cache

.common-test:
    extends:
        - .with-ssh
        - .run-test
    services:
        -   name: $CI_REGISTRY/son-video/docker-stack/erp-pg-schema:postgresql-10
            alias: erp-postgresql
        -   name: $CI_REGISTRY/son-video/docker-stack/mysql:5.7.31
            alias: erp-server-mysql-test
        -   name: $CI_REGISTRY/son-video/docker-stack/cups:latest
            alias: erp-server-cups
    cache:
        -   <<: *cache_php
            policy: pull
    before_script:
        # Environment variables for tests
        - rm .env.test
        - cp .env.gitlab .env.test

        # Prepare pg database
        - echo "erp-postgresql:5432:*:devteam:devteam" > ~/.pgpass
        - chmod 0600 ~/.pgpass
        - timeout 90s bash -c "until pg_isready -h erp-postgresql ; do sleep 5 ; done"

        - psql -h erp-postgresql -U devteam postgres < vendor/son-video/erp-pg-schema/src/schema/template.sql
        - psql -h erp-postgresql -U devteam postgres -c "CREATE DATABASE svd_erp_test_template OWNER svd_dev TEMPLATE svd_dev;"
        - psql -h erp-postgresql -U devteam postgres -c "CREATE DATABASE svd_erp_test OWNER svd_dev TEMPLATE svd_dev;"
        - psql -h erp-postgresql -U devteam postgres < vendor/son-video/erp-pg-schema/src/schema/roles.sql
        - mysql -h erp-server-mysql-test -u root -proot < sql/mysql/roles.sql

        ## prepare required cache directory
        - mkdir -p var/cache

unit-tests:
    stage: test
    extends: .common-test
    script:
        - mkdir -p var/test
        - chmod -R 777 ./var/test
        - vendor/bin/atoum -c .atoum.gitlab.php -d tests/Unit --no-code-coverage -ulr
    artifacts:
        reports:
            junit:
                - atoum-report.xml
        expire_in: 1 week

phpunit-e2e-tests:
    stage: test
    extends: .common-test
    script:
        # Seed databases
        - php -f .atoum.php

        - mkdir -p var/test
        - chmod -R 777 ./var/test
        - vendor/bin/phpunit --testdox --testsuite e2e

phpunit-unit-tests:
    stage: test
    extends: .common-test
    script:
        # Seed databases
        - php -f .atoum.php

        - mkdir -p var/test
        - chmod -R 777 ./var/test
        -  XDEBUG_MODE=coverage vendor/bin/phpunit --testdox --testsuite unit --coverage-cobertura coverage/cobertura.xml
    artifacts:
        reports:
            coverage_report:
                coverage_format: cobertura
                path: coverage/cobertura.xml

e2e-tests:
    stage: test
    extends: .common-test
    script:
        - vendor/bin/behat tests/Acceptance/features --colors -f pretty -o std -f junit -o xml


#####################################
# Build-deploy Stage
#####################################

build-deploy:
    stage: build-deploy
    rules:
        -   if: $CI_COMMIT_BRANCH == "validation"
        -   if: $CI_COMMIT_BRANCH == "master"
    cache:
        -   <<: *cache_php
            policy: pull # "composer install --no-dev" will update the local directory, but we don't want to cache (push) that
    script:
        - mkdir -p $ARTIFACTS_DIR/deploy
        - apt-get update && apt-get install -y make
        - cd deployment && make package && cd -

        # copy newly generated dependencies in artifacts for deployment in next recipe
        - cp -r deployment/erp-server.tar $ARTIFACTS_DIR/deploy/erp-server.tar
    artifacts:
        expire_in: 1 day
        paths:
            - $ARTIFACTS_DIR/


#####################################
# Deploy Stage
#####################################
.deployment:
    stage: deploy
    extends:
        - .with-ssh
    image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
    interruptible: true
    script: &deployment_scripts
        - apt-get update && apt-get install -y make git

        - git config --global user.name "${GITLAB_USER_NAME}"
        - git config --global user.email "${GITLAB_USER_EMAIL}"
        - git remote set-url --<NAME_EMAIL>:son-video/erp-server.git
        - git fetch -p --all --tags --force

        - cp -r $ARTIFACTS_DIR/deploy/erp-server.tar deployment/erp-server.tar

deploy-validation:
    rules:
        -   if: $CI_COMMIT_BRANCH == "validation"
    extends: .deployment
    script:
        - *deployment_scripts
        - cd deployment && make deploy-validation

deploy-staging:
    rules:
        -   if: $CI_COMMIT_BRANCH == "master"
    extends: .deployment
    script:
        - *deployment_scripts
        - cd deployment && make deploy-staging
