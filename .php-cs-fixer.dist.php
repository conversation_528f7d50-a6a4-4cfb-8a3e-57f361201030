<?php

declare(strict_types=1);

use PhpCsFixer\DocBlock\DocBlock;
use PhpCsFixer\DocBlock\Line;
use PhpCsFixer\Fixer\FixerInterface;
use PhpCsFixer\FixerDefinition\FixerDefinition;
use PhpCsF<PERSON>er\FixerDefinition\FixerDefinitionInterface;
use PhpCsFixer\Preg;
use PhpCsF<PERSON>er\Tokenizer\CT;
use PhpCsFixer\Tokenizer\Token;
use PhpCsFixer\Tokenizer\Tokens;
use PhpCsFixer\Tokenizer\TokensAnalyzer;
use PhpCsFixer\Utils;

/**
 * Fixer for using prettier-php to fix.
 */
final class PrettierPHPFixer implements FixerInterface
{
    public function getPriority(): int
    {
        // Allow prettier to pre-process the code before php-cs-fixer
        return 999;
    }

    public function isCandidate(Tokens $tokens): bool
    {
        return true;
    }

    public function isRisky(): bool
    {
        return false;
    }

    public function fix(SplFileInfo $file, Tokens $tokens): void
    {
        if (0 < $tokens->count() && $this->isCandidate($tokens) && $this->supports($file)) {
            $this->applyFix($file, $tokens);
        }
    }

    public function getName(): string
    {
        return 'Prettier/php';
    }

    public function supports(SplFileInfo $file): bool
    {
        return true;
    }

    private function applyFix(SplFileInfo $file, Tokens $tokens): void
    {
        exec("npx -s prettier $file", $prettierOutput, $result_code);
        if (0 === $result_code) {
            $code = implode(PHP_EOL, $prettierOutput);
            $tokens->setCode($code);
        }
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition('Pre format with prettier', []);
    }
}

/**
 * Quick hack of PhpUnitMethodCasingFixer for Atoum.
 *
 * @see https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/blob/master/src/Fixer/PhpUnit/PhpUnitMethodCasingFixer.php
 */
final class AtoumMethodCaseFixer implements FixerInterface
{
    public function getName(): string
    {
        return 'Atoum/test_method_case';
    }

    public function getDefinition(): FixerDefinitionInterface
    {
        return new FixerDefinition('Convert Atoum test methods to snake case', []);
    }

    /** Must run after PhpUnitTestAnnotationFixer. */
    public function getPriority(): int
    {
        return 0;
    }

    final public function isCandidate(Tokens $tokens): bool
    {
        return $tokens->isAllTokenKindsFound([T_CLASS, T_STRING]);
    }

    public function isRisky(): bool
    {
        return false;
    }

    public function fix(SplFileInfo $file, Tokens $tokens): void
    {
        if (0 < $tokens->count() && $this->isCandidate($tokens) && $this->supports($file)) {
            $this->applyFix($file, $tokens);
        }
    }

    public function supports(SplFileInfo $file): bool
    {
        return true;
    }

    final private function applyFix(SplFileInfo $file, Tokens $tokens): void
    {
        $phpUnitTestCaseIndicator = new AtoumTestCaseIndicator();

        foreach ($phpUnitTestCaseIndicator->findAtoumClasses($tokens) as $indices) {
            $this->applyAtoumClassFix($tokens, $indices[0], $indices[1]);
        }
    }

    private function applyAtoumClassFix(Tokens $tokens, int $startIndex, int $endIndex): void
    {
        for ($index = $endIndex - 1; $index > $startIndex; --$index) {
            if (!$this->isTestMethod($tokens, $index)) {
                continue;
            }

            $functionNameIndex = $tokens->getNextMeaningfulToken($index);
            $functionName = $tokens[$functionNameIndex]->getContent();
            $newFunctionName = $this->updateMethodCasing($functionName);

            if ($newFunctionName !== $functionName) {
                $tokens[$functionNameIndex] = new Token([T_STRING, $newFunctionName]);
            }

            $docBlockIndex = $this->getDocBlockIndex($tokens, $index);

            if ($this->isPHPDoc($tokens, $docBlockIndex)) {
                $this->updateDocBlock($tokens, $docBlockIndex);
            }
        }
    }

    final private function getDocBlockIndex(Tokens $tokens, int $index): int
    {
        $modifiers = [T_PUBLIC, T_PROTECTED, T_PRIVATE, T_FINAL, T_ABSTRACT, T_COMMENT];

        if (defined('T_ATTRIBUTE')) {
            // PHP 8.0+
            $modifiers[] = T_ATTRIBUTE;
        }

        if (defined('T_READONLY')) {
            // PHP 8.2+
            $modifiers[] = T_READONLY;
        }

        do {
            $index = $tokens->getPrevNonWhitespace($index);

            if (defined('T_ATTRIBUTE') && $tokens[$index]->isGivenKind(CT::T_ATTRIBUTE_CLOSE)) {
                $index = $tokens->getPrevTokenOfKind($index, [[T_ATTRIBUTE]]);
            }
        } while ($tokens[$index]->isGivenKind($modifiers));

        return $index;
    }

    final private function isPHPDoc(Tokens $tokens, int $index): bool
    {
        return $tokens[$index]->isGivenKind(T_DOC_COMMENT);
    }

    private function updateMethodCasing(string $functionName): string
    {
        $parts = explode('::', $functionName);

        $functionNamePart = array_pop($parts);

        $newFunctionNamePart = Utils::camelCaseToUnderscore($functionNamePart);
        $parts[] = $newFunctionNamePart;

        return implode('::', $parts);
    }

    private function isTestMethod(Tokens $tokens, int $index): bool
    {
        // Check if we are dealing with a (non-abstract, non-lambda) function
        if (!$this->isMethod($tokens, $index)) {
            return false;
        }

        // if the function name starts with test it's a test
        $functionNameIndex = $tokens->getNextMeaningfulToken($index);
        $functionName = $tokens[$functionNameIndex]->getContent();

        if (str_starts_with($functionName, 'test')) {
            return true;
        }

        $docBlockIndex = $this->getDocBlockIndex($tokens, $index);

        return $this->isPHPDoc($tokens, $docBlockIndex) && str_contains($tokens[$docBlockIndex]->getContent(), '@test'); // If the function doesn't have test in its name, and no doc block, it's not a test
    }

    private function isMethod(Tokens $tokens, int $index): bool
    {
        $tokensAnalyzer = new TokensAnalyzer($tokens);

        return $tokens[$index]->isGivenKind(T_FUNCTION) && !$tokensAnalyzer->isLambda($index);
    }

    private function updateDocBlock(Tokens $tokens, int $docBlockIndex): void
    {
        $doc = new DocBlock($tokens[$docBlockIndex]->getContent());
        $lines = $doc->getLines();

        $docBlockNeedsUpdate = false;
        for ($inc = 0; $inc < count($lines); ++$inc) {
            $lineContent = $lines[$inc]->getContent();
            if (!str_contains($lineContent, '@depends')) {
                continue;
            }

            $newLineContent = Preg::replaceCallback(
                '/(@depends\s+)(.+)(\b)/',
                function (array $matches): string {
                    return sprintf('%s%s%s', $matches[1], $this->updateMethodCasing($matches[2]), $matches[3]);
                },
                $lineContent
            );

            if ($newLineContent !== $lineContent) {
                $lines[$inc] = new Line($newLineContent);
                $docBlockNeedsUpdate = true;
            }
        }

        if ($docBlockNeedsUpdate) {
            $lines = implode('', $lines);
            $tokens[$docBlockIndex] = new Token([T_DOC_COMMENT, $lines]);
        }
    }
}

final class AtoumTestCaseIndicator
{
    public function isAtoumClass(Tokens $tokens, int $index): bool
    {
        if (!$tokens[$index]->isGivenKind(T_CLASS)) {
            throw new \LogicException(sprintf('No "T_CLASS" at given index %d, got "%s".', $index, $tokens[$index]->getName()));
        }

        $index = $tokens->getNextMeaningfulToken($index);

        if (!$tokens[$index]->isGivenKind(T_STRING)) {
            return false;
        }

        $extendsIndex = $tokens->getNextTokenOfKind($index, ['{', [T_EXTENDS]]);

        if (!$tokens[$extendsIndex]->isGivenKind(T_EXTENDS)) {
            return false;
        }

        if (Preg::match('/(?:Test|TestCase|MysqlTest|PommTest|atoum|atoum\test)$/', $tokens[$index]->getContent())) {
            return true;
        }

        while (null !== ($index = $tokens->getNextMeaningfulToken($index))) {
            if ($tokens[$index]->equals('{')) {
                break; // end of class signature
            }

            if (!$tokens[$index]->isGivenKind(T_STRING)) {
                continue; // not part of extends nor part of implements; so continue
            }

            if (
                Preg::match('/(?:Test|TestCase|MysqlTest|PommTest|atoum|atoum\test)$/', $tokens[$index]->getContent())
            ) {
                return true;
            }
        }

        return false;
    }

    public function findAtoumClasses(Tokens $tokens): iterable
    {
        for ($index = $tokens->count() - 1; $index > 0; --$index) {
            if (!$tokens[$index]->isGivenKind(T_CLASS) || !$this->isAtoumClass($tokens, $index)) {
                continue;
            }

            $startIndex = $tokens->getNextTokenOfKind($index, ['{']);

            if (null === $startIndex) {
                return;
            }

            $endIndex = $tokens->findBlockEnd(Tokens::BLOCK_TYPE_CURLY_BRACE, $startIndex);

            yield [$startIndex, $endIndex];
        }
    }
}

$finder = (new PhpCsFixer\Finder())->in(['config', 'src', 'tests']);

return (new PhpCsFixer\Config())
    ->registerCustomFixers([new PrettierPHPFixer(), new AtoumMethodCaseFixer()])
    ->setRiskyAllowed(true)
    ->setRules([
        '@Symfony' => true,
        'Prettier/php' => true,
        'Atoum/test_method_case' => true,
        'phpdoc_line_span' => [
            'const' => 'single',
            'method' => 'single',
            'property' => 'single',
        ],
        'phpdoc_to_comment' => ['ignored_tags' => ['lang']],
        'concat_space' => ['spacing' => 'one'],
    ])
    ->setFinder($finder);
