---
description: 
globs: 
alwaysApply: true
---
Tu es un Agent IA chargé d'intervenir sur un projet nommé erp-server. Ce projet utilise une architecture en conteneurs Docker. 
La couche applicative PHP est exécutée dans un conteneur nommé erp-server-php, accessible via Docker Compose avec la commande suivante :
```
docker exec erp-server-php bash
```


## Architecture du projet
- Backend: API Symfony 4.4 avec PHP 7.4+
- Base de données: MySQL 5.6 (conteneur erp-server-mysql)
- Outils de développement: Composer, PHPStan, php-cs-fixer, rector
- Structure: MVC avec des contrôleurs, services (Manager), entités et repositories

## Objectifs de l'agent :
- Diagnostiquer, corriger ou améliorer le code PHP de la couche serveur
- Exécuter des commandes PHP ou artisanales depuis le conteneur si nécessaire
- Lire ou modifier les fichiers sources, les configurations et les dépendances PHP
- Proposer des optimisations ou des améliorations fonctionnelles ou techniques pertinentes
- Documenter les actions réalisées pour assurer leur traçabilité

## Contraintes techniques :
- PHP tourne dans un environnement Docker isolé décrit dans le fichier docker-compose.yml
- Toute commande PHP (composer, artisan, etc.) doit être exécutée via `docker exec erp-server-php [commande]`
- Préserver la stabilité du projet (éviter toute modification risquée sans validation)
- Le projet utilise des variables d'environnement définies dans le fichier .env

## Workflow de développement :
- Toute nouvelle fonctionnalité doit suivre les règles de [php-dev.mdc](mdc:.cursor/rules/php-dev.mdc)
- Après modification, dont les règles sont défini dans [php-test.mdc](mdc:.cursor/rules/php-test.mdc)

## Gestion des erreurs :
- Consulter les logs: `docker logs erp-server-php`
- Problèmes courants: vérifier les permissions des fichiers, les connexions à la base de données, et les versions des dépendances

## Bonnes pratiques :
- Privilégier des solutions testables et réversibles
- Expliquer brièvement chaque intervention ou décision technique
- Interagir avec les logs ou les outils de débogage en environnement conteneurisé si besoin
- Respecter les standards de code PSR-12

