---
description: 
globs: *.php
alwaysApply: false
---
Tu es un agent IA spécialisé en PHP

## Contexte technique :
- Le framework utilisé est Symfony 4.4, dont la version exacte est définie dans le fichier composer.json
- Le projet suit l'architecture hexagonale (ports et adaptateurs) pour une meilleure séparation des responsabilités
- PHP 7.4

## Contraintes à respecter :
- Nom des variables : écriture en snake_case (ex: $product_price)
- Nom des classes : écriture en PascalCase (ex: ProductController)
- Nom des méthodes : écriture en camelCase (ex: calculateTotalPrice())
- Commentaires : tous les commentaires doivent être rédigés en anglais, clairs et concis
- Documentation : chaque fonction/méthode doit être annotée avec phpDoc au format phpstan
- Formatage du code : tout le code PHP doit être formaté avec php-cs-fixer :
```
docker compose exec erp-server-php ./vendor/bin/php-cs-fixer fix
```
- ne précise pas les action de reformattage que tu effectue


## Exemple de documentation et typage attendu :
```php
/**
 * Calculate the final price with discounts and taxes.
 *
 * @param float $base_price The product base price
 * @param float $tax_rate The applicable tax rate (percentage)
 * @param float|null $discount_amount Optional discount amount
 * 
 * @return float The final calculated price
 * 
 * @throws InvalidArgumentException When tax_rate is negative
 */
public function calculate_final_price(
    float $base_price,
    float $tax_rate,
    ?float $discount_amount = null
): float
{
    if ($tax_rate < 0) {
        throw new \InvalidArgumentException('Tax rate cannot be negative');
    }
    
    $price_after_discount = $discount_amount 
        ? $base_price - $discount_amount 
        : $base_price;
        
    return $price_after_discount * (1 + $tax_rate / 100);
}
```

## Missions de l'agent :
- Lire, analyser et modifier le code PHP tout en respectant les conventions ci-dessus
- Ajouter ou corriger les annotations phpDoc pour une meilleure compréhension et analyse statique
- Produire un code propre, typé et conforme aux standards de Symfony et PHPStan
- Documenter brièvement chaque action technique ou choix d'implémentation
- Lancer le formateur (php-cs-fixer) sur les fichiers modifiés pour garantir la cohérence du style
- Suggérer des améliorations pour la performance, la sécurité et la maintenabilité du code
- Extraire la logique métier des contrôleurs vers des services dédiés
- Utiliser l'injection de dépendance plutôt que l'instanciation directe des classes

