nelmio_api_doc:
    use_validation_groups: true
    documentation:
        servers:
            - url: https://erp.validation.aws.son-video.work/api/erp-server-validation
              description: 'Validation proxy (Remove the second /api/: https://url/api/validation/api/v1/something -> https://url/api/validation/v1/something'
            - url: https://erp.staging.aws.son-video.work/api/erp-server-staging
              description: 'Staging proxy (Remove the second /api/: https://url/api/staging/api/v1/something -> https://url/api/staging/v1/something'
            - url: https://erp.son-video.work/api/erp-server-prod
              description: 'Production proxy (Remove the second /api/: https://url/api/prod/api/v1/something -> https://url/api/prod/v1/something'
        info:
            title: ERP Server
            description: API pour l'ERP de Son-Vidéo.com
            version: 1.0.0
        components:
            securitySchemes:
                Bearer:
                    type: http
                    scheme: Bearer <token>
                    bearerFormat: JWT
        security:
            - Bearer: []
    areas: # to filter documented areas
        default:
            path_patterns:
                - ^/api/v\d+
        ezl:
            with_annotation: true
            path_patterns:
                - ^/api/v\d+
