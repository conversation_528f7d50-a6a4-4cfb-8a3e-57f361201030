# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
    param_fetcher_listener: true
    allowed_methods_listener: true
    routing_loader:
        default_format: json
    view:
        view_response_listener: 'force'
        formats:
            json: true
            xml: true
            html: false
    #    exception:
    #        codes:
    #            App\Exception\MyException: 403
    #        messages:
    #            App\Exception\MyException: Forbidden area.
    format_listener:
        rules:
            - { path: ^/api, prefer_extension: true, fallback_format: json, priorities: [json] }
            - { path: ^/internal-api, prefer_extension: true, fallback_format: json, priorities: [json] }
    zone:
        - { path: ^/api/* }
        - { path: ^/internal-api/* }
