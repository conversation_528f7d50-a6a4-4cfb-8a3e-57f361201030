# Read the documentation: https://github.com/1up-lab/OneupFlysystemBundle/tree/master/Resources/doc/index.md
oneup_flysystem:
    adapters:
        default_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/tmp'
        uploads_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/uploads'
        cilo_ftp_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/cilo/ftp'
        cilo_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/cilo/s3'
        wiser_ftp_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/wiser/ftp'
        wiser_ftp_svd_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/wiser/ftp-svd'
        wiser_backup_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/wiser/backup'
        gfk_ftp_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/gfk/ftp'

        # uncomment to test implementation directly on aws
        #        uploads_s3_adapter:
        #            awss3v3:
        #                client: 'app.s3_client'
        #                bucket: '%env(ERP_S3_DEFAULT_BUCKET_NAME)%'
        #                prefix: '%env(ERP_S3_DEFAULT_BUCKET_UPLOADS_PREFIX)%'
        #        sticker_s3_adapter:
        #            awss3v3:
        #                client: 'app.s3_client'
        #                bucket: '%env(STICKER_S3_BUCKET)%'
        #                prefix: '%env(STICKER_S3_PREFIX)%'
        #        legacy_s3_adapter:
        #            awss3v3:
        #                client: 'app.s3_client'
        #                bucket: '%env(ERP_BACKOFFICE_S3_BUCKET)%'
        #        legacy_france_express_s3_adapter:
        #            awss3v3:
        #                client: 'app.s3_client'
        #                bucket: '%env(ERP_BACKOFFICE_S3_BUCKET)%'
        #                prefix: '%env(TRANSFER_FRANCE_EXPRESS_S3_PREFIX)%'
        #        svd_statics_s3_adapter:
        #            awss3v3:
        #                client: 'app.s3_client'
        #                bucket: '%env(SVD_STATICS_S3_BUCKET_NAME)%'

        # comment corresponding keys to test implementation directly on aws
        sticker_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/sticker/s3'
        legacy_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/legacy/s3'
        legacy_france_express_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/legacy/s3'
        svd_statics_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/svd-statics/s3'
        mondial_relay_ftp_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/mondial_relay/ftp'
        mondial_relay_s3_adapter:
            local:
                directory: '%kernel.project_dir%/var/dev/mondial_relay/s3'
        group_digital_ftp_adapter:
            ftp:
                host: '%env(GROUP_DIGITAL_FTP_HOST)%'
                username: '%env(GROUP_DIGITAL_FTP_USERNAME)%'
                password: '%env(GROUP_DIGITAL_FTP_PASSWORD)%'
                ssl: '%env(bool:GROUP_DIGITAL_FTP_USE_SSL)%'
