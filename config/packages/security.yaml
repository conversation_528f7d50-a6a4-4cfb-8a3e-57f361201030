security:
    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        api_provider:
            id: 'SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\Provider\UserProvider'
        internal_api_provider:
            id: 'SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\Provider\InternalApiUserProvider'

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        api_doc:
            pattern: ^/api/doc/(default|ezl)(\.json)?$
            security: false

        test:
            pattern: ^/api/v1/test$
            security: false

        webhook:
            pattern: ^/api/v1/webhook
            security: false

        api:
            pattern: ^/api
            security: true
            stateless: true
            simple_preauth:
                authenticator: SonVideo\HalMiddlewareBundle\Infrastructure\Adapter\Security\Authenticator\AccessTokenAuthenticator
            provider: api_provider

        # Secure area for requests between SVD apps
        internal_api:
            pattern: ^/internal-api
            security: true
            stateless: true
            simple_preauth:
                authenticator: Son<PERSON>ide<PERSON>\HalMiddlewareBundle\Infrastructure\Adapter\Security\Authenticator\InternalApiAuthenticator
            provider: internal_api_provider

        main:
            anonymous: true

            # activate different ways to authenticate

            # http_basic: true
            # https://symfony.com/doc/current/security.html#a-configuring-how-your-users-will-authenticate

            # form_login: true
            # https://symfony.com/doc/current/security/form_login_setup.html

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api/doc$, roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: ^/api/doc.json$, roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: ^/api/v1/webhook/hasura$, roles: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
        - { path: ^/api/v1/carrier/eligible-shipment-methods, roles: [ ROLE_USER, ROLE_EASYLOUNGE ] }
        - { path: ^/api/v1/accouting/payment, roles: [ ROLE_USER, ROLE_EASYLOUNGE, ROLE_MARKETPLACE_CONNECTOR ] }
        - { path: ^/api/v1/customer-order, roles: [ ROLE_USER, ROLE_EASYLOUNGE, ROLE_MARKETPLACE_CONNECTOR ] }
        - { path: ^/api/v1/customer-order/[0-9]+/tracking-infos$, roles: [ ROLE_USER, ROLE_EASYLOUNGE, ROLE_MARKETPLACE_CONNECTOR ] }
        - { path: ^/api, roles: [ ROLE_USER ] }
        - { path: ^/internal-api, roles: [ ROLE_INTERNAL_API ] }
