framework:
    messenger:
        transports:
            erp-product:
                dsn: 'sns+sqs://topic=%env(SERVER_ENV)%-erp-product.fifo'
                retry_strategy:
                    service: App\Messenger\Retry\NoRetryStrategy
            erp-price:
                dsn: 'sns+sqs://topic=%env(SERVER_ENV)%-erp-price.fifo'
                retry_strategy:
                    service: App\Messenger\Retry\NoRetryStrategy
            erp-product-stock:
                dsn: 'sns+sqs://topic=%env(SERVER_ENV)%-erp-product-stock.fifo'
                retry_strategy:
                    service: App\Messenger\Retry\NoRetryStrategy
            erp-category:
                dsn: 'sns+sqs://topic=%env(SERVER_ENV)%-erp-category.fifo'
                retry_strategy:
                    service: App\Messenger\Retry\NoRetryStrategy

        routing: '%messenger_routing%'
