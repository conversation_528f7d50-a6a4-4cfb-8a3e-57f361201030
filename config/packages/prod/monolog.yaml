monolog:
    handlers:
        # The application is scaled in one or more docker containers
        # Therefore file logging do not serve any purpose as the logs are purged each new release
        # Following <PERSON> recommendation, we log errors in stderr only. On his side it will be sent to Kibana
        # We also keep the Sentry handlers which will have the errors below error level (see config comments below)
        # Do no trigger until level is at least error and log only in stderr
        main:
            type: fingers_crossed
            action_level: error
            handler: nested

        nested:
            type: stream
            path: 'php://stderr'
            level: error

        # Buffer all logs until an error has been reached
        buffered:
            type: buffer
            level: error
            handler: sentry

        # Message below error levels should be registered as breadcrumb in sentry
        finger_crossed:
            type: fingers_crossed
            level: error
            handler: sentry_breadcrumb

        # Sentry logging handler
        sentry:
            type: service
            id: App\Sentry\MonologHandler
            channels: [ '!sentry', '!pheanstalk', '!event', '!security', '!request' ]

        sentry_breadcrumb:
            type: service
            id: App\Sentry\MonologBreadcrumbHandler
            channels: [ '!sentry', '!pheanstalk', '!event', '!security', '!request', '!pheanstalk', '!php' ]

        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        #firephp:
        #    type: firephp
        #    level: info
        #chromephp:
        #    type: chromephp
        #    level: info
        console:
            type: console
            process_psr_3_messages: false
            channels: [ '!event', '!doctrine', '!console', '!pheanstalk', '!php' ]
