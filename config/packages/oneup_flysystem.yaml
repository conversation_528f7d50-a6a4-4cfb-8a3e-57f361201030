# Read the documentation: https://github.com/1up-lab/OneupFlysystemBundle/tree/master/Resources/doc/index.md
oneup_flysystem:
    adapters:
        # The local adapter SHOULD NOT be overridden
        # It's going to be used by processes needing full path to given file (example: CUPS)
        # It also should not be used for string data that need to be available for more than one request
        # because the file might be stored in a non-shared directory in a docker instance
        local_adapter:
            local:
                directory: '%kernel.project_dir%/var/tmp'
        # All these adapters need to be redefined in each {env}/oneup_flysystem.yaml
        default_adapter: ~
        uploads_s3_adapter: ~
        cilo_ftp_adapter: ~
        cilo_s3_adapter: ~
        wiser_ftp_svd_adapter: ~
        wiser_ftp_adapter: ~
        wiser_backup_adapter: ~
        sticker_s3_adapter: ~
        legacy_s3_adapter: ~
        svd_statics_s3_adapter: ~
        group_digital_ftp_adapter: ~

    filesystems:
        local_filesystem:
            adapter: local_adapter
            alias: League\Flysystem\Filesystem
            mount: local_filesystem
        uploads_s3_filesystem:
            adapter: uploads_s3_adapter
            alias: Upload\Filesystem\S3
            mount: uploads_s3_filesystem
        default_filesystem:
            adapter: default_adapter
            alias: League\Flysystem\Filesystem
            mount: default_filesystem
        cilo_ftp_filesystem:
            adapter: cilo_ftp_adapter
            alias: Cilo\Filesystem\Ftp
            mount: cilo_ftp_filesystem
        cilo_s3_filesystem:
            adapter: cilo_s3_adapter
            alias: Cilo\Filesystem\S3
            mount: cilo_s3_filesystem
        wiser_ftp_filesystem:
            adapter: wiser_ftp_adapter
            alias: Wiser\Filesystem\Ftp
            mount: wiser_ftp_filesystem
        wiser_ftp_svd_filesystem:
            adapter: wiser_ftp_svd_adapter
            alias: Wiser\Filesystem\FtpSvd
            mount: wiser_ftp_svd_filesystem
        wiser_backup_filesystem:
            adapter: wiser_backup_adapter
            alias: Wiser\Filesystem\Backup
            mount: wiser_backup_filesystem
        sticker_filesystem:
            adapter: sticker_s3_adapter
            alias: Sticker\Filesystem\S3
            mount: sticker_filesystem
        legacy_filesystem:
            adapter: legacy_s3_adapter
            alias: Legacy\Filesystem\S3
            mount: legacy_filesystem
        legacy_france_express_filesystem:
            adapter: legacy_france_express_s3_adapter
            alias: FranceExpress\Filesystem\S3
            mount: legacy_france_express_filesystem
        svd_statics_filesystem:
            adapter: svd_statics_s3_adapter
            alias: Legacy\Filesystem\S3
            mount: svd_statics_filesystem
        mondialrelay_ftp_filesystem:
            adapter: mondial_relay_ftp_adapter
            alias: MondialRelay\Filesystem\FTP
            mount: mondial_relay_ftp_filesystem
        mondialrelay_s3_filesystem:
            adapter: mondial_relay_s3_adapter
            alias: MondialRelay\Filesystem\S3
            mount: mondial_relay_s3_filesystem
        gfk_ftp_filesystem:
            adapter: gfk_ftp_adapter
            alias: GFK\Filesystem\Ftp
            mount: gfk_ftp_filesystem
        group_digital_ftp_filesystem:
            adapter: group_digital_ftp_adapter
            alias: GroupDigital\Filesystem\Ftp
            mount: group_digital_ftp_filesystem
