parameters:
    # Adds a fallback DATABASE_URL if the env var is not set.
    # This allows you to run cache:warmup even if your
    # environment variables are not available yet.
    # You should not need to change this value.
    env(DATABASE_URL): ''
    env(DATABASE_MYSQL_URL): ''
    env(DATABASE_MYSQL_READONLY_URL): ''

doctrine:
    dbal:
        connections:
            # PG ERP
            pg_pdo:
                charset: UTF8
                url: '%env(resolve:DATABASE_URL)%'

            pg_data_warehouse:
                charset: UTF8
                server_version: 9.6
                url: '%env(resolve:DATABASE_DATA_WAREHOUSE_URL)%'

            # Mysql connections
            legacy:
                driver: 'pdo_mysql'
                server_version: 5.7.31
                charset: UTF8
                url: '%env(resolve:DATABASE_LEGACY_URL)%'
            legacy_readonly:
                driver: 'pdo_mysql'
                server_version: 5.7.31
                charset: UTF8
                url: '%env(resolve:DATABASE_LEGACY_READONLY_URL)%'
