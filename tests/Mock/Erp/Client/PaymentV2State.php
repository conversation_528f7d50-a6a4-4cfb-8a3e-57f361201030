<?php

namespace App\Tests\Mock\Erp\Client;

use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\Payment\Manager\PaymentV2StateInterface;

class PaymentV2State implements PaymentV2StateInterface
{
    public function isValidWithProvidedOrderContext(
        CustomerOrderCreationContextDto $context_entity
    ): CustomerOrderCreationContextDto {
        return $context_entity;
    }

    public function isValidWithExistingCustomerOrder(
        CustomerOrderPaymentCreationRequestDto $request_context,
        CustomerOrderBasicInfo $customer_order_basic_info
    ): CustomerOrderPaymentCreationRequestDto {
        return $request_context;
    }

    public function canHandle(string $payment_method_code, int $customer_id): bool
    {
        return true;
    }
}
