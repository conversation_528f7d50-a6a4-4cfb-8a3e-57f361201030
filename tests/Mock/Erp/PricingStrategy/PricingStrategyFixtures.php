<?php

namespace App\Tests\Mock\Erp\PricingStrategy;

class PricingStrategyFixtures
{
    public const PRICING_STRATEGY_OK = [
        'name' => 'test',
        'starts_at' => '2024-01-01 10:00:00',
        'ends_at' => '2024-01-01 11:00:00',
        'activation_status' => 'CREATED',
        'weekdays_increment_amount' => 0,
        'weekdays_min_margin_rate' => 10,
        'weekend_increment_amount' => 0,
        'weekend_min_margin_rate' => 10,
        'competitors' => [],
        'sales_channels' => [],
    ];
    public const PRICING_STRATEGY_WRONG_STATUS = [
        'name' => 'test',
        'starts_at' => '2024-01-01 10:00:00',
        'ends_at' => '2024-01-01 11:00:00',
        'activation_status' => 'PROUT',
        'weekdays_increment_amount' => 0,
        'weekdays_min_margin_rate' => 10,
        'weekend_increment_amount' => 0,
        'weekend_min_margin_rate' => 10,
        'competitors' => [],
        'sales_channels' => [],
    ];
    public const PRICING_STRATEGY_NO_START_DATE = [
        'name' => 'test',
        'starts_at' => '',
        'ends_at' => '2024-01-01 11:00:00',
        'activation_status' => 'CREATED',
        'weekdays_increment_amount' => 0,
        'weekdays_min_margin_rate' => 10,
        'weekend_increment_amount' => 0,
        'weekend_min_margin_rate' => 10,
        'competitors' => [],
        'sales_channels' => [],
    ];
    public const PRICING_STRATEGY_START_AFTER_END = [
        'name' => 'test',
        'starts_at' => '2024-01-01 12:00:00',
        'ends_at' => '2024-01-01 11:00:00',
        'activation_status' => 'CREATED',
        'weekdays_increment_amount' => 0,
        'weekdays_min_margin_rate' => 10,
        'weekend_increment_amount' => 0,
        'weekend_min_margin_rate' => 10,
        'competitors' => [],
        'sales_channels' => [],
    ];
}
