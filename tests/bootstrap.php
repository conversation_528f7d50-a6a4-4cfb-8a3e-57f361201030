<?php

use DG\BypassFinals;
use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__) . '/vendor/autoload.php';

if (file_exists(dirname(__DIR__) . '/config/bootstrap.php')) {
    require dirname(__DIR__) . '/config/bootstrap.php';
} elseif (method_exists(Dotenv::class, 'load')) {
    (new Dotenv())->load(dirname(__DIR__) . '/.env.test');
}

if ($_SERVER['APP_DEBUG']) {
    umask(0000);
}

// Sometimes it does not work anymore (Message: THE ERROR HANDLER HAS CHANGED!)
// - Comment BypassFinals::enable();
// - launch 1or 2  tests and cancel (Ctrl+c)
// - Re-enable BypassFinals::enable();
BypassFinals::enable();
