<?php

namespace PHPUnit\Unit\App\Formatter\String;

use App\Formatter\String\StringFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class StringFormatterTest extends KernelTestCase
{
    /** Tests the transliterate method with various inputs. */
    public function test_transliterate(): void
    {
        // Test with normal string
        $text = StringFormatter::transliterate('toto a la plage');
        $this->assertSame('toto a la plage', $text);

        // Test with special characters
        $text = StringFormatter::transliterate(
            'ŠŒŽšœžŸ¥µÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýÿ€'
        );
        $this->assertSame('SOEZsoezY¥µÀÁÂÃÄÅAEÇÈÉÊËIIÎÏDNOOÔOÖOÙUÛÜYssàáâãäåaeçèéêëiiîïdnooôoöoùuûüyy€', $text);

        // Test with emoticons
        $text = StringFormatter::transliterate('Moravčík 🦊');
        $this->assertSame('Moravcik', $text);

        // Test with Arabic
        $text = StringFormatter::transliterate('صباح الخير');
        $this->assertSame('sbah alkhyr', $text);

        // Test with Chinese
        $text = StringFormatter::transliterate('你好');
        $this->assertSame('ni hao', $text);

        // Test with Ukrainian
        $text = StringFormatter::transliterate('Здрастуйте');
        $this->assertSame('Zdrastujte', $text);

        // Test with null
        $text = StringFormatter::transliterate(null);
        $this->assertSame('', $text);

        // Test with special characters
        $text = StringFormatter::transliterate('tototo� a la plage');
        $this->assertSame('tototo a la plage', $text);
    }
}
