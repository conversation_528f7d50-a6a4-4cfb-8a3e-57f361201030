<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Manager\ArticleWarningDispatcher;
use SonVideo\Erp\Article\Mysql\Repository\ArticleStockRepository;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Mailing\Manager\Internal\InternalEmailDispatcher;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleWarningDispatcherTest extends KernelTestCase
{
    /** @var array */
    private $email_payloads;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_with_order.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleWarningDispatcher. */
    protected function getTestedInstance(): ArticleWarningDispatcher
    {
        $email_dispatcher_mock = $this->createMock(InternalEmailDispatcher::class);
        $test_class = $this;
        $email_dispatcher_mock
            ->method('dispatch')
            ->willReturnCallback(function (array $data) use ($test_class): ?LoggableSystemEvent {
                $test_class->email_payloads[] = $data;

                return null;
            });

        return new ArticleWarningDispatcher(
            self::$container->get(SingleArticleReadRepository::class),
            self::$container->get(ArticleStockRepository::class),
            self::$container->get(TaskRepository::class),
            $email_dispatcher_mock,
            self::$container->get(LoggerInterface::class),
            'test',
            'https://erp_client/legacy',
            'https://erp_client'
        );
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the dispatch_status_alert method. */
    public function test_dispatch_status_alert(): void
    {
        $article_warning_dispatcher = $this->getTestedInstance();
        $user = self::$container->get(AccountQueryRepository::class)->getUser('gege');

        $this->email_payloads = [];
        $this->resetTasks();

        // A product with status "oui" sends nothing
        $article_warning_dispatcher->dispatchStatusAlerts(13895, $user);
        $this->assertCount(0, $this->email_payloads);

        $tasks = $this->fetchTasks();
        $this->assertIsArray($tasks);
        $this->assertCount(0, $tasks);

        $this->email_payloads = [];

        // A product with status "last" sends one email
        $article_warning_dispatcher->dispatchStatusAlerts(81123, $user);
        $this->assertCount(1, $this->email_payloads);

        $this->email_payloads = [];

        // A product in with status "yapu" sends two emails
        $article_warning_dispatcher->dispatchStatusAlerts(81078, $user);
        $this->assertCount(2, $this->email_payloads);

        $this->resetTasks();

        // A product in yapu generates tasks
        $article_warning_dispatcher->dispatchStatusAlerts(81078, $user);

        $tasks = $this->fetchTasks();
        $this->assertIsArray($tasks);
        $this->assertCount(2, $tasks);

        $task = $tasks[0];
        $this->assertEquals('2', $task['id_commande']);
        $this->assertEquals('1207', $task['id_utilisateur']);
        $this->assertEquals('29', $task['id_type']);
        $this->assertEquals('yapu / last', $task['sujet']);
        $this->assertStringContainsString(
            'La commande suivante peut ne pas être servie <a target="_blank" href="/legacy/v1/commandes/edition_commande.php?id_commande=2">2</a><br>Au moins un de ses produits est au statut YAPU.',
            $task['description']
        );
    }

    /** Fetches tasks from the database. */
    private function fetchTasks(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.TC_tache
        ORDER BY id_commande ASC
        SQL;

        return $this->getPdo()->fetchAll($sql);
    }

    /** Resets tasks in the database. */
    private function resetTasks(): int
    {
        $sql = <<<SQL
        TRUNCATE backOffice.TC_tache
        SQL;

        return $this->getPdo()->fetchAffected($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
