<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Exception\NotFoundException;
use App\Tests\Utils\File\FilesystemHelper;
use SonVideo\Erp\Article\Manager\ArticleMediaVariations;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleMediaVariationsTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleMediaVariations. */
    protected function getTestedInstance(): ArticleMediaVariations
    {
        return self::$container->get(ArticleMediaVariations::class);
    }

    /** Tests the format_image_payload method. */
    public function test_format_image_payload(): void
    {
        $article_id = 123;
        $large_image_path = '/image_1200.jpeg';
        $small_image_path = '/image_300_square.jpeg';
        $incorrect_image_path = '/incorrect_image_path.jpeg';
        $format_image_payload = $this->getTestedInstance();

        // Format a large image (size > 1200)
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', $large_image_path, $large_image_path);
        $formatted_payload = $format_image_payload->make($large_image_path);

        $this->assertEquals(
            [
                'media_variation' => [
                    'image' => [
                        'largest' => 1200,
                        'source' => $large_image_path,
                        'referential' => [
                            '95' => $large_image_path . '?p=95',
                            '140' => $large_image_path . '?p=140',
                            '180' => $large_image_path . '?p=180',
                            '300' => $large_image_path . '?p=300',
                            '600' => $large_image_path . '?p=600',
                            '900' => $large_image_path . '?p=900',
                            '1200' => $large_image_path . '?p=1200',
                            '180_square' => $large_image_path . '?p=180_square',
                            '300_square' => $large_image_path . '?p=300_square',
                            '450_square' => $large_image_path . '?p=450_square',
                        ],
                    ],
                ],
            ],
            $formatted_payload
        );

        // Format a small image (size = 300)
        self::$container->get(FilesystemHelper::class)->moveFile('svd_statics', $small_image_path, $small_image_path);
        $formatted_payload = $format_image_payload->make($small_image_path);

        $this->assertEquals(
            [
                'media_variation' => [
                    'image' => [
                        'largest' => 300,
                        'source' => $small_image_path,
                        'referential' => [
                            '95' => $small_image_path . '?p=95',
                            '140' => $small_image_path . '?p=140',
                            '180' => $small_image_path . '?p=180',
                            '300' => $small_image_path . '?p=300',
                            '180_square' => $small_image_path . '?p=180_square',
                            '300_square' => $small_image_path . '?p=300_square',
                        ],
                    ],
                ],
            ],
            $formatted_payload
        );

        // Format a non-existent image
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessageMatches(
            '/\/var\/test\/svd-statics\/s3' . preg_quote($incorrect_image_path, '/') . ' not found\./'
        );
        $format_image_payload->make($incorrect_image_path, $article_id);
    }
}
