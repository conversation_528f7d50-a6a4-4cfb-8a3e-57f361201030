<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleUnconditionalDiscountUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticleUnconditionalDiscountManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleUnconditionalDiscountManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'article/get_article_by_id_or_sku_v2.sql',
            'article/article_unconditional_discount.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleUnconditionalDiscountManager. */
    protected function getTestedInstance(): ArticleUnconditionalDiscountManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return self::$container->get(ArticleUnconditionalDiscountManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $dto = $this->getSerializer()->denormalize(
            [
                'article_id' => 72215,
                'amount' => 3,
            ],
            ArticleUnconditionalDiscountUpdateContextDto::class
        );

        // Valid unconditional discount system_events
        $manager = $this->getTestedInstance();
        $manager->update($dto);

        $system_event = $this->fetchLastSystemEventsForArticle('72215');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.unconditional_discount', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 72215}, "data": {"amount": {"new": 3, "old": 20}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Tests the update method with no unconditional discount. */
    public function test_update_with_no_unconditional_discount(): void
    {
        $dto = $this->getSerializer()->denormalize(
            [
                'article_id' => 81078,
                'amount' => '',
            ],
            ArticleUnconditionalDiscountUpdateContextDto::class
        );

        // Valid unconditional discount system_events
        $manager = $this->getTestedInstance();
        $manager->update($dto);

        $system_event = $this->fetchLastSystemEventsForArticle('81078');
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.unconditional_discount', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"article": 81078}, "data": {"amount": {"new": 3, "old": 0}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Fetches the last system events for a given article ID. */
    private function fetchLastSystemEventsForArticle(string $article_id): array
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.system_event
        WHERE JSON_CONTAINS(payload, :article_id, '$._rel.article');
        LIMIT 1
        MYSQL;

        return $this->getPdo()->fetchOne($sql, ['article_id' => $article_id]);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
