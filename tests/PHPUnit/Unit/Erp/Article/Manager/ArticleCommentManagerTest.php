<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Manager\ArticleCommentManager;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleCommentManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_comment.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleCommentManager. */
    protected function getTestedInstance(): ArticleCommentManager
    {
        return self::$container->get(ArticleCommentManager::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $article_comment_manager = $this->getTestedInstance();

        // Update comment and log event
        $article_comment_manager->update(81078, 2, ['is_active' => false], $this->getUser());

        $system_event = $this->fetchLastSystemEvents(81078);
        $this->assertIsArray($system_event);
        $this->assertEquals('article.update.comment', $system_event['name']);

        $payload = json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR);
        $this->assertEquals(['is_active' => ['new' => false, 'old' => true]], $payload['data']);
        $this->assertEquals(['article' => 81078, 'article_comment' => 2], $payload['_rel']);
        $this->assertEquals(
            [
                'user_id' => 1,
                'lastname' => 'Admin',
                'username' => 'admin',
                'firstname' => 'Seigneur',
            ],
            $payload['meta']['updated_by']
        );

        $comment = $this->fetchArticleComment(2);
        $this->assertEquals('0', $comment['is_active']);
    }

    /** Tests the create method. */
    public function test_create(): void
    {
        $article_comment_manager = $this->getTestedInstance();

        // Create comment and log event
        $article_comment_manager->create(81123, ['message' => 'LDLC: 279.99', 'type' => 'general'], $this->getUser());

        $system_event = $this->fetchLastSystemEvents(81123);
        $this->assertIsArray($system_event);
        $this->assertEquals('article.create.comment', $system_event['name']);

        $event_payload = json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR);
        $this->assertEquals('LDLC: 279.99', $event_payload['data']['message']);
        $this->assertEquals(81123, $event_payload['_rel']['article']);
        $this->assertIsInt($event_payload['_rel']['article_comment']);

        $this->assertEquals(
            [
                'article_id' => 81123,
                'message' => 'LDLC: 279.99',
                'created_by' => 1,
                'type' => 'general',
            ],
            $event_payload['data']
        );

        $this->assertEquals(
            [
                'user_id' => 1,
                'lastname' => 'Admin',
                'username' => 'admin',
                'firstname' => 'Seigneur',
            ],
            $event_payload['meta']['created_by']
        );

        $comment = $this->fetchLastArticleComment();
        $this->assertEquals('81123', $comment['id_produit']);
        $this->assertEquals('LDLC: 279.99', $comment['commentaire']);
        $this->assertEquals('1', $comment['user_id']);
        $this->assertEquals('general', $comment['type']);
    }

    /** Gets the admin user for testing. */
    private function getUser(): UserEntity
    {
        return self::$container->get(AccountQueryRepository::class)->getUser('admin');
    }

    /** Fetches the last system events for a given main ID. */
    private function fetchLastSystemEvents(int $main_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        WHERE main_id = :main_id
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql, ['main_id' => $main_id]);
    }

    /** Fetches an article comment by ID. */
    private function fetchArticleComment(int $article_comment_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.BO_PDT_ART_commentaire
        WHERE id = :article_comment_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['article_comment_id' => $article_comment_id]);
    }

    /** Fetches the last article comment. */
    private function fetchLastArticleComment()
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.BO_PDT_ART_commentaire
        ORDER BY id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
