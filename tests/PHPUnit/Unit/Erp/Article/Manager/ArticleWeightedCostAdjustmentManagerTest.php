<?php

namespace PHPUnit\Unit\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Article\Dto\CreationContext\ArticleWeightedCostAdjustmentCreationContextDto;
use SonVideo\Erp\Article\Dto\UpdateContext\ArticleWeightedCostAdjustmentUpdateContextDto;
use SonVideo\Erp\Article\Manager\ArticleWeightedCostAdjustmentManager;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleWeightedCostAdjustmentManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'article/article_weighted_cost_adjustment.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArticleWeightedCostAdjustmentManager. */
    protected function getTestedInstance(): ArticleWeightedCostAdjustmentManager
    {
        return self::$container->get(ArticleWeightedCostAdjustmentManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the create method. */
    public function test_create(): void
    {
        $article_weighted_cost_adjustment_manager = $this->getTestedInstance();

        // Log event correctly when created
        $weighted_cost_adjustment = $this->getSerializer()->denormalize(
            [
                'article_id' => 81123,
                'type' => 'devaluation',
                'amount' => 50,
                'meta' => [
                    'commentaire' => 'je commente beaucoup',
                    'date_range' => [
                        'from' => '2023-05-10',
                        'to' => '2023-05-20',
                    ],
                ],
                'created_by' => 1,
            ],
            ArticleWeightedCostAdjustmentCreationContextDto::class
        );

        $article_weighted_cost_adjustment_manager->create($weighted_cost_adjustment, $this->getUser());
        $system_event = $this->fetchLastSystemEvents();

        $this->assertIsArray($system_event);
        $this->assertEquals('article.create.weighted_cost_adjustment', $system_event['name']);

        $data = json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR)['data'];
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('meta', $data);
        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('created_by', $data);
        $this->assertArrayHasKey('amount', $data);
        $this->assertArrayHasKey('created_at', $data);
        $this->assertArrayHasKey('updated_at', $data);

        $this->assertEquals(
            '{"date_range": {"to": "2023-05-20", "from": "2023-05-10"}, "commentaire": "je commente beaucoup"}',
            $data['meta']
        );
        $this->assertEquals('devaluation', $data['type']);
        $this->assertEquals('50.00', $data['amount']);

        // Create adjustement with empty meta
        $weighted_cost_adjustment = $this->getSerializer()->denormalize(
            [
                'article_id' => 81123,
                'type' => 'devaluation',
                'amount' => 50,
                'meta' => [],
                'created_by' => 1,
            ],
            ArticleWeightedCostAdjustmentCreationContextDto::class
        );

        $id = $article_weighted_cost_adjustment_manager->create($weighted_cost_adjustment, $this->getUser());
        $weighted_cost_adjustment = $this->findAdjustment($id);

        $this->assertIsArray($weighted_cost_adjustment);
        $this->assertEquals('{}', $weighted_cost_adjustment['meta']);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        // Log event correctly when updated
        $weighted_cost_adjustment = $this->getSerializer()->denormalize(
            [
                'id' => 1,
                'article_id' => 81123,
                'meta' => [
                    'date_range' => [
                        'from' => '2023-06-10',
                        'to' => '2023-06-20',
                    ],
                    'quoi' => 'coubeh',
                    'toto' => 'tata',
                ],
            ],
            ArticleWeightedCostAdjustmentUpdateContextDto::class
        );

        $this->getTestedInstance()->update($weighted_cost_adjustment, $this->getUser());
        $system_event = $this->fetchLastSystemEvents();

        $this->assertEquals('article.update.weighted_cost_adjustment', $system_event['name']);
        $this->assertEquals(
            [
                'quoi' => ['new' => 'coubeh', 'old' => ''],
                'date_range' => [
                    'new' => ['to' => '2023-06-20', 'from' => '2023-06-10'],
                    'old' => ['to' => '2023-05-20', 'from' => '2023-05-11'],
                ],
                'commentaire' => ['new' => '', 'old' => 'bonsoir'],
            ],
            json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR)['data']
        );
    }

    /** Gets the admin user for testing. */
    private function getUser(): UserEntity
    {
        return self::$container->get(AccountQueryRepository::class)->getUser('admin');
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Finds an adjustment by ID. */
    private function findAdjustment(int $id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.BO_STK_weighted_cost_adjustment
        WHERE id = :id;
        SQL;

        return $this->getPdo()->fetchOne($sql, ['id' => $id]);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
