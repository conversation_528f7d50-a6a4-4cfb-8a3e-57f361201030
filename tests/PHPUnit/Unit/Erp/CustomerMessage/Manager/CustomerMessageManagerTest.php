<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerMessage\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use PommProject\Foundation\Session\Connection;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\CustomerMessage\Manager\CustomerMessageManager;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerMessageManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'customer/customer_messages.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();

        $sql = <<<PGSQL
        INSERT INTO system.parameter (name, value, description)
        VALUES ('email.mailjet_template_id.contact', '3469317', 'Template id on mailjet')
        ON CONFLICT DO NOTHING;
        PGSQL;

        $this->getPgConnexion()->executeAnonymousQuery($sql);
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): CustomerMessageManager
    {
        return self::$container->get(CustomerMessageManager::class);
    }

    /** Tests the reply method with an empty message. */
    public function test_reply_with_empty_message(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');
        $message_to_reply = 2;

        $tested_class = $this->getTestedInstance();

        // Throw exception when message is empty
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Message is required');
        $tested_class->reply($message_to_reply, $user, '');
    }

    /** Tests the reply method with a non-existent customer message. */
    public function test_reply_with_non_existent_customer_message(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');

        $tested_class = $this->getTestedInstance();

        // Throw exception when customer message does not exist
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer message is not found');
        $tested_class->reply(666, $user, 'Coucou');
    }

    /** Tests the reply method with a valid message. */
    public function test_reply_with_valid_message(): void
    {
        $user = self::$container->get(AccountQueryRepository::class)->getUser('admin');
        $message_to_reply = 2;

        $tested_class = $this->getTestedInstance();

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            '{
                "success": true,
                "reason": "OK",
                "data": {
                    "Messages": [
                        {
                            "Status": "success",
                            "CustomID": "",
                            "To": [
                                {
                                    "Email": "<EMAIL>",
                                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                                    "MessageID": 576460762539150950,
                                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                                }
                            ],
                            "Cc": [],
                            "Bcc": []
                        }
                    ]
                },
                "_in_test_mode": false
            }'
        );

        // It persist the customer message reply in database
        $this->assertEquals(0, count($this->fetchCustomerMessageReply($message_to_reply)));

        $tested_class->reply($message_to_reply, $user, 'Coucou voici ma réponse');

        $replies = $this->fetchCustomerMessageReply($message_to_reply);
        $this->assertEquals(1, count($replies));

        $reply = $replies[0];
        $this->assertEquals($message_to_reply, (int) $reply['parent_id']);
        $this->assertEquals('Re: Une question home-cinéma ?', $reply['sujet']);
        $this->assertEquals("Coucou voici ma réponse\nLe roi du système", $reply['corps']);
        $this->assertEquals('svd', $reply['origine']);
        $this->assertEquals('<EMAIL>', $reply['expediteur']);
        $this->assertEquals('<EMAIL>', $reply['destinataire']);
        $this->assertEquals(16, (int) $reply['id_type']);
        $this->assertEquals(2, (int) $reply['id_prospect']);
    }

    /** Gets the PostgreSQL connection. */
    private function getPgConnexion(): Connection
    {
        return self::$container
            ->get(ParameterModel::class)
            ->getSession()
            ->getConnection();
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches customer message replies by parent ID. */
    protected function fetchCustomerMessageReply(int $customer_message_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.PCT_message_prospect
        WHERE parent_id = :parent_id
        SQL;

        return $this->getPdo()->fetchAll($sql, ['parent_id' => $customer_message_id]);
    }
}
