<?php

namespace PHPUnit\Unit\Erp\Carrier\Manager\Sticker\MondialRelay;

use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Carrier\Manager\Sticker\MondialRelay\RequestPayload;
use SonVideo\Erp\Carrier\Mysql\Repository\StickerReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class RequestPayloadTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'wms/sticker/generate-and-print-mondial-relay.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the request payload. */
    public function test_request_payload(): void
    {
        $shipmentEntity = $this->getMockedRepository()->load(4653679, null);
        $requestPayload = new RequestPayload($shipmentEntity);
        $payload = $requestPayload->make();

        // Test: Set product description in content1 parameter
        $this->assertEquals(
            [
                'ModeCol' => 'CCC',
                'ModeLiv' => '24R',
                'NDossier' => '4653679',
                'NClient' => '1878297',
                'Expe_Langage' => 'FR',
                'Expe_Ad1' => 'SON-VIDEO DISTRIBUTION',
                'Expe_Ad3' => '314 rue du prof Paul Milliez',
                'Expe_Ville' => 'CHAMPIGNY SUR MARNE',
                'Expe_CP' => '94500',
                'Expe_Pays' => 'FR',
                'Expe_Tel1' => '0155091839',
                'Dest_Langage' => 'FR',
                'Dest_Ad1' => 'Guy Liguili',
                'Dest_Ad2' => 'le grand portail gris',
                'Dest_Ad3' => '38 rue de la ville en bois',
                'Dest_Ad4' => 'au fond de la cours à gauche',
                'Dest_Ville' => 'NANTES',
                'Dest_CP' => '44100',
                'Dest_Pays' => 'FR',
                'Dest_Tel1' => '0155092043',
                'Dest_Tel2' => '0155092043',
                'Dest_Mail' => '<EMAIL>',
                'Poids' => 460.0,
                'NbColis' => 1,
                'CRT_Valeur' => 0,
                'COL_Rel_Pays' => 'XX',
                'COL_Rel' => null,
                'LIV_Rel_Pays' => 'FR',
                'LIV_Rel' => null,
            ],
            $payload
        );

        // Test: Without optional data
        $shipmentEntity->sender->address_line2 = null;
        $shipmentEntity->recipient->address_line2 = null;
        $shipmentEntity->recipient->address_line3 = null;
        $shipmentEntity->recipient->address_line4 = null;
        $shipmentEntity->recipient->phone = null;
        $shipmentEntity->recipient->mobile = null;
        $shipmentEntity->recipient->email = null;
        $requestPayload = new RequestPayload($shipmentEntity);
        $payload = $requestPayload->make();

        $this->assertEquals(
            [
                'ModeCol' => 'CCC',
                'ModeLiv' => '24R',
                'NDossier' => '4653679',
                'NClient' => '1878297',
                'Expe_Langage' => 'FR',
                'Expe_Ad1' => 'SON-VIDEO DISTRIBUTION',
                'Expe_Ad3' => '314 rue du prof Paul Milliez',
                'Expe_Ville' => 'CHAMPIGNY SUR MARNE',
                'Expe_CP' => '94500',
                'Expe_Pays' => 'FR',
                'Expe_Tel1' => '0155091839',
                'Dest_Langage' => 'FR',
                'Dest_Ad1' => 'Guy Liguili',
                'Dest_Ad3' => '38 rue de la ville en bois',
                'Dest_Ville' => 'NANTES',
                'Dest_CP' => '44100',
                'Dest_Pays' => 'FR',
                'Poids' => 460.0,
                'NbColis' => 1,
                'CRT_Valeur' => 0,
                'COL_Rel_Pays' => 'XX',
                'COL_Rel' => null,
                'LIV_Rel_Pays' => 'FR',
                'LIV_Rel' => null,
            ],
            $payload
        );
    }

    /** Gets the mocked repository. */
    private function getMockedRepository(): StickerReadRepository
    {
        return self::$container->get(StickerReadRepository::class);
    }
}
