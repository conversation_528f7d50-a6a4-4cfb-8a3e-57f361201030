<?php

namespace PHPUnit\Unit\Erp\AntiFraud\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudFormattedReason;
use SonVideo\Erp\AntiFraud\Entity\ArticleForAntiFraudModule;
use SonVideo\Erp\AntiFraud\Manager\ArticleAntiFraudStatusChecker;
use SonVideo\Erp\Referential\AntiFraudReason;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArticleAntiFraudStatusCheckerTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests that articles are loaded correctly. */
    public function test_it_should_load_articles_correctly(): void
    {
        $article_keys = [123, '456', 'irrelevant_associative_key' => 'ASKU', 'ANOTHERSKU'];

        $article_read_repository = $this->createMock(ArticleReadRepository::class);
        $parameter_model = $this->createMock(ParameterModel::class);

        /** @var QueryBuilder $query_builder */
        $query_builder = self::$container->get(QueryBuilder::class);

        $pager = $this->createMock(Pager::class);
        $pager
            ->expects($this->once())
            ->method('getResults')
            ->willReturn([]);

        $article_read_repository
            ->expects($this->once())
            ->method('findAllPaginated')
            ->willReturn($pager);

        $tested_instance = new ArticleAntiFraudStatusChecker(
            $article_read_repository,
            $parameter_model,
            $query_builder
        );
        $tested_instance->getExclusionStatusesFor($article_keys);

        $this->assertStringContainsString(
            '(a.id_produit IN (:product_id_0, :product_id_1) OR p.reference IN (:sku_0, :sku_1))',
            $query_builder->getWhere()
        );

        $this->assertEquals(
            [
                'product_id_0' => 123,
                'product_id_1' => 456,
                'sku_0' => 'ASKU',
                'sku_1' => 'ANOTHERSKU',
            ],
            $query_builder->getWhereParameters()
        );
    }

    /** Tests that results are dispatched correctly for the exclusion statuses list. */
    public function test_it_should_dispatch_result_correctly_for_the_exclusion_statuses_list(): void
    {
        $article_read_repository = $this->createMock(ArticleReadRepository::class);

        $collection_iterator = $this->createMock(CollectionIterator::class);
        $collection_iterator
            ->expects($this->once())
            ->method('extract')
            ->willReturn([['value' => '{}']]);

        $parameter_model = $this->createMock(ParameterModel::class);
        $parameter_model
            ->expects($this->once())
            ->method('getParameter')
            ->willReturn($collection_iterator);

        /** @var QueryBuilder $query_builder */
        $query_builder = self::$container->get(QueryBuilder::class);

        $pager = $this->createMock(Pager::class);
        $pager
            ->expects($this->once())
            ->method('getResults')
            ->willReturn([
                [
                    'product_id' => 123,
                    'sku' => 'FOO',
                    'brand_id' => 1,
                    'subcategory_id' => 1,
                    'selling_price' => '11.1',
                ],
                [
                    'product_id' => 456,
                    'sku' => 'SKU',
                    'brand_id' => 1,
                    'subcategory_id' => 1,
                    'selling_price' => '22.2',
                ],
            ]);

        $article_read_repository
            ->expects($this->once())
            ->method('findAllPaginated')
            ->willReturn($pager);

        $tested_instance = new ArticleAntiFraudStatusChecker(
            $article_read_repository,
            $parameter_model,
            $query_builder
        );
        $result = $tested_instance->getExclusionStatusesFor([123, 'SKU']);

        // Not product excluded
        $this->assertEquals(
            [
                123 => false,
                'SKU' => false,
            ],
            $result
        );
    }

    /** Helper method to check with a specific rule. */
    private function checkWithRule(string $rule): ?AntiFraudFormattedReason
    {
        $collection_iterator = $this->createMock(CollectionIterator::class);
        $collection_iterator
            ->expects($this->once())
            ->method('extract')
            ->willReturn([['value' => $rule]]);

        $parameter_model = $this->createMock(ParameterModel::class);
        $parameter_model
            ->expects($this->once())
            ->method('getParameter')
            ->willReturn($collection_iterator);

        $tested_instance = new ArticleAntiFraudStatusChecker(
            self::$container->get(ArticleReadRepository::class),
            $parameter_model,
            self::$container->get(QueryBuilder::class)
        );

        return $tested_instance->check(
            ArticleForAntiFraudModule::from([
                'product_id' => 123,
                'sku' => 'BAR',
                'brand_id' => 1,
                'subcategory_id' => 1,
                'selling_price' => '100',
            ])
        );
    }

    /** Tests checking against brand rules. */
    public function test_it_should_check_against_brand_rules(): void
    {
        // rule not applicable
        $this->assertNull($this->checkWithRule('[{"type": "baz"}]'));

        // not applicable to brand_id
        $this->assertNull($this->checkWithRule('[{"type": "brand", "brand_id": 666}]'));

        // applicable to brand_id without a threshold
        $result = $this->checkWithRule('[{"type": "brand", "brand_id": 1}]');
        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_BRAND, $result->getDetails()[0]['key']);
        $this->assertEquals(['brand_id' => 1], $result->getDetails()[0]['details']);

        // applicable to brand_id with a threshold
        $result = $this->checkWithRule('[{"type": "brand", "brand_id": 1, "min_price": 2}]');
        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_BRAND, $result->getDetails()[0]['key']);
        $this->assertEquals(
            [
                'brand_id' => 1,
                'article_selling_price' => 100.0,
                'min_price' => 2,
            ],
            $result->getDetails()[0]['details']
        );

        // NOT applicable to brand_id with a threshold
        $result = $this->checkWithRule('[{"type": "brand", "brand_id": 1, "min_price": 101}]');
        $this->assertNull($result);
    }

    /** Tests checking against subcategory rules. */
    public function test_it_should_check_against_subcategory_rules(): void
    {
        // rule not applicable
        $this->assertNull($this->checkWithRule('[{"type": "baz"}]'));

        // not applicable to subcategory_id
        $this->assertNull($this->checkWithRule('[{"type": "subcategory", "subcategory_id": 666}]'));

        // applicable to subcategory_id without a threshold
        $result = $this->checkWithRule('[{"type": "subcategory", "subcategory_id": 1}]');
        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_SUBCATEGORY, $result->getDetails()[0]['key']);
        $this->assertEquals(['subcategory_id' => 1], $result->getDetails()[0]['details']);

        // applicable to subcategory_id with a threshold
        $result = $this->checkWithRule('[{"type": "subcategory", "subcategory_id": 1, "min_price": 2}]');
        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_SUBCATEGORY, $result->getDetails()[0]['key']);
        $this->assertEquals(
            [
                'subcategory_id' => 1,
                'article_selling_price' => 100.0,
                'min_price' => 2,
            ],
            $result->getDetails()[0]['details']
        );

        // NOT applicable to subcategory_id with a threshold
        $result = $this->checkWithRule('[{"type": "subcategory", "subcategory_id": 1, "min_price": 101}]');
        $this->assertNull($result);
    }

    /** Tests checking against SKU rules. */
    public function test_it_should_check_against_sku_rules(): void
    {
        // rule not applicable
        $this->assertNull($this->checkWithRule('[{"type": "baz"}]'));

        // not applicable to subcategory_id
        $this->assertNull($this->checkWithRule('[{"type": "sku", "skus": ["toto"]}]'));

        // applicable to subcategory_id without a threshold
        $result = $this->checkWithRule('[{"type": "sku", "skus": ["BAR"]}]');
        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_SKU, $result->getDetails()[0]['key']);
    }

    /** Tests merging multiple rejection reasons. */
    public function test_it_should_merge_multiple_rejection_reasons(): void
    {
        // applicable to subcategory_id without a threshold
        $result = $this->checkWithRule(
            '[{"type": "subcategory", "subcategory_id": 1, "min_price": 2},{"type": "sku", "skus": ["BAR"]}]'
        );

        $this->assertInstanceOf(AntiFraudFormattedReason::class, $result);
        $this->assertEquals('BAR', $result->getDetails()[0]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_SUBCATEGORY, $result->getDetails()[0]['key']);
        $this->assertEquals(
            [
                'subcategory_id' => 1,
                'article_selling_price' => 100.0,
                'min_price' => 2,
            ],
            $result->getDetails()[0]['details']
        );

        $this->assertEquals('BAR', $result->getDetails()[1]['sku']);
        $this->assertEquals(AntiFraudReason::INVALID_SKU, $result->getDetails()[1]['key']);
    }
}
