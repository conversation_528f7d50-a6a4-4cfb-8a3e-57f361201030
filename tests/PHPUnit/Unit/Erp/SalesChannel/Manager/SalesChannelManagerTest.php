<?php

namespace PHPUnit\Unit\Erp\SalesChannel\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\SalesChannel\Dto\SalesChannelContextDto;
use SonVideo\Erp\SalesChannel\Manager\SalesChannelManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SalesChannelManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SalesChannelManager
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return self::$container->get(SalesChannelManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update method. */
    public function test_update_success(): void
    {
        $sale_channel = $this->getTestedInstance();
        $dto = $this->getSerializer()->denormalize(
            [
                'sales_channel_id' => 5,
                'minimum_margin_rate' => 30,
                'minimum_available_quantity' => 2,
                'average_commission_rate' => 0.22,
            ],
            SalesChannelContextDto::class
        );

        // Update sales channel success
        $updated = $sale_channel->update($dto);
        $this->assertEquals(1, $updated);
    }

    /** Tests the update method with an unknown sales channel. */
    public function test_update_with_unknown_sales_channel(): void
    {
        $sale_channel = $this->getTestedInstance();
        $dto = $this->getSerializer()->denormalize(
            [
                'sales_channel_id' => 666,
                'minimum_margin_rate' => 30,
                'minimum_available_quantity' => 2,
                'average_commission_rate' => 0.22,
            ],
            SalesChannelContextDto::class
        );

        // Unknown sales channel throw exception
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('No sales channel found with id 666.');
        $sale_channel->update($dto);
    }
}
