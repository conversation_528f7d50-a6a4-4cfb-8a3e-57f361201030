<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\PaymentLink;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use PHPUnit\Framework\MockObject\MockObject;
use PommProject\ModelManager\Model\CollectionIterator;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use SonVideo\Erp\Mailing\Manager\PaymentLink\PaymentLinkEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PaymentLinkEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): PaymentLinkEmailDispatcher
    {
        $tested_class = self::$container->get(PaymentLinkEmailDispatcher::class);
        $tested_class->setParameterModel($this->getParameterMock());

        return $tested_class;
    }

    public function test_dispatch_with_invalid_payload_first_case(): void
    {
        $email_dispatcher = $this->getTestedInstance();

        $invalid_request_payload = <<<JSON
        {
          "to": "bad email",
          "context": {},
          "_rel": {
            "customer": null,
            "customer_order": null
          }
        }
        JSON;

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value is not a valid email address.', $exception->getMessage());
        $this->assertStringContainsString('[from]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[context][subject]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][content]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[context][payment_link]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[_rel][customer_order]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_invalid_payload_second_case(): void
    {
        $email_dispatcher = $this->getTestedInstance();

        $invalid_request_payload = <<<JSON
        {
          "from": null,
          "to": null,
          "context": {
            "subject": null,
            "content": null,
            "payment_link": null,
            "customer_order_id": null
          },
          "_rel": {}
        }
        JSON;

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from]: This value should not be null.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][subject]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][subject]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][content]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][content]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][payment_link]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][payment_link]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order_id]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][customer_order_id]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_invalid_payload_third_case(): void
    {
        $email_dispatcher = $this->getTestedInstance();

        $invalid_request_payload = <<<JSON
        {
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>"
          },
          "to": "<EMAIL>",
          "context": {
            "subject": "",
            "content": "",
            "payment_link": "bad link",
            "customer_order_id": 0
          },
          "_rel": {}
        }
        JSON;

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString(
            '[context][subject]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][content]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][payment_link]: This value is not a valid URL.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_rel][customer]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[_rel][customer_order]: This field is missing.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload(): void
    {
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "from": {
            "name": "Son-Video.com",
            "email": "<EMAIL>"
          },
          "context": {
            "subject": "Votre lien de paiement",
            "content": "Bonjour,\\n Ces caractères sont modifiés: <>\"'&\\n",
            "payment_link": "http://example.com/",
            "customer_order_id": 789
          },
          "_rel": {
            "customer": 970481,
            "customer_order": 12345678
          },
          "_sent_by": 1300
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);

        $payload = $result->getPayload();
        $this->assertEquals(
            'Bonjour,<br> Ces caractères sont modifiés: &lt;&gt;&quot;&#39;&amp;<br>',
            $payload['meta']['context']['content']
        );
    }

    /** @return ParameterModel|MockObject */
    private function getParameterMock()
    {
        $parameter_mock = $this->createMock(ParameterModel::class);

        $parameter_mock->method('getParameter')->willReturnCallback(function (string $key) {
            if ('email.bcc' === $key) {
                return $this->getCollectionMock('[]');
            }
            if ('email.mailjet_template_id.payment_link' === $key) {
                return $this->getCollectionMock('123');
            }

            throw new \Exception('Unexpected key');
        });

        return $parameter_mock;
    }

    /** @return CollectionIterator|MockObject */
    private function getCollectionMock(string $value)
    {
        $parameter_collection = $this->createMock(CollectionIterator::class);

        $parameter_collection->method('extract')->willReturn([['value' => $value]]);

        return $parameter_collection;
    }
}
