<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\MonologTest;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderImportStatusCleaner as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderImportStatusCleanerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'customer_order/command/customer_order_import_status_checker.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
        MonologTest::initialize(self::$container->get(LoggerInterface::class));
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** Tests that no import tags are found. */
    public function test_should_not_found_any_import_tag(): void
    {
        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertTrue(MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders'));
    }

    /** Tests that the cleaner skips if the customer has not reached the minimum threshold. */
    public function test_should_skip_if_customer_has_not_reached_min_threshold(): void
    {
        // PREPARE DATA
        $sql = <<<'MYSQL'
        INSERT IGNORE INTO backOffice.commande_tag (id_commande, tag_id, meta) VALUES (:customer_order_id, 'status.import', '{}')
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.commande
        SET date_creation = NOW() - INTERVAL 9 MINUTE
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertFalse(
            MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders')
        );

        $this->assertTrue(MonologTest::handler()->hasNoticeThatContains('Min threshold not reached yet, skipping...'));
    }

    /** Tests that the cleaner skips if the customer order is not fully paid and the maximum threshold has not been reached yet. */
    public function test_should_skip_if_customer_order_is_not_fully_paid_and_max_threshold_has_not_been_reached_yet(): void
    {
        // PREPARE DATA
        $sql = <<<'MYSQL'
        INSERT IGNORE INTO backOffice.commande_tag (id_commande, tag_id, meta) VALUES (:customer_order_id, 'status.import', '{}')
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.commande
        SET date_creation = NOW() - INTERVAL 11 MINUTE
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.paiement_commande
        SET auto_statut = null
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertFalse(
            MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders')
        );

        $this->assertTrue(
            MonologTest::handler()->hasNoticeThatContains('Min threshold reached but not fully paid yet, skipping...')
        );
    }

    /** Tests that the cleaner cleans if the customer order is fully paid and has reached the minimum threshold. */
    public function test_should_clean_if_customer_order_is_fully_paid_and_has_reached_min_threshold(): void
    {
        // PREPARE DATA
        $sql = <<<'MYSQL'
        INSERT IGNORE INTO backOffice.commande_tag (id_commande, tag_id, meta) VALUES (:customer_order_id, 'status.import', '{}')
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.commande
        SET date_creation = NOW() - INTERVAL 11 MINUTE
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.paiement_commande
        SET auto_statut = 'accepte'
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertFalse(
            MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders')
        );

        $this->assertTrue(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Removed tag "%s" for customer order "%s"', 'status.import', 1724028)
            )
        );

        $this->assertFalse(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Tagged customer order "%s" with "%s"', 1724028, 'status.abandoned')
            )
        );
    }

    /** Tests that the cleaner cleans if the customer order is fully paid and has reached the maximum threshold. */
    public function test_should_clean_if_customer_order_is_fully_paid_and_has_reached_max_threshold(): void
    {
        // PREPARE DATA
        $sql = <<<'MYSQL'
        INSERT IGNORE INTO backOffice.commande_tag (id_commande, tag_id, meta) VALUES (:customer_order_id, 'status.import', '{}')
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.commande
        SET date_creation = NOW() - INTERVAL 2 HOUR - INTERVAL 1 MINUTE
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.paiement_commande
        SET auto_statut = 'accepte'
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertFalse(
            MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders')
        );

        $this->assertTrue(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Removed tag "%s" for customer order "%s"', 'status.import', 1724028)
            )
        );

        $this->assertFalse(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Tagged customer order "%s" with "%s"', 1724028, 'status.abandoned')
            )
        );
    }

    /** Tests that the cleaner cleans if the customer order is not fully paid and has reached the maximum threshold, and marks it as abandoned. */
    public function test_should_clean_if_customer_order_is_not_fully_paid_and_has_reached_max_threshold_and_mark_it_has_abandoned(): void
    {
        // PREPARE DATA
        $sql = <<<'MYSQL'
        INSERT IGNORE INTO backOffice.commande_tag (id_commande, tag_id, meta) VALUES (:customer_order_id, 'status.import', '{}')
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.commande
        SET date_creation = NOW() - INTERVAL 2 HOUR - INTERVAL 1 MINUTE
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        $sql = <<<'MYSQL'
        UPDATE backOffice.paiement_commande
        SET auto_statut = null
        WHERE id_commande = :customer_order_id
        MYSQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => 1724028]);

        // TEST
        MonologTest::handler()->clear();
        $this->getTestedInstance()->clean();

        $this->assertFalse(
            MonologTest::handler()->hasInfoThatContains('No import status found in any customer orders')
        );

        $this->assertTrue(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Removed tag "%s" for customer order "%s"', 'status.import', 1724028)
            )
        );

        $this->assertTrue(
            MonologTest::handler()->hasNoticeThatContains(
                sprintf('Tagged customer order "%s" with "%s"', 1724028, 'status.abandoned')
            )
        );
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
