<?php

namespace PHPUnit\Unit\Erp\Customer\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use SonVideo\Erp\Customer\Dto\CustomerUpdateRequestDto;
use SonVideo\Erp\Customer\Manager\CustomerUpdater;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerUpdaterTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users_backoffice.sql', 'customer/customers.sql']);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): CustomerUpdater
    {
        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);

        return new CustomerUpdater(
            self::$container->get(LegacyPdo::class),
            self::$container->get(SystemEventLogger::class),
            self::$container->get(CustomerRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(CurrentUser::class)
        );
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        $customer_updater = $this->getTestedInstance();
        $data = $this->getSerializer()->denormalize(
            [
                'customer_id' => 5,
                'type' => 'entreprise',
                'blacklist' => true,
                'encours_sfac' => 3000,
                'classification' => 'Centrale',
                'tva_number' => '18',
                'accept_marketing_emails' => true,
                'company_name' => 'pouet pouet',
                'balance_acceptance' => true,
                'atradius' => 'Pas soumis',
                'npai' => true,
                'incoterm' => 'DAT',
            ],
            CustomerUpdateRequestDto::class
        );

        // Update success
        $nb_affected = $customer_updater->update($data);
        $this->assertEquals(1, $nb_affected);

        $system_event = $this->fetchLastSystemEvents();
        $this->assertIsArray($system_event);
        $this->assertEquals('customer.update', $system_event['name']);
        $this->assertEquals(
            '{"_rel": {"customer": 5}, "data": {"npai": {"new": true, "old": false}, "type": {"new": "entreprise", "old": "particulier"}, "blacklist": {"new": true, "old": false}, "encours_sfac": {"new": 3000, "old": 0}, "cnt_numero_tva": {"new": "18", "old": "88"}, "acceptation_relicat": {"new": true, "old": false}}, "meta": {"updated_by": {"user_id": 1, "lastname": "Admin", "username": "admin", "firstname": "Seigneur"}}}',
            $system_event['payload']
        );
    }

    /** Tests the update method with a non-existent customer. */
    public function test_update_with_non_existent_customer(): void
    {
        $customer_updater = $this->getTestedInstance();
        $data = $this->getSerializer()->denormalize(
            ['customer_id' => 5555, 'type' => 'entreprise', 'blacklist' => true],
            CustomerUpdateRequestDto::class
        );

        // Update fail customer not found
        $this->expectException(NotFoundException::class);
        $this->expectExceptionMessage('Customer with id 5555 not found.');
        $customer_updater->update($data);
    }

    /** Fetches the last system events. */
    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }

    /** Gets the PDO instance. */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
