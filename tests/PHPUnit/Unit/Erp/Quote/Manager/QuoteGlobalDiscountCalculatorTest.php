<?php

namespace PHPUnit\Unit\Erp\Quote\Manager;

use App\Exception\InternalErrorException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Quote\Manager\QuoteGlobalDiscountCalculator as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class QuoteGlobalDiscountCalculatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'quote/put_quote_allocate_discount_globally.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    /** @throws \Exception */
    public function test_calculate_with_prorated_discount(): void
    {
        $all_quote_lines = $this->fetchQuoteLines(10);

        $this->assertCount(5, $all_quote_lines);
        $this->assertEquals(81078, (int) $all_quote_lines[0]->product_id);
        $this->assertEquals(-1.0, (float) $all_quote_lines[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $all_quote_lines[1]->product_id);
        $this->assertEquals(-10.34, (float) $all_quote_lines[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $all_quote_lines[2]->product_id);
        $this->assertEquals(-20.56, (float) $all_quote_lines[2]->unit_discount_amount);

        // in-between quote line section
        $this->assertNull($all_quote_lines[3]->product_id);
        $this->assertEquals(13895, (int) $all_quote_lines[4]->product_id);
        $this->assertEquals(-30.78, (float) $all_quote_lines[4]->unit_discount_amount);

        $has_discount_admin_permission = false;
        $updated_quote_line_products = $this->getTestedInstance()->calculateWith(
            10,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -350
        );

        // in-between quote line section should be filtered out
        $this->assertCount(4, $updated_quote_line_products);
        $this->assertEquals(81078, (int) $updated_quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[0]->quantity);
        $this->assertEquals(-62.39, (float) $updated_quote_line_products[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $updated_quote_line_products[1]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[1]->quantity);
        $this->assertEquals(-34.19, (float) $updated_quote_line_products[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $updated_quote_line_products[2]->product_id);
        $this->assertEquals(4, (int) $updated_quote_line_products[2]->quantity);
        $this->assertEquals(-62.39, (float) $updated_quote_line_products[2]->unit_discount_amount);
        $this->assertEquals(13895, (int) $updated_quote_line_products[3]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[3]->quantity);
        $this->assertEquals(-3.85, (float) $updated_quote_line_products[3]->unit_discount_amount);
    }

    /** @throws \Exception */
    public function test_calculate_with_no_products(): void
    {
        $all_quote_lines = $this->fetchQuoteLines(20);

        $this->assertCount(1, $all_quote_lines);

        // in-between quote line section
        $this->assertNull($all_quote_lines[0]->product_id);

        $has_discount_admin_permission = false;
        $updated_quote_line_products = $this->getTestedInstance()->calculateWith(
            20,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -100
        );

        // in-between quote line section should be filtered out
        $this->assertCount(0, $updated_quote_line_products);
    }

    /** @throws \Exception */
    public function test_calculate_with_negative_margin_products_ignored(): void
    {
        $all_quote_lines = $this->fetchQuoteLines(30);

        $this->assertCount(3, $all_quote_lines);
        $this->assertEquals(81078, (int) $all_quote_lines[0]->product_id);
        $this->assertEquals(-15, (float) $all_quote_lines[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $all_quote_lines[1]->product_id);
        $this->assertEquals(0, (float) $all_quote_lines[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $all_quote_lines[2]->product_id);
        $this->assertEquals(-20, (float) $all_quote_lines[2]->unit_discount_amount);

        $has_discount_admin_permission = false;
        $updated_quote_line_products = $this->getTestedInstance()->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -50
        );

        $this->assertCount(3, $updated_quote_line_products);
        $this->assertEquals(81078, (int) $updated_quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[1]->quantity);
        $this->assertEquals(-25, (float) $updated_quote_line_products[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $updated_quote_line_products[1]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[1]->quantity);
        $this->assertEquals(0, (float) $updated_quote_line_products[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $updated_quote_line_products[2]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[2]->quantity);
        $this->assertEquals(-25, (float) $updated_quote_line_products[2]->unit_discount_amount);
    }

    /** @throws \Exception */
    public function test_calculate_with_positive_discount_amount(): void
    {
        $instance = $this->getTestedInstance();
        $has_discount_admin_permission = false;

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Unit discount amount must be negative, "2000" given');

        $instance->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            2000
        );
    }

    /** @throws \Exception */
    public function test_calculate_with_negative_selling_price(): void
    {
        $instance = $this->getTestedInstance();
        $has_discount_admin_permission = false;

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Selling price must be positive, "-41.************" given');

        $instance->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -2500
        );
    }

    /** @throws \Exception */
    public function test_calculate_with_discount_below_threshold(): void
    {
        $instance = $this->getTestedInstance();
        $has_discount_admin_permission = false;

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Margin rate (0.04) can\'t be less than "0.05"');

        $instance->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -1150
        );
    }

    /** @throws \Exception */
    public function test_calculate_with_discount_below_zero_threshold(): void
    {
        $instance = $this->getTestedInstance();
        $has_discount_admin_permission = true;

        $this->expectException(InternalErrorException::class);
        $this->expectExceptionMessage('Margin rate (-0.*****************) can\'t be less than "0"');

        $instance->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -1201
        );
    }

    /** @throws \Exception */
    public function test_calculate_with_discount_below_threshold_with_permission(): void
    {
        $all_quote_lines = $this->fetchQuoteLines(30);

        $this->assertCount(3, $all_quote_lines);
        $this->assertEquals(81078, (int) $all_quote_lines[0]->product_id);
        $this->assertEquals(-15, (float) $all_quote_lines[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $all_quote_lines[1]->product_id);
        $this->assertEquals(0, (float) $all_quote_lines[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $all_quote_lines[2]->product_id);
        $this->assertEquals(-20, (float) $all_quote_lines[2]->unit_discount_amount);

        $has_discount_admin_permission = true;
        $updated_quote_line_products = $this->getTestedInstance()->calculateWith(
            30,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -1150
        );

        $this->assertCount(3, $updated_quote_line_products);
        $this->assertEquals(81078, (int) $updated_quote_line_products[0]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[0]->quantity);
        $this->assertEquals(-575, (float) $updated_quote_line_products[0]->unit_discount_amount);
        $this->assertEquals(81123, (int) $updated_quote_line_products[1]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[1]->quantity);
        $this->assertEquals(0, (float) $updated_quote_line_products[1]->unit_discount_amount);
        $this->assertEquals(128416, (int) $updated_quote_line_products[2]->product_id);
        $this->assertEquals(1, (int) $updated_quote_line_products[2]->quantity);
        $this->assertEquals(-575, (float) $updated_quote_line_products[2]->unit_discount_amount);
    }

    /** @throws \Exception */
    public function test_calculate_with_locked_quote(): void
    {
        $instance = $this->getTestedInstance();
        $has_discount_admin_permission = false;

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Quote is locked');

        $instance->calculateWith(
            40,
            self::$container->get(AccountQueryRepository::class)->getUser('admin'),
            $has_discount_admin_permission,
            -10
        );
    }

    /** @throws \Exception */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** @throws \Exception */
    protected function fetchQuoteLines(int $quote_id): array
    {
        $sql = <<<SQL
        SELECT
          ql.quote_line_id,
          qlp.*
          FROM
            backOffice.quote_line ql
              LEFT JOIN backOffice.quote_line_product qlp ON qlp.quote_line_id = ql.quote_line_id
          WHERE
            ql.quote_id = :quote_id
          ORDER BY display_order
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['quote_id' => $quote_id]);
    }
}
