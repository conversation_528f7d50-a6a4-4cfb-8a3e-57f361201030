<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\CustomerOrderPayment\Manager;

use SonVideo\Erp\CustomerOrderPayment\Manager\BankRedirectionPayloadValidation;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class BankRedirectionPayloadValidationTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the validator service. */
    private function getValidator(): ValidatorInterface
    {
        return self::$container->get('validator');
    }

    /** Tests validation rules when all keys are null. */
    public function test_rules_when_all_keys_are_null(): void
    {
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": null,
            "articles": null,
            "request_uri": null
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(3, $messages);
        $this->assertEquals(
            [
                '[customer_order_id]' => 'This value should not be blank.',
                '[request_uri]' => 'This value should not be blank.',
                '[articles]' => 'This value should not be blank.',
            ],
            $messages
        );
    }

    /** Tests validation rules when all keys are blank. */
    public function test_rules_when_all_keys_are_blank(): void
    {
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": "",
            "articles": "",
            "request_uri": ""
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertCount(3, $messages);
        $this->assertEquals(
            [
                '[customer_order_id]' => 'This value should be of type integer.',
                '[request_uri]' => 'This value should not be blank.',
                '[articles]' => 'This value should be of type iterable.',
            ],
            $messages
        );
    }

    /** Tests validation of articles in various scenarios. */
    public function test_articles(): void
    {
        // Test with empty articles array
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": 123,
            "articles": [],
            "request_uri": "toto"
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertEquals(
            [
                '[articles]' => 'This value should not be blank.',
            ],
            $messages
        );

        // Test with empty article object
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": 123,
            "articles": [
                {}
            ],
            "request_uri": "toto"
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertEquals(
            [
                '[articles][0][sku]' => 'This field is missing.',
                '[articles][0][unit_selling_price]' => 'This field is missing.',
            ],
            $messages
        );

        // Test with null article values
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": 123,
            "articles": [
                {
                    "sku": null,
                    "unit_selling_price": null
                }
            ],
            "request_uri": "toto"
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertEquals(
            [
                '[articles][0][sku]' => 'This value should not be blank.',
                '[articles][0][unit_selling_price]' => 'This value should not be blank.',
            ],
            $messages
        );

        // Test with wrong types for article values
        $invalid_request_payload = <<<JSON
        {
            "customer_order_id": 123,
            "articles": [
                {
                    "sku": 123,
                    "unit_selling_price": "toto"
                }
            ],
            "request_uri": "toto"
        }
        JSON;

        $errors = $this->getValidator()->validate(
            json_decode($invalid_request_payload, true, 512, JSON_THROW_ON_ERROR),
            BankRedirectionPayloadValidation::rules()
        );

        $messages = ConstraintMessageFormatter::extract($errors);

        $this->assertEquals(
            [
                '[articles][0][sku]' => 'This value should be of type string.',
                '[articles][0][unit_selling_price]' => 'This value should be of type numeric.',
            ],
            $messages
        );
    }
}
