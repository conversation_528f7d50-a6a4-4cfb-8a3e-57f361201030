INSERT INTO system.parameter (name, value, description)
VALUES
    ('email.mailjet_template_id.confirm_shipping', '3488641', 'Template id on mailjet'),
    ('email.mailjet_template_id.confirm_store_pickup', '3488651', 'Template id on mailjet'),
    ('email.mailjet_template_id.contact', '3469317', 'Template id on mailjet'),
    ('email.mailjet_template_id.customer_order_available', '3455226', 'Template id on mailjet'),
    ('email.mailjet_template_id.confirm_customer_order', '3429176', 'Template id on mailjet'),
    ('email.mailjet_template_id.customer_order_replenishment_in_progress', '3457579', 'Template id on mailjet'),
    ('email.mailjet_template_id.debug', '3425440', 'Template id on mailjet'),
    ('email.mailjet_template_id.internal', '4847681', 'Template id on mailjet'),
    ('email.mailjet_template_id.pickup_store_availability', '3457514', 'Template id on mailjet'),
    ('email.mailjet_template_id.quotation', '3457227', 'Template id on mailjet'),
    ('email.mailjet_template_id.quote_quotation', '3975802', 'Template id on mailjet'),
    ('email.mailjet_template_id.send_gift_card', '3491199', 'Template id on mailjet'),
    ('email.mailjet_template_id.quote_offer', '4242955', 'Template id on mailjet'),
    ('email.mailjet_template_id.external', '6439657', 'Template id on mailjet'),
    ('customer_order.anti_fraud_module.product_exclusion_rules', '[]', 'Product exclusion rules for the customer order anti fraud module'),
    ('email.bcc', '{"enabled": true, "emails": [{"email":"<EMAIL>"},{"email":"<EMAIL>","name":"Jo Joba"}]}', 'Bcc configuration')
ON CONFLICT DO NOTHING
;
