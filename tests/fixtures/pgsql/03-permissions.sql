-- Since they are not limited to tests, Permissions are handled in a separated file: sql/pg/schema/fixtures.sql
INSERT INTO permission.owner (owner_id, is_active, type, meta, inherited_roles)
  VALUES
    ('5c908c8f-88c4-4bf2-938d-b4ea5907e493', true, 'ROLE'::public.permission_owner_type, '{"name":"Role"}'::jsonb, null), -- cannot make api calls since its a role
    ('8f70d06e-7c7d-46ef-b605-744aad4112bf', true, 'ACCOUNT'::public.permission_owner_type, '{"name":"System"}'::jsonb, '{5c908c8f-88c4-4bf2-938d-b4ea5907e493}'), -- cannot make api calls since it does not have a username to hydrate with a legacy user (only in erp-server)
    ('9e81bd23-e7ac-4ba3-842f-8da6554bc540', true, 'ACCOUNT'::public.permission_owner_type, '{"name":"user1", "username": "admin"}'::jsonb, '{5c908c8f-88c4-4bf2-938d-b4ea5907e493}'),
    ('ed654889-b92e-4e86-9e69-b1165fb91220', true, 'ACCOUNT'::public.permission_owner_type, '{"name":"user2", "username": "gege"}'::jsonb, null),
    ('5a4e8ddc-2a5f-4de4-8947-ddc1038e0f20', TRUE, 'APPLICATION'::public.permission_owner_type, '{"name": "An Application"}'::jsonb, NULL)
;

INSERT INTO permission.owner_permission (owner_id, permission_id, scope)
SELECT
    '5c908c8f-88c4-4bf2-938d-b4ea5907e493', permission_id, 'erp'
FROM permission.permission
;
