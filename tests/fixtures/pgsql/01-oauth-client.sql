INSERT INTO oauth.client (id, random_id, redirect_uris, secret, allowed_grant_types)
  VALUES
    (1,
     '5ldl196q5bgogwwkko4ckc8o8wwokwg04488gow4kgk04s40kg',
     'a:1:{i:0;s:14:"http://hal.lxc";}',
     '4w5h1w4vts84g4s4ks88scw0cwco80cg44sowsw88ss4g44ooc',
     'a:5:{i:0;s:8:"password";i:1;s:18:"authorization_code";i:2;s:18:"client_credentials";i:3;s:5:"token";i:4;s:13:"refresh_token";}'),
    (5,
     '2dh2w35e98pww84g48cgcgg8o00844o0kg0o4g0kgsskwow8gs',
     'a:0:{}',
     '57b3clphgtoog84kk8ggwo84gwwgwg4oscs0k400kw8ks8gcsk',
     'a:1:{i:0;s:18:"client_credentials";}')
;

INSERT INTO oauth.access_token (id, client_id, token, expires_at, scope, account_id)
  VALUES
    (1,
     1,
     'user1-token-admin',
     EXTRACT(EPOCH FROM timezone('Europe/Paris', (NOW() + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE)),
     NULL,
     '9e81bd23-e7ac-4ba3-842f-8da6554bc540'),
    (2,
     1,
     'user2-token-without-permission',
     EXTRACT(EPOCH FROM timezone('Europe/Paris', (NOW() + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE)),
     NULL,
     'ed654889-b92e-4e86-9e69-b1165fb91220'),
    (3,
     5,
     'app-token-easylounge',
     EXTRACT(EPOCH FROM timezone('Europe/Paris', (NOW() + INTERVAL '1 year')::TIMESTAMP WITHOUT TIME ZONE)),
     NULL,
     '5a4e8ddc-2a5f-4de4-8947-ddc1038e0f20')
;
