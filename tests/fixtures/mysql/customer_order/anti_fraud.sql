INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
  VALUES
  (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1),
  (1185, 'ogone_accountable', 'none', '', '', now(), now(), 1, 1)
;

INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
  VALUES
    (2, 'Afrique du Sud', 'ZA', 'ZAF', 710, 'Monde', 'oui', 'oui', 'non', null, 999, null, '', 0, 0),
    (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

-- support schengen area
INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
  (5, 'Allemagne', 'DE', 'DEU', 276, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 1, 0),
  (65, 'Finlande', 'FI', 'FIN', 246, 'Europe', 'oui', 'oui', 'oui', null, 999, '^[0-9]{5}$', '5 chiffres', 1, 0)

;


INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page, menu, comprendre_pour_choisir, meta_description, presentation)
  VALUES
  (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', '')
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_5, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
 VALUES 
  (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080')
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture, port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav, id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay, pixmania_segment_id, hors_gabarit, illustration, redoute_nomenclature_node)
  VALUES
  (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null),
  (259, 1, 1, 'Récepteurs Bluetooth 2', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null),
  (260, 1, 1, 'Récepteurs Bluetooth 3', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null)
;

INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
  VALUES
  (1, 1, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81079, 'ARCAMRBLINKNR2', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81080, 'ARCAMRBLINKNR3', 'article', '2019-08-30 20:14:44', 259, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81081, 'ARCAMRBLINKNR4', 'article', '2019-08-30 20:14:44', 260, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81082, 'IRRELEVANT', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 81083, 'BUT_SHOULD_BE_MERGED', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
  (1, 1, 123, 'UNPRODUIT', 'generique', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80)
;

INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte, id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte, encours_maximum, encours_consomme, marque_disponible, marque_en_vente, siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax, siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse, siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile, commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile, comptabilite_email, technique_contact, technique_telephone, technique_mobile, technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen, frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape, hors_delai_auto, reliquat_attente_auto)
  VALUES
  (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;

INSERT INTO backOffice.marque (id_marque, marque, logo, status, importateur, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, gamme_qualite, public, type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif, prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis, V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
  VALUES
  (262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
  (263, 'Arcam2', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
  (264, 'Arcam3', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', '')
;

INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
  VALUES
  (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1, '2019-03-01 10:35:41')
;

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
 VALUES 
  ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81079, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink 2', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81080, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink 3', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81081, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink 4', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81082, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 263, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink 5', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-09-03 01:06:38', 1, 0, 81083, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 264, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink 6', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1)
;

INSERT INTO backOffice.transporteur (id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
  VALUES
  (2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1, 'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000, 0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', '', 5),
  (5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
  (31, 'EDNA', 'Emport Depot Nantes', 'non', 0, 2, 0, 'Emport Depot Nantes', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type, mono_colis, spidy_tracking_number_mask)
  VALUES
  (1, 2, '6C', 'Colissimo domicile', 1, 'Livraison sous 48/72h au domicile remis contre signature', 'messagerie', 1, null)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
  VALUES
  (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
  (5, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes', 'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi', 'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan')
;

INSERT INTO backOffice.paiement (id_paiement, moyen, actif, operation, avoir, paiement, description, surcout, attente_paiement, creation_distante, autorisation_distante, annulation_distante, demande_remise_distante, remboursement_distant, remise_directe, declaration_impaye, interrogation_etat_distante, statut_source, garantie_source, justificatif_creation, justificatif_creation_source, justificatif_creation_type, justificatif_acceptation, justificatif_acceptation_source, justificatif_acceptation_type, justificatif_acceptation_motif, justificatif_remise, justificatif_remise_source, justificatif_remise_type, justificatif_remise_motif, bon_remise, bon_remise_motif, compte_bancaire, journal, emport_depot)
VALUES
    (49, 'SVDCC', 'Y', 'paiement', 'N', 'Carte Cadeau SVD', 'Carte Cadeau Son-Vidéo.com', 0.00, 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No Carte', '^[0-9]{1,64}$', 'Y', 'manuel', 'No Carte', '^[0-9]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
    (59, 'CBS-O', 'N', 'paiement', 'N', 'CBOL-Ogone-3DS', 'Carte de crédit en ligne sécurisée Ogone 3DS', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'auto', 'No remise', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'N', '', '512300', 'CE', 'Y'),
    (60, 'AMX-O', 'N', 'paiement', 'N', 'Amex-Ogone', 'Carte American Express Ogone', 0.00, 'N', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '^[A-Za-z0-9_\\/\\-]{1,64}$', 'Y', 'manuel', 'No remise', '^[A-Za-z0-9_\\/]{1,64}$', 'N', '', '512000', 'AMX', 'Y'),
    (68, 'PPAL', 'N', 'paiement', 'N', 'Paypal', 'Paypal', 0.00, 'N', 'Y', 'N', 'Y', 'N', 'Y', 'Y', 'Y', 'Y', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^[A-Z0-9_\\-]{1,64}$', 'Y', 'auto', 'No transaction', '^[A-Z0-9_\\-\\.]{1,64}$', 'N', '', '512020', 'PP', 'N'),
    (90, 'FULLCB3X', 'Y', 'paiement', 'N', 'FullCB 3x', 'Cetelem FullCB 3x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
    (91, 'FULLCB4X', 'Y', 'paiement', 'N', 'FullCB 4x', 'Cetelem FullCB 4x', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No remise', '', 'N', '', '512000', 'BQ', 'Y'),
    (109, 'FLOA', 'Y', 'paiement', 'N', 'Floa', 'Floa - Paiement fractionné 3/4x sans frais ', 0.00, 'N', 'Y', 'Y', 'Y', 'N', 'Y', 'N', 'N', 'N', 'auto', 'auto', 'N', 'auto', 'ID Trans. Client', 'Y', 'auto', 'No transaction', '', 'Y', 'manuel', 'No remise', '', 'N', '', '512000', 'BQ', 'Y')
;


-- customer order
INSERT INTO backOffice.prospect (id_prospect, cnt_email, cnt_lvr_email, email, blacklist)
  VALUES
  (1, '<EMAIL>', '<EMAIL>', '<EMAIL>', 0),
  (2, '<EMAIL>', '<EMAIL>', '<EMAIL>', 1)
;

INSERT INTO backOffice.commande (id_commande, id_prospect, creation_origine, cnt_fct_email, cnt_lvr_email, commentaire_facture, no_commande_origine, flux, promotion_id, depot_emport, id_transporteur, id_pdt_transporteur, sales_channel_id, date_creation)
  VALUES
  (1, 2, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 1, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (2, 1, 'ecranlounge.com', '<EMAIL>', '<EMAIL>', 'test', 2, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (4, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 4, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (5, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 5, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (6, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 6, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (7, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 7, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (8, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 8, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (9, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 9, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (10, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 10, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (11, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 11, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (12, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 12, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (13, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 13, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (14, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 14, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (15, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 15, 'traitement', null, null, 2, 1, 1, '2018-09-11'),
  (16, 1, 'son-video.com', '<EMAIL>', '<EMAIL>', 'test', 16, 'traitement', null, null, 2, 1, 1, '2018-09-11')
;

INSERT INTO backOffice.commande (id_commande, id_prospect, creation_origine, cnt_fct_id_pays, cnt_lvr_id_pays, cnt_fct_email, cnt_lvr_email, commentaire_facture, no_commande_origine, flux, promotion_id, depot_emport, id_transporteur, id_pdt_transporteur, sales_channel_id, date_creation)
VALUES
    (3, 1, 'son-video.com', 67, 2, '<EMAIL>', '<EMAIL>', 'test', 3, 'traitement', null, null, 2, 1, 1, '2018-09-11')
;

INSERT INTO backOffice.produit_commande (id, id_commande, id_produit, id_unique, tva, quantite, prix_vente, prix_achat, description, prix_ecotaxe, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, remise_type, remise_montant, remise_description, groupe_type, groupe_description, id_bon_livraison, reservation, prix_sorecop)
  VALUES
  (10, 1, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (11, 2, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (12, 3, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (13, 4, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (14, 5, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (15, 6, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (16, 7, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (17, 8, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (18, 9, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (19, 10, 81078, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (20, 15, 81083, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (21, 15, 81079, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (22, 15, 81080, 2, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (23, 15, 81081, 3, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (24, 15, 81082, 4, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null),
  (25, 16, 123, 1, 0.200, 1, 249.00, 131.58, 'rBlink', null, null, 0.00, 0.000, 0.00, 0.000, null, null, 0.00, 0.00, 0.000, null, null, 0.00, null, null, null, null, null, null)
;

INSERT INTO backOffice.paiement_commande (id, id_commande, id_unique, id_paiement, type, workflow, creation_date, creation_usr, creation_montant, creation_justificatif, creation_origine, acceptation_date, acceptation_usr, acceptation_montant, acceptation_justificatif, annulation_date, annulation_usr, annulation_montant, demande_remise_date, demande_remise_usr, remise_date, remise_usr, remise_montant, remise_justificatif, remise_bon, remise_taux, impaye_date, impaye_usr, impaye_montant, auto_statut, auto_statut_detail, auto_garantie, auto_garantie_detail, pays_ip, pays_origine)
  VALUES
  (1, 1, 1, 49, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '1-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (2, 2, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '2-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (3, 3, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '3-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (4, 4, 1, 49, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '4-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (5, 5, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '5-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (6, 6, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '6-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (7, 7, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '7-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (8, 8, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '8-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (9, 9, 1, 60, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '9-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'remise', null, null, null, null, null),
  (10, 10, 1, 60, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '10-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null),
  (11, 11, 1, 90, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '11-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null),
  (12, 12, 1, 91, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '12-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null),
  (13, 13, 1, 68, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '13-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null),
  (14, 14, 1, 68, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '14-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, null, null, null, null, null, null),
  (15, 15, 1, 59, 'paiement', 'legacy', NOW(), 'backoffice', 4.39, '15-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null)
;

-- payment_v2
INSERT INTO backOffice.paiement_commande (id, id_commande, id_unique, id_paiement, type, workflow, operation_id_bin, creation_date, creation_usr, creation_montant, creation_justificatif, creation_origine, acceptation_date, acceptation_usr, acceptation_montant, acceptation_justificatif, annulation_date, annulation_usr, annulation_montant, demande_remise_date, demande_remise_usr, remise_date, remise_usr, remise_montant, remise_justificatif, remise_bon, remise_taux, impaye_date, impaye_usr, impaye_montant, auto_statut, auto_statut_detail, auto_garantie, auto_garantie_detail, pays_ip, pays_origine)
  VALUES
  (16, 16, 1, 68, 'paiement', 'payment_v2', unhex(replace('12345678-df87-4378-9177-c8e8fd9b67b6', '-', '')), NOW(), 'backoffice', 4.39, '16-1', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null),
  -- combines :
  -- - payment v2
  -- - worldline
  -- - auto garantie
  -- - pays origine hors france mais dans l'espace schengen
  (17, 16, 2, 59, 'paiement', 'payment_v2', unhex(replace('12345678-df87-4378-9177-c8e8fd9b67b7', '-', '')), NOW(), 'backoffice', 4.39, '16-2', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, '3DS', 'NOT NULL', null, 'FI'),
  -- floa
  (18, 16, 3, 109, 'paiement', 'payment_v2', unhex(replace('12345678-df87-4378-9177-c8e8fd9b67b8', '-', '')), NOW(), 'backoffice', 4.39, '16-3', 'son-video.com', null, null, 0.00, null, null, null, 0.00, null, null, null, null, 0.00, null, null, 1, null, null, 0.00, 'accepte', null, null, null, null, null)
;

INSERT INTO backOffice.anti_fraud_customer_order (customer_order_id, status, reason)
VALUES
  (15, 'REJECTED', '{"name":"HAS_INVALID_ARTICLES","details":[{"key":"INVALID_SUBCATEGORY","sku":"ARCAMRBLINKNR3","details":{"subcategory_id":259}},{"key":"INVALID_SUBCATEGORY","sku":"ARCAMRBLINKNR4","details":{"subcategory_id":260}},{"key":"INVALID_BRAND","sku":"IRRELEVANT","details":{"brand_id":263}},{"key":"INVALID_BRAND","sku":"BUT_SHOULD_BE_MERGED","details":{"brand_id":264}},{"key":"INVALID_SKU","sku":"ARCAMRBLINKNR2"}]}')
;


INSERT INTO backOffice.anti_fraud_customer_order_payment (customer_order_payment_id, status, reason)
VALUES
  (16, 'NON_ELIGIBLE', '{"name": "PAYMENT_NOT_SUPPORTED"}')
;
