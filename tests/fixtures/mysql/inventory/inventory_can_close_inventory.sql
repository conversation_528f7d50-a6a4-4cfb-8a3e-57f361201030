INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (4, '03.05.a.01.01.01', 2, '03.05.A$01.01.01', 1),
    (5, '03.04.a.01.01.01', 2, '03.04.A$01.01.01', 1),
    (6, '03.06.a.01.01.01', 1, '03.06.A$01.01.01', 1)
;

INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation, statut, type, name)
VALUES
    (2, '2019-08-08 11:38:32', '2019-08-15 09:43:56', 1, 2, 1000, 'closed', 'partial', null),
    (3, '2019-08-08 11:38:32', null, 1, 2, 1000, 'on-going', 'partial', null),
    (4, '2019-08-08 11:38:32', null, 1, 2, 1000, 'on-going', 'partial', null)
;

INSERT INTO backOffice.BO_INV_zone_location (zone_id, location_id)
VALUES
    (1, 4),
    (4, 5),
    (4, 6)
;

INSERT INTO backOffice.BO_INV_inventory_location (inventory_id, location_id, scanned_empty_at)
VALUES
    (3, 4, null),
    (3, 5, null),
    (4, 6, '2019-08-08 11:38:32'),
    (4, 5, null),
    (4, 4, null)
;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
    (3, 3, 'global', 1),
    (4, 3, 'produit', 2),
    (5, 4, 'global', 1),
    (6, 4, 'produit', 2)
;

INSERT INTO backOffice.BO_INV_collecte_article (BO_INV_collecte_id, id_produit, id_emplacement, sf_guard_user_id, quantite, quantite_stock, date_actualisation, date_collecte)
VALUES
    (3, 81078, 4, 1000, 1, 1, '2019-08-08 11:38:32', '2019-08-08 11:38:32'),
    (5, 81078, 4, 1000, 1, 1, '2019-08-08 11:38:32', '2019-08-08 11:38:32'),
    (6, 81078, 5, 1000, 1, 1, '2019-08-08 11:38:32', '2019-08-08 11:38:32'),
    (6, 81078, 4, 1000, 1, 1, '2019-08-08 11:38:32', '2019-08-08 11:38:32')
;
