INSERT INTO backOffice.stock_classification (stock_classification_id, based_on, name, threshold, description, created_at, updated_at)
VALUES
    (1, 'revenue', 'A', '20', 'Produits à haute valeur (40% du CA)', '2025-07-29 15:56:15', '2025-07-31 18:01:28'),
    (2, 'revenue', 'B', '80', 'Produits à valeur moyenne (40% à 80% du CA)', '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (3, 'revenue', 'C', '100', 'Produits à faible valeur (20% restants)', '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (4, 'variability', 'X', '0.50', 'Produits à demande stable (CV ? 0.5)', '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (5, 'variability', 'Y', '1.00', 'Produits à demande fluctuante (0.5 < CV ? 1.0)', '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (6, 'variability', 'Z', '999.99', 'Produits à demande irrégulière (CV > 1.0)', '2025-07-29 15:56:15', '2025-07-29 15:56:15')
;

INSERT INTO backOffice.stock_classification_service_rate (service_rate, z_score)
VALUES (1, 0.100),(2, 0.120),(3, 0.140),(4, 0.160),(5, 0.180),(6, 0.200),(7, 0.220),(8, 0.240),(9, 0.260),(10, 0.280),(11, 0.300),(12, 0.320),(13, 0.340),
(14, 0.360),(15, 0.380),(16, 0.400),(17, 0.420),(18, 0.440),(19, 0.460),(20, 0.480),(21, 0.500),(22, 0.520),(23, 0.540),(24, 0.560),(25, 0.580),
(26, 0.600),(27, 0.620),(28, 0.640),(29, 0.660),(30, 0.680),(31, 0.700),(32, 0.720),(33, 0.740),(34, 0.760),(35, 0.780),(36, 0.800),(37, 0.820),
(38, 0.840),(39, 0.860),(40, 0.880),(41, 0.900),(42, 0.920),(43, 0.940),(44, 0.960),(45, 0.980),(46, 1.000),(47, 1.020),(48, 1.040),(49, 1.060),
(50, 1.080),(51, 1.100),(52, 1.120),(53, 1.140),(54, 1.160),(55, 1.180),(56, 1.200),(57, 1.220),(58, 1.240),(59, 1.260),(60, 1.280),(61, 1.300),
(62, 1.320),(63, 1.340),(64, 1.360),(65, 1.380),(66, 1.400),(67, 1.410),(68, 1.420),(69, 1.430),(70, 1.435),(71, 1.437),(72, 1.439),(73, 1.441),
(74, 1.442),(75, 1.443),(76, 1.444),(77, 1.445),(78, 1.446),(79, 1.447),(80, 1.448),(81, 1.449),(82, 1.450),(83, 1.452),(84, 1.454),(85, 1.456),
(86, 1.438),(87, 1.440),(88, 1.520),(89, 1.580),(90, 1.640),(91, 1.710),(92, 1.750),(93, 1.810),(94, 1.880),(95, 1.960),(96, 2.050),(97, 2.120),
(98, 2.330),(99, 2.580);


INSERT INTO backOffice.stock_classification_matrix (stock_classification_matrix_id, name, revenue_class, variability_class, service_rate, created_at, updated_at)
VALUES
    (1, 'AX', 'A', 'X', 97, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (2, 'AY', 'A', 'Y', 95, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (3, 'AZ', 'A', 'Z', 93, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (4, 'BX', 'B', 'X', 91, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (5, 'BY', 'B', 'Y', 90, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (6, 'BZ', 'B', 'Z', 89, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (7, 'CX', 'C', 'X', 85, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (8, 'CY', 'C', 'Y', 80, '2025-07-29 15:56:15', '2025-07-29 15:56:15'),
    (9, 'CZ', 'C', 'Z', 70, '2025-07-29 15:56:15', '2025-07-29 15:56:15')
;
