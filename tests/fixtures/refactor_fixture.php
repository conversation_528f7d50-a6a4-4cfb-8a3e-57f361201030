<?php

/**
 * Refactors SQL INSERT statements by removing a specific column from table inserts.
 *
 * This class searches for INSERT statements in SQL files and removes a specified
 * column from both the column list and corresponding values.
 */
class RefactorInsertRequest
{
    private string $table_name;
    private string $column_name;
    private string $folder_path;
    private string $database_name;

    public const ANY_OR_NEW_LINE = '(?:[^;()]|[\n])';
    public const SPACE_OR_NEW_LINE = '(?:[\s\n])';

    /**
     * Initialize the refactor request with table and column information.
     *
     * @param string $table_name    The target table name to refactor
     * @param string $column_name   The column name to remove from INSERT statements
     * @param string $folder_path   The folder path to search for SQL files
     * @param string $database_name The database name (optional, defaults to 'backOffice')
     */
    public function __construct(
        string $table_name,
        string $column_name,
        string $folder_path,
        string $database_name = 'backOffice'
    ) {
        $this->table_name = $table_name;
        $this->column_name = $column_name;
        $this->folder_path = $folder_path;
        $this->database_name = $database_name;
    }

    /**
     * Execute the refactoring process on all matching files.
     *
     * @throws \RuntimeException When no files are found or command execution fails
     */
    public function execute(): void
    {
        $files = $this->searchFilesWithInsertRequest();

        foreach ($files as $index => $file) {
            echo sprintf('- Refactoring file: %s (%d/%d)... ', $file, $index + 1, count($files));
            $this->refactorFile($file);
            echo 'OK' . PHP_EOL;
        }
    }

    /**
     * Search for files containing INSERT statements for the specified table.
     *
     * @return string[] Array of file paths containing INSERT statements
     *
     * @throws \RuntimeException When command execution fails or no files found
     */
    private function searchFilesWithInsertRequest(): array
    {
        $regex_pattern = $this->table_name;
        echo 'Searching for table: "' .
            $this->table_name .
            '" in database: "' .
            $this->database_name .
            '" within folder: ' .
            $this->folder_path .
            PHP_EOL;

        // Use escapeshellarg to properly escape the regex for shell execution
        $escaped_regex = escapeshellarg($regex_pattern);
        $escaped_folder = escapeshellarg($this->folder_path);

        // Construct the command with proper escaping
        $command = sprintf('grep -r -l -E %s %s', $escaped_regex, $escaped_folder);
        echo 'Command: ' . $command . PHP_EOL;

        $output = shell_exec($command);

        if (false === $output) {
            throw new \RuntimeException(sprintf('Command execution failed: %s', $command));
        }

        if (empty($output)) {
            throw new \RuntimeException(sprintf('No files found containing INSERT statements for table: %s', $this->table_name));
        }

        return array_filter(explode("\n", $output), function (string $file): bool {
            return !empty($file);
        });
    }

    /**
     * Refactor a single file by removing the specified column from INSERT statements.
     *
     * @param string $file_path The path to the file to refactor
     *
     * @throws \RuntimeException When file cannot be read or written
     */
    private function refactorFile(string $file_path): void
    {
        $content = file_get_contents($file_path);

        if (false === $content) {
            throw new \RuntimeException(sprintf('Cannot read file: %s', $file_path));
        }

        $any = static::ANY_OR_NEW_LINE;
        $blank = static::SPACE_OR_NEW_LINE;

        $regex =
            '~' .
            // insert statement
            "(?<statement>INSERT$any+(?:{$this->database_name}\.)?{$this->table_name})$blank*?" .
            // columns declaration
            '\(' .
            // columns capture
            "(?<columns>$any*?{$this->column_name}$any*?)" .
            '\)' . // end of columns declaration
            "$blank*?VALUES$blank*?" .
            // values capture
            '(?<values>(?:[^;]|\n)+)' . // everything between VALUES and ;
            ';' .
            '~';

        preg_match_all($regex, $content, $matches);
        if (empty($matches)) {
            echo 'No INSERT statement found for table ' . $this->table_name . ' in file ' . $file_path . PHP_EOL;

            return;
        }

        foreach ($matches[0] as $index => $match) {
            $original_statement = $match;

            $columns = array_map('trim', explode(',', $matches['columns'][$index]));
            $rows = $this->extractValues($matches['values'][$index], $columns);

            $refactored_values = [];
            foreach ($rows as &$row) {
                // Remove column value
                unset($row[$this->column_name]);
                $refactored_values[] = '  (' . implode(', ', $row) . ')';
            }

            // Remove the column from the list of columns
            $column_positions = array_flip($columns);
            unset($column_positions[$this->column_name]);
            $filtered_columns = array_flip($column_positions);

            $modified_statement =
                $matches['statement'][$index] .
                ' (' .
                implode(', ', $filtered_columns) .
                ')' .
                PHP_EOL .
                ' VALUES ' .
                PHP_EOL .
                implode(',' . PHP_EOL, $refactored_values) .
                PHP_EOL .
                ';';

            $content = str_replace($original_statement, $modified_statement, $content);
        }

        if (false === file_put_contents($file_path, $content)) {
            throw new \RuntimeException(sprintf('Cannot write to file: %s', $file_path));
        }
    }

    /**
     * Extract values from INSERT statement and map them to columns.
     *
     * @param string   $values_string The VALUES part of the INSERT statement
     * @param string[] $columns       Array of column names
     *
     * @return array<int, array<string, string>> Array of rows with column-value mapping
     *
     * @throws \InvalidArgumentException When number of values doesn't match columns
     */
    private function extractValues(string $values_string, array $columns): array
    {
        $regex = '~'
        . '\(' //opening parenthesis
            . '(?<line>' // line capture
                . '(?:'
                    . '\'(?:[^\']|\'\')*\'' //quoted string
                    . '|' // OR
                    . '[^;()]' // all exept semicolon and parenthesis
                    . '|' // OR
                    . '[a-zA-Z_]+\(.*?\)' // function call
                . ')+'
            . ')' // line capture ends
        . '\)' //closing parenthesis
        . '~';
        preg_match_all($regex, $values_string, $line_matches);

        $rows = [];

        foreach ($line_matches['line'] as $line) {
            $regex = '~'
                . '(?<values>'
                    . '(?:[a-zA-Z_]+\(.*?\))' // function call
                    . '|' // or
                    . '[A-z0-9.]+' // non string like number, boolean, null, etc.
                    . '|' // or
                    . '\'(?:[^\']|\'\')*\'' // quoted string
                . ')'
                . '~';

            preg_match_all($regex, $line, $value_matches);
            if (count($value_matches['values']) !== count($columns)) {
                throw new \InvalidArgumentException(sprintf('Number of values (%d) does not match number of columns (%d) in row: %s. Values ', count($value_matches['values']), count($columns), $line));
            }

            $row = [];
            foreach ($value_matches['values'] as $index => $value) {
                $row[$columns[$index]] = trim($value);
            }

            $rows[] = $row;
        }

        return $rows;
    }
}

// Script execution
try {
    $table_name = 'table_name';
    $column_names = [];
    $folder_path = '/var/www/erp-server/tests/fixtures';

    foreach ($column_names as $column_name) {
        $refactor = new RefactorInsertRequest($table_name, $column_name, $folder_path);
        $refactor->execute();
    }

    echo 'Refactoring completed successfully!' . PHP_EOL;
} catch (\Exception $e) {
    echo PHP_EOL . 'Error: ' . $e->getMessage() . PHP_EOL;
    exit(1);
}
