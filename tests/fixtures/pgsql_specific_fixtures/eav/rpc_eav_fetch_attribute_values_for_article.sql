INSERT INTO eav.attribute (attribute_id, name, meta, definition, i18n)
VALUES
  (162, 'Coloris', '{"unit": null, "label": "Couleur", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (887, 'Conditionnement câble HP', '{"unit": null, "label": "Conditionnement", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (734, 'Connecteur câble HP', '{"unit": null, "label": "Connecteur", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (922, 'Qualité cables HP', '{"unit": null, "label": "Qualité", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (701, 'Section cable enceinte', '{"unit": null, "label": "Section du conducteur", "prefix": null, "suffix": null}', '{"type": "text"}', null)
;
INSERT INTO eav.attribute (attribute_id, name, meta, definition, i18n)
VALUES
  (908, 'Codecs Bluetooth', '{"unit": null, "label": "Codecs Bluetooth", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (743, 'Norme Bluetooth', '{"unit": null, "label": "Norme", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (935, 'Sens Transmission', '{"unit": null, "label": "Sens de transmission", "prefix": null, "suffix": null}', '{"type": "text"}', null),
  (595, 'Sorties audio', '{"unit": null, "label": "Sorties audio", "prefix": null, "suffix": null}', '{"type": "text"}', null)
;

INSERT INTO eav.subcategory (subcategory_id, use_filters)
VALUES
  (56, false),
  (258, true)
;

INSERT INTO eav.subcategory_attribute (attribute_id, subcategory_id, display_order, filter_status)
  VALUES
  (162, 56, 100, 'ACTIVE_OPENED'),
  (922, 56, 40, 'ACTIVE_OPENED'),
  (887, 56, 100, 'ACTIVE_CLOSED'),
  (734, 56, 100, 'INACTIVE'),
  (701, 56, 100, 'INACTIVE'),
  (935, 258, 100, 'INACTIVE'),
  (908, 258, 100, 'ACTIVE_OPENED'),
  (595, 258, 100, 'ACTIVE_OPENED'),
  (743, 258, 100, 'ACTIVE_CLOSED')
;

INSERT INTO eav.attribute_value (attribute_value_id, attribute_id, value, meta, i18n, display_order)
VALUES
  (25292, 935, 'Emetteur', '{}', null, 20),
  (25291, 935, 'Récepteur', '{}', null, 100),
  (23337, 908, 'LDAC Sony (990Kbps)', '{}', null, 100),
  (23335, 908, 'AAC (256Kbps maxi)', '{}', null, 100),
  (23336, 908, 'apt-X (350Kbps)', '{}', null, 100),
  (41111, 908, 'apt-X HD (576Kbps)', '{}', null, 100),
  (23334, 908, 'SBC (345Kbps maxi)', '{}', null, 100),
  (10834, 595, 'RCA', '{}', null, 100),
  (10835, 595, 'Jack (Casque)', '{}', null, 100),
  (12568, 595, 'Caisson de basse', '{}', null, 100),
  (12569, 595, 'XLR', '{}', null, 100),
  (19743, 595, 'AES / EBU (XLR)', '{}', null, 450),
  (21098, 595, 'Pré-out RCA', '{}', null, 100),
  (21257, 595, 'Jack 6,35 mm', '{}', null, 210),
  (22147, 595, 'Pré-out XLR', '{}', null, 100),
  (23751, 595, 'Line RCA', '{}', null, 100),
  (25774, 595, 'Mini-Jack 4,4 mm', '{}', null, 100),
  (17807, 595, 'Mini-Jack 3,5 mm', '{}', null, 100),
  (12617, 595, 'Coaxiale (numérique)', '{}', null, 100),
  (24503, 595, 'Mini-Jack 2.5 mm', '{}', null, 100),
  (12618, 595, 'SPDif (Optique)', '{}', null, 100),
  (25572, 595, 'Pentaconn 4,4 mm', '{}', null, 100),
  (15278, 595, 'Aucune', '{}', null, 100),
  (18686, 743, 'Bluetooth v2.0', '{}', null, 100),
  (22052, 887, 'Bobine', '{}', null, 20)
;

INSERT INTO eav.product_value (sku, attribute_value_id)
VALUES
  ('ARCAMRBLINKNR', 25292),
  ('ARCAMRBLINKNR', 23334),
  ('ARCAMRBLINKNR', 12568),
  ('ARCAMRBLINKNR', 18686),
  ('NORSTCL81123M', 22052)
;
