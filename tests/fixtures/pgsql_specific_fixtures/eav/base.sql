INSERT INTO eav.attribute (attribute_id, name, meta, definition, i18n)
VALUES
(162, 'Coloris', '{"unit": null, "label": "Couleur", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(887, 'Conditionnement câble HP', '{"unit": null, "label": "Conditionnement", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(734, 'Connecteur câble HP', '{"unit": null, "label": "Connecteur", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(922, 'Qualité cables HP', '{"unit": null, "label": "Qualité", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(701, 'Section cable enceinte', '{"unit": null, "label": "Section du conducteur", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(908, 'Codecs Bluetooth', '{"unit": null, "label": "Codecs Bluetooth", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(743, 'Norme Bluetooth', '{"unit": null, "label": "Norme", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(935, 'Sens Transmission', '{"unit": null, "label": "Sens de transmission", "prefix": null, "suffix": null}', '{"type": "text"}', null),
(595, 'Sorties audio', '{"unit": null, "label": "Sorties audio", "prefix": null, "suffix": null}', '{"type": "text"}', null)
;

INSERT INTO eav.attribute_value (attribute_value_id, attribute_id, value, meta, i18n, display_order)
VALUES
(1359, 162, 'Rouge', '{}', null, 100),
(1356, 162, 'Noir', '{}', null, 100),
(1363, 162, 'Vert', '{}', null, 100),
(3906, 162, 'Orange', '{}', null, 100),
(6438, 162, 'Violet', '{}', null, 100),
(7109, 162, 'Jaune', '{}', null, 100),
(10834, 595, 'RCA', '{}', null, 100),
(10835, 595, 'Jack (Casque)', '{}', null, 100),
(12568, 595, 'Caisson de basse', '{}', null, 100),
(12569, 595, 'XLR', '{}', null, 100),
(13011, 162, 'Beige', '{}', null, 125),
(13982, 701, '2 x 1 mm²', '{}', null, 10),
(13983, 701, '2 x 1,5 mm²', '{}', null, 20),
(13984, 701, '2 x 2 mm²', '{}', null, 30),
(13985, 701, '2 x 2,5 mm²', '{}', null, 40),
(13987, 701, '2 x 3,5 mm²', '{}', null, 60),
(13988, 701, '2 x 4 mm²', '{}', null, 70),
(13989, 701, '2 x 4,5 mm²', '{}', null, 80),
(13990, 701, '2 x 5 mm²', '{}', null, 90),
(13991, 701, '2 x 6 mm²', '{}', null, 100),
(13992, 701, '2 x 7 mm²', '{}', null, 110),
(13993, 701, '2 x 8 mm²', '{}', null, 120),
(13986, 701, '2 x 3 mm²', '{}', null, 100),
(17173, 734, 'Fourches', '{}', null, 20),
(17172, 734, 'Câble à dénuder', '{}', null, 100),
(18683, 743, 'Bluetooth v1.0', '{}', null, 10),
(18684, 743, 'Bluetooth v1.1', '{}', null, 20),
(18685, 743, 'Bluetooth v1.2', '{}', null, 30),
(18687, 743, 'Bluetooth v2.1', '{}', null, 50),
(18688, 743, 'Bluetooth v3.0', '{}', null, 60),
(18689, 743, 'Bluetooth v4.0', '{}', null, 70),
(18690, 743, 'Bluetooth v4.1', '{}', null, 80),
(18686, 743, 'Bluetooth v2.0', '{}', null, 100),
(18768, 701, '2 x 0,75 mm²', '{}', null, 5),
(19743, 595, 'AES / EBU (XLR)', '{}', null, 450),
(21098, 595, 'Pré-out RCA', '{}', null, 100),
(21257, 595, 'Jack 6,35 mm', '{}', null, 210),
(22052, 887, 'Bobine', '{}', null, 20),
(22053, 887, 'Monté', '{}', null, 100),
(22051, 887, 'Sur mesure', '{}', null, 100),
(22204, 701, '14 x 0,13 mm²', '{}', null, 300),
(22205, 701, '20 x 0,13 mm²', '{}', null, 320),
(22206, 701, '10 x 0,13 mm²', '{}', null, 290),
(22147, 595, 'Pré-out XLR', '{}', null, 100),
(22278, 162, 'Transparent', '{}', null, 100),
(23337, 908, 'LDAC Sony (990Kbps)', '{}', null, 100),
(23335, 908, 'AAC (256Kbps maxi)', '{}', null, 100),
(23336, 908, 'apt-X (350Kbps)', '{}', null, 100),
(23751, 595, 'Line RCA', '{}', null, 100),
(24058, 701, '2 x 2,8 mm²', '{}', null, 45),
(24059, 701, '2 x 5,2 mm²', '{}', null, 92),
(24065, 701, '2 x 6,2 mm²', '{}', null, 102),
(24285, 922, 'Standard **', '{}', null, 2),
(24286, 922, 'Classique ***', '{}', null, 3),
(24290, 922, 'Simple *', '{}', null, 1),
(24292, 701, '2 x 1,77 mm²', '{}', null, 25),
(24294, 701, '2 x 5,5 mm²', '{}', null, 95),
(24304, 701, '2 x 1,25 mm²', '{}', null, 22),
(24305, 701, '2 x 1,88 mm²', '{}', null, 26),
(24287, 922, 'Performance ****', '{}', null, 100),
(24288, 922, 'Excellence *****', '{}', null, 100),
(24489, 701, '2 x 1,6 mm²', '{}', null, 24),
(25292, 935, 'Emetteur', '{}', null, 20),
(25291, 935, 'Récepteur', '{}', null, 100),
(25774, 595, 'Mini-Jack 4,4 mm', '{}', null, 100),
(25882, 701, '2 x 2,08 mm²', '{}', null, 32),
(1358, 162, 'Blanc', '{}', null, 100),
(10712, 162, 'Rose', '{}', null, 100),
(1583, 162, 'Bleu', '{}', null, 100),
(17807, 595, 'Mini-Jack 3,5 mm', '{}', null, 100),
(12617, 595, 'Coaxiale (numérique)', '{}', null, 100),
(24503, 595, 'Mini-Jack 2.5 mm', '{}', null, 100),
(12618, 595, 'SPDif (Optique)', '{}', null, 100),
(1364, 162, 'Marron', '{}', null, 100),
(25572, 595, 'Pentaconn 4,4 mm', '{}', null, 100),
(17174, 734, 'Fiches bananes', '{}', null, 100),
(15278, 595, 'Aucune', '{}', null, 100),
(41111, 908, 'apt-X HD (576Kbps)', '{}', null, 100),
(23334, 908, 'SBC (345Kbps maxi)', '{}', null, 100),
(1582, 162, 'Gris', '{}', null, 100)
;


INSERT INTO eav.subcategory (subcategory_id, use_filters)
VALUES
(56, true),
(258, false)
;

INSERT INTO eav.subcategory_attribute (attribute_id, subcategory_id, display_order, filter_status)
VALUES
(162, 56, 100, 'ACTIVE_OPENED'),
(922, 56, 40, 'ACTIVE_OPENED'),
(887, 56, 100, 'ACTIVE_CLOSED'),
(734, 56, 100, 'INACTIVE'),
(701, 56, 100, 'INACTIVE'),
(935, 258, 100, 'INACTIVE'),
(908, 258, 100, 'INACTIVE'),
(595, 258, 100, 'INACTIVE'),
(743, 258, 100, 'INACTIVE'),
(701, 258, 100, 'INACTIVE')
;

-- products linked to subcat 56
-- QEDQE1455
-- QEDQE6119
INSERT INTO eav.product_value (sku, attribute_value_id)
VALUES
('QEDQE1455', 1359),
('QEDQE1455', 24286),
('QEDQE1455', 22051),
('QEDQE1455', 17173),
('QEDQE1455', 24292),

('QEDQE6119', 1356),
('QEDQE6119', 24288),
('QEDQE6119', 22052),
('QEDQE6119', 17173)
;

-- products linked to subcat 258
-- YAMEPH100SI
INSERT INTO eav.product_value (sku, attribute_value_id)
VALUES
('YAMEPH100SI', 25292),
('YAMEPH100SI', 23334)
;


INSERT INTO eav.product_subcategory (sku, subcategory_id)
VALUES
('YAMEPH100SI', 258),
('QEDQE1455', 56),
('QEDQE6119', 56),
('GRUNMW702700', 144)
;