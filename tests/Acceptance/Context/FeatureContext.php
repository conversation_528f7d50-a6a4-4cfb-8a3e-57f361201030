<?php

namespace App\Tests\Acceptance\Context;

use App\Tests\Unit\Test;
use Behat\Behat\Context\Context;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\MinkExtension\Context\MinkContext;
use Behat\Testwork\Hook\Scope\BeforeSuiteScope;
use League\Flysystem\MountManager;
use SonVideo\Erp\Filesystem\Manager\TemporaryFile;
use Symfony\Component\HttpKernel\KernelInterface;

/**
 * This context class contains the definitions of the steps used by the demo
 * feature file. Learn how to get started with Behat and BDD on Behat's website.
 *
 * @see http://behat.org/en/latest/quick_start.html
 */
class FeatureContext extends MinkContext implements Context
{
    /** @var KernelInterface */
    private $kernel;

    /** FeatureContext constructor. */
    public function __construct(KernelInterface $kernel)
    {
        $this->kernel = $kernel;
    }

    /**
     * beforeSuite.
     *
     * This method is run before any feature in the suite runs.
     *
     * We use it to create the database in initial launch (the 'first' suite)
     * and (re)populate the database before each user suite (called 'core_features).
     *
     * @see http://docs.behat.org/en/v2.5/guides/3.hooks.html
     *
     * @BeforeSuite
     */
    public static function beforeSuite(BeforeSuiteScope $scope): void
    {
        $suite_name = $scope->getSuite()->getName();

        if ('default' === $suite_name) {
            $fixture = new FixtureAcceptance();
            $fixture->create()->shutdown();
            $fixture->populate();
        }

        static::clearCache();
    }

    /**
     * before.
     *
     * This method is execute before all the scenarios that contain this context.
     * In my case, I set base_url mink goutte for each feature.
     * base_url is set from annotation(tag) at start of each feature file
     *
     * @BeforeScenario
     */
    public function before(BeforeScenarioScope $scope): void
    {
        $this->setMinkParameter('base_url', $_ENV['BEHAT_BASE_URL']);
    }

    /**
     * afterSuite.
     *
     * @AfterSuite
     */
    public static function afterSuite(): void
    {
        static::clearCache();
    }

    /** clearCache */
    public static function clearCache(): void
    {
    }

    /**
     * reloadDatabase.
     *
     * @BeforeScenario @reload-database
     */
    public function reloadDatabase(): void
    {
        $fixture = new FixtureAcceptance($this->kernel);
        // IMPORTANT : Do not shutdown the kernel
        $fixture->populate();
    }

    /**
     * clearDatabase.
     *
     * @BeforeScenario @clear-database
     */
    public function clearDatabase(): void
    {
        $fixture = new FixtureAcceptance($this->kernel);
        $fixture->clear();
    }

    /**
     * waitSeconds.
     *
     * @When /^(?:|I )wait for (?P<time>\d+) seconds?$/
     */
    public function waitSeconds(int $time): self
    {
        sleep($time);

        return $this;
    }

    /**
     * urlShouldContainerParameter.
     *
     * @throws \Exception
     * @Then url should contain the parameter :parameter_name
     */
    public function urlShouldContainTheParameter(string $parameter_name)
    {
        $url = parse_url(str_replace('#', '?', $this->getSession()->getCurrentUrl()));
        parse_str($url['query'] ?? '', $params);

        if (!isset($params[$parameter_name])) {
            throw new \Exception(sprintf('Query parameter "%s" is not found. Current url: "%s".', $parameter_name, $this->getSession()->getCurrentUrl()));
        }
    }

    /**
     * urlShouldContainerParameterWithValue.
     *
     * @throws \Exception
     *
     * @Then url should contain the parameter :parameter_name with value :value
     */
    public function urlShouldContainerParameterWithValue(string $parameter_name, string $value)
    {
        $this->urlShouldContainTheParameter($parameter_name);

        $url = parse_url(str_replace('#', '?', $this->getSession()->getCurrentUrl()));
        parse_str($url['query'] ?? '', $params);

        if ($params[$parameter_name] !== $value) {
            throw new \Exception(sprintf('Query parameter "%s" has not value "%s". Value is "%s". Current url: "%s".', $parameter_name, $value, $params[$parameter_name], $url));
        }
    }

    /**
     * PG SQL fixtures.
     *
     * @Given /^I load sql fixtures from file "([^"]*)"$/
     */
    public function loadSqlFixturesFromFile(string $file): void
    {
        $fixture = new FixtureAcceptance($this->kernel);
        // IMPORTANT : Do not shutdown the kernel
        $fixture->loadSpecificFixtures($file);
    }

    /**
     * loadMysqlFixturesFromFile.
     *
     * @Given /^I load mysql fixtures from file "([^"]*)"$/
     */
    public function loadMysqlFixturesFromFile(string $file): void
    {
        $fixture = new FixtureAcceptance($this->kernel);
        // IMPORTANT : Do not shutdown the kernel
        $fixture->loadMysqlSpecificFixtures($file);
    }

    /**
     * Load Mysql fixture in specified database.
     *
     * @Given /^I load mysql fixtures from file "([^"]*)" to "([^"]*)"$/
     *
     * @throws \Exception
     */
    public function loadMysqlFixturesFromFileTo(string $file, string $database): void
    {
        $fixture = new FixtureAcceptance($this->kernel);
        // IMPORTANT : Do not shutdown the kernel
        $fixture->loadMysqlSpecificFixtures($file, $database);
    }

    /**
     * thereIsATemporaryFileNamed.
     *
     * @Given /^There is a temporary file named "([^"]*)" from "([^"]*)"$/
     */
    public function thereIsATemporaryFileNamed(string $target_name, string $source_name): void
    {
        $mount_manager = $this->kernel
            ->getContainer()
            ->get('test.service_container')
            ->get(MountManager::class);
        $source_fs = $mount_manager->getFilesystem('mock_filesystem');
        $target_fs = $mount_manager->getFilesystem(TemporaryFile::FILESYSTEM);

        $content = $source_fs->read($source_name);
        if ($target_fs->has($target_name)) {
            $target_fs->delete($target_name);
        }

        $target_fs->write($target_name, $content);
    }

    /** @Given /^There is a file in "([^"]*)" named "([^"]*)" from "([^"]*)"$/ */
    public function thereIsAFileNamedFrom(string $target_fs, string $target_name, string $source_name): void
    {
        Test::moveFile(
            $this->kernel->getContainer()->get('test.service_container'),
            $target_fs,
            $target_name,
            $source_name
        );
    }
}
