<?php
/*
 * This file is part of CMS BackOffice package.
 *
 * (c) 2016 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Acceptance\Context;

use App\Kernel;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

/**
 * FixtureAcceptance.
 *
 * @copyright   2016 Son Video Distribution
 * <AUTHOR> <<EMAIL>>
 */
class FixtureAcceptance
{
    /** @var KernelInterface */
    protected $kernel;

    /**
     * FixtureAcceptance constructor.
     *
     * @param KernelInterface $kernel
     */
    public function __construct($kernel = null)
    {
        if (is_null($kernel)) {
            $this->kernel = new Kernel('test', true);
            $this->kernel->boot();
        } else {
            $this->kernel = $kernel;
        }
    }

    /**
     * create.
     *
     * Launch atoum.php to create test template database.
     *
     * @return $this
     */
    public function create()
    {
        $atoum_file = __DIR__ . '/../../../.atoum.php';
        if (!file_exists($atoum_file)) {
            throw new \RuntimeException(sprintf('Can not find %s.', $atoum_file));
        }

        if (false === system(sprintf('php -f %s', $atoum_file))) {
            throw new \RuntimeException('Can not create test template database.');
        }

        return $this;
    }

    /**
     * populate.
     *
     * Populate test database.
     *
     * @return $this
     */
    public function populate()
    {
        PgDatabase::reloadFixtures();

        return $this;
    }

    /**
     * clear.
     *
     * Clear test database.
     *
     * @return $this
     */
    public function clear()
    {
        PgDatabase::reloadFixtures();
        MySqlDatabase::clearDatabases();

        return $this;
    }

    /**
     * shutdown.
     *
     * Shutdown kernel
     */
    public function shutdown(): void
    {
        $this->kernel->shutdown();
    }

    /** loadSpecificFixtures */
    public function loadSpecificFixtures(string $file): self
    {
        PgDatabase::loadSpecificFixtures([$file]);

        return $this;
    }

    /**
     * loadMysqlSpecificFixtures.
     *
     * @throws \Exception
     */
    public function loadMysqlSpecificFixtures(string $file, string $database = 'backOffice'): self
    {
        MySqlDatabase::loadSpecificFixtures([$file], [':database' => $database]);

        return $this;
    }

    /** getContainer */
    public function getContainer(): ContainerInterface
    {
        return $this->kernel->getContainer();
    }
}
