<?php

namespace App\Tests\Acceptance\Context;

use App\Tests\Mock\Erp\Client\MailjetApiTransactionalClient;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;

class MailjetApiTransactionalContext implements Context
{
    /**
     * expectMailjetApiTransactionalCallWithResponse.
     *
     * @Given /^(?:I )?expect MailjetApiTransactional call to "(?P<resource>[^"]*)" with id "(?P<id>[^"]*)" to respond with:/
     */
    public function expectMailjetApiTransactionalCallWithResponse(
        string $resource,
        string $id,
        PyStringNode $py_string_node
    ): void {
        MailjetApiTransactionalClient::savedResult($resource, $id, $py_string_node->getRaw());
    }

    /**
     * clearMailjetApiTransactionalCache.
     *
     * @Given /^(?:I )?clear MailjetApiTransactional cache$/
     */
    public function clearMailjetApiTransactionalCache(): void
    {
        MailjetApiTransactionalClient::clearCache();
    }
}
