<?php
/*
 * This file is part of advice package.
 *
 * (c) 2018 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Acceptance\Context;

use App\Tests\Mock\RpcClientServiceMock;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\PyStringNode;
use Exception;
use Psr\Cache\InvalidArgumentException;

/**
 * Class RpcContext.
 *
 * @copyright 2018 Son-Video Distribution
 * <AUTHOR> <nicola<PERSON>.<EMAIL>>
 */
class RpcContext implements Context
{
    /**
     * rpcCallToWithMethodReturns.
     *
     * @Given Rpc call to :arg1 with method :arg2 returns :arg3
     */
    public function rpcCallToWithMethodReturns(string $service_name, string $service_uri, string $expected_result): void
    {
        RpcClientServiceMock::savedResult($service_name, $service_uri, $expected_result);
    }

    /**
     * expectRpcCallWithResponse.
     *
     * @Given /^(?:I )?expect RPC call to "(?P<name>[^"]*)" "(?P<method>[^"]*)" to respond with:/
     */
    public function expectRpcCallWithResponse(string $name, string $method, PyStringNode $py_string_node): void
    {
        RpcClientServiceMock::savedResult($name, $method, $py_string_node->getRaw());
    }

    /**
     * clearRpcCache.
     *
     * @Given /^(?:I )?clear RPC cache$/
     */
    public function clearRpcCache(): void
    {
        RpcClientServiceMock::clearCache();
    }

    /**
     * @Then /^(?:I )?print last ("(?P<rpc>[^"]*)" )?RPC call$/
     *
     * @throws InvalidArgumentException
     */
    public function printLastRpcCall(string $rpc = ''): void
    {
        $service = '';
        $method = '';
        if ('' !== $rpc) {
            $splitted_rpc = preg_split('/->/', $rpc);
            $service = $splitted_rpc[0] ?? '';
            $method = $splitted_rpc[1] ?? '';
        }

        print_r(RpcClientServiceMock::getLastCall($service, $method));
    }

    /**
     * @Then /^last RPC call service should be "(?P<service>[^"]*)"$/
     *
     * @throws InvalidArgumentException
     */
    public function lastRpcCallServiceShouldBe(string $service)
    {
        $last_call = RpcClientServiceMock::getLastCall();
        if (!is_array($last_call) || !isset($last_call['service'])) {
            throw new Exception('Last RPC call has no service defined');
        }

        if ($last_call['service'] !== $service) {
            throw new Exception(sprintf('Last RPC call service is "%s". Expected: "%s"', $last_call['service'], $service));
        }
    }

    /**
     * @Then /^last RPC call method should be "(?P<method>[^"]*)"$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallMethodShouldBe(string $method)
    {
        $last_call = RpcClientServiceMock::getLastCall();
        if (!is_array($last_call) || !isset($last_call['method'])) {
            throw new Exception('Last RPC call has no method defined');
        }

        if ($last_call['method'] !== $method) {
            throw new Exception(sprintf('Last RPC call method is "%s". Expected: "%s"', $last_call['method'], $method));
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should exists$/
     *
     * @return array|mixed
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldExists(string $node, string $rpc = '')
    {
        $service = '';
        $method = '';
        if ('' !== $rpc) {
            $splitted_rpc = preg_split('/->/', $rpc);
            $service = $splitted_rpc[0] ?? '';
            $method = $splitted_rpc[1] ?? '';
        }

        $last_call = RpcClientServiceMock::getLastCall($service, $method);
        if (!is_array($last_call) || !isset($last_call['args'])) {
            throw new Exception('Last RPC call has no args defined');
        }

        return $this->extractNodeValue($node, $last_call['args']);
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should have (?P<number>\d+) element(s)?$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldHaveElements(string $node, string $number, string $rpc = '')
    {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (!is_array($node_value)) {
            throw new Exception('The node value is not an array');
        }

        $number = (int) $number;
        $size = count($node_value);
        if ($size !== $number) {
            throw new Exception(sprintf('The node size is %d. Expected: %d', $size, $number));
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should be equal to the string "(?P<value>[^"]*)"$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldBeEqualToString(string $node, string $value, string $rpc = '')
    {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (!is_string($node_value)) {
            throw new Exception(sprintf('The node value is not a string. Current: "%s"', gettype($node_value)));
        }

        if ($node_value !== $value) {
            throw new Exception(sprintf('The node value is "%s". Expected: "%s"', $node_value, $value));
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should be equal to the pystring$/
     *
     * @throws InvalidArgumentException
     */
    public function lastRpcCallArgsNodeShouldBeEqualToPyString(
        string $node,
        PyStringNode $py_string_node,
        string $rpc = ''
    ) {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (!is_string($node_value)) {
            throw new Exception(sprintf('The node value is not a string. Current: "%s"', gettype($node_value)));
        }

        $value = trim($py_string_node->getRaw());
        if ($node_value !== $value) {
            throw new Exception(sprintf('The node value is "%s". Expected: "%s"', $node_value, $value));
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should be equal to the number "(?P<value>[^"]*)"$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldBeEqualToNumber(string $node, string $value, string $rpc = '')
    {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (!is_numeric($node_value)) {
            throw new Exception(sprintf('The node value is not a number. Current: "%s"', gettype($node_value)));
        }

        if ($node_value !== (int) $value && $node_value !== (float) $value) {
            throw new Exception(sprintf('The node value is "%s". Expected: "%s"', $node_value, $value));
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should not be empty$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldNotBeEmpty(string $node, string $rpc = '')
    {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (empty($node_value)) {
            throw new Exception('The node value is empty');
        }
    }

    /**
     * @Then /^last ("(?P<rpc>[^"]*)" )?RPC call args node "(?P<node>[^"]*)" should be null$/
     *
     * @throws InvalidArgumentException
     * @throws Exception
     */
    public function lastRpcCallArgsNodeShouldBeNull(string $node, string $rpc = '')
    {
        $node_value = $this->lastRpcCallArgsNodeShouldExists($node, $rpc);
        if (null !== $node_value) {
            throw new Exception('The node value is not null');
        }
    }

    /**
     * Utility function to retrieve an element in a multidimensional array.
     *
     * @param string $node Path to the element. Ex: '9->data->my_element'
     *
     * @return array|mixed
     *
     * @throws Exception
     */
    protected function extractNodeValue(string $node, array $array)
    {
        $path = explode('->', $node);
        $current = $array;
        $slice_size = 0;
        foreach ($path as $key) {
            try {
                $current = $current[$key];
            } catch (\Exception $e) {
                $slice = array_slice($path, 0, $slice_size);
                throw new Exception(sprintf('Element "%s" does not exist at path "%s"', $key, implode('->', $slice)), $e->getCode(), $e);
            }

            ++$slice_size;
        }

        return $current;
    }
}
