<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Acceptance\Context;

use App\Tests\Mock\SynappsNotifierMock;
use Behat\Behat\Context\Context;
use <PERSON>hat\Gherkin\Node\PyStringNode;
use SonVideo\Synapps\Client\Manager\SynappsNotifier;
use Symfony\Component\HttpKernel\KernelInterface;

class SynappsContext implements Context
{
    /** @var SynappsNotifierMock */
    private $synapps_notifier_mock;

    /** FeatureContext constructor. */
    public function __construct(KernelInterface $kernel)
    {
        $this->synapps_notifier_mock = $kernel->getContainer()->get(SynappsNotifier::class);
    }

    /** @Given /^(?:I )?flush Synapps events$/ */
    public function flushSynappsEvents(): void
    {
        $this->synapps_notifier_mock->flush();
    }

    /** @Then /^(?:I )?should have sent a synapps event with subject ("(?P<subject>[^"]*)" )?and payload:$/ */
    public function sendAnEventWithPayload(string $subject, PyStringNode $py_string_node)
    {
        $found = $this->synapps_notifier_mock->hasSentEventWithSubjectAndPayload(
            $subject,
            json_decode($py_string_node, true, 512, JSON_THROW_ON_ERROR)
        );

        if (false === $found) {
            throw new \Exception(implode("\n", ['Synapps event not found', vsprintf('Found "%d" message(s) : %s', [$this->synapps_notifier_mock->getCountMessages(), json_encode($this->synapps_notifier_mock->getMessagesArray(), JSON_PRETTY_PRINT)])]) . "\n");
        }
    }
}
