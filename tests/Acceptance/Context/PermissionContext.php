<?php
/*
 * This file is part of advice package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Acceptance\Context;

use Behat\Behat\Context\Context;
use Exception;
use PommProject\Foundation\Pomm;
use PommProject\Foundation\Session;

class PermissionContext implements Context
{
    /** @var Session */
    private $pomm_session;

    public function __construct(Pomm $pomm)
    {
        $this->pomm_session = $pomm->getSession('admin');
    }

    /**
     * Add a permission to a user.
     *
     * @When /^(?:I )?add permission "(?P<permission>[^"]*)" to user "(?P<name>[^"]*)"/
     */
    public function addPermissionTo(string $permission, string $name): void
    {
        $user_id = $this->getUserId($name);

        $sql = <<<PGSQL
        INSERT INTO permission.owner_permission (owner_id, permission_id, scope) VALUES ($*, $*, 'erp')
        ON CONFLICT (owner_id, permission_id, scope) DO NOTHING;
        PGSQL;

        $this->pomm_session->getQueryManager()->query($sql, [$user_id, $permission]);
    }

    /**
     * Remove a permission from a user.
     *
     * @When /^(?:I )?remove permission "(?P<permission>[^"]*)" from user "(?P<name>[^"]*)"/
     */
    public function removePermissionFrom(string $permission, string $name): void
    {
        $user_id = $this->getUserId($name);

        $sql = <<<PGSQL
        DELETE FROM permission.owner_permission WHERE owner_id = $* AND permission_id = $*;
        PGSQL;
        $this->pomm_session->getQueryManager()->query($sql, [$user_id, $permission]);
    }

    /**
     * Remove all permissions from a user.
     * Note that this does not remove inherited permissions.
     *
     * @When /^(?:I )?remove all permissions from user "(?P<name>[^"]*)"/
     */
    public function removeAllPermissionsFrom(string $name): void
    {
        $user_id = $this->getUserId($name);

        $sql = <<<PGSQL
        DELETE FROM permission.owner_permission WHERE owner_id = $*;
        PGSQL;
        $this->pomm_session->getQueryManager()->query($sql, [$user_id]);
    }

    /** @throws Exception */
    protected function getUserId(string $name)
    {
        $sql = <<<PGSQL
        SELECT owner_id FROM permission.owner WHERE meta->>'name' = $*;
        PGSQL;

        $roles = $this->pomm_session
            ->getQueryManager()
            ->query($sql, [$name])
            ->extract();
        if (!is_countable($roles) || count($roles) < 1) {
            throw new \Exception(sprintf('User with name "%s" not found', $name));
        }

        return $roles[0]['owner_id'];
    }
}
