Feature: Create an inventory
  As a manager
  I need to be able to create an inventory on a given Warehouse

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/3"
    Then  the response status code should be 401

  Scenario: Updating a non-existing inventory fails
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/9"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Inventory not found"

  Scenario: Updating with a too long name fails
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/3" with body:
    """
    {
      "name": "Bonjour, je souhaiterai vraiment faire un inventaire très complet de notre bel entrepôt"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "name": "This value is too long. It should have 80 characters or less."
      }
    }
    """

  Scenario: Update an inventory successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/3" with body:
    """
    {
      "name": "mezzanines"
    }
    """
    Then  the response status code should be 204
