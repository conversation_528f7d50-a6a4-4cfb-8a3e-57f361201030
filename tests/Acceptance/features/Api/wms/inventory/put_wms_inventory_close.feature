Feature: Close an inventory
  As a manager
  I need to be able to close an ongoing inventory

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/1/close"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/1/close"
    Then  the response status code should be 401

  Scenario: Attempt to close a non-existing inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/666/close"
    Then  the response status code should be 404

  Scenario: Attempt to close an inventory when all product are not validated yet
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/2/close"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    "1" article(s) non compté(s) ou validé(s).
    """

  @clear-database
  Scenario: Close an inventory successfully
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed_and_closable.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/2/close"
    Then  the response status code should be 200
