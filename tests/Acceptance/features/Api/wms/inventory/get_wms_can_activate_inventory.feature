Feature: get can activate inventory

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_can_activate_inventory.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/can-activate"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/can-activate"
    Then  the response status code should be 401

  Scenario: Test on unknown inventory id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/666/can-activate"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Inventory not found."

  Sc<PERSON>rio: Test can activate on a partial inventory that cannot be activated
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/3/can-activate"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 3 element
    And   the JSON node "data->can_activate" should be false
    And   the JSON node "data->location_count" should be null
    And   the JSON node "data->reasons" should have 6 element
    And   the JSON node "data->reasons->delivery_notes_count" should be equal to "1"
    And   the JSON node "data->reasons->delivery_notes" should have 1 element
    And   the JSON node "data->reasons->move_missions_count" should be equal to "2"
    And   the JSON node "data->reasons->move_missions" should have 2 elements
    And   the JSON node "data->reasons->inventories_count" should be equal to "1"
    And   the JSON node "data->reasons->inventories" should have 1 element

  Scenario: Test can activate on a product inventory that cannot be activated
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/8/can-activate"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 3 element
    And   the JSON node "data->can_activate" should be false
    And   the JSON node "data->location_count" should be equal to "2"
    And   the JSON node "data->reasons" should have 6 element
    And   the JSON node "data->reasons->delivery_notes_count" should be equal to "1"
    And   the JSON node "data->reasons->delivery_notes" should have 1 element
    And   the JSON node "data->reasons->move_missions_count" should be equal to "1"
    And   the JSON node "data->reasons->move_missions" should have 1 element
    And   the JSON node "data->reasons->inventories_count" should be equal to "1"
    And   the JSON node "data->reasons->inventories" should have 1 element

  Scenario: Test can activate on a product inventory that can be activated
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/9/can-activate"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 3 element
    And   the JSON node "data->can_activate" should be true
    And   the JSON node "data->location_count" should be equal to "0"
    And   the JSON node "data->reasons" should have 6 element
    And   the JSON node "data->reasons->delivery_notes_count" should be equal to "0"
    And   the JSON node "data->reasons->delivery_notes" should have 0 element
    And   the JSON node "data->reasons->move_missions_count" should be equal to "0"
    And   the JSON node "data->reasons->move_missions" should have 0 element
    And   the JSON node "data->reasons->inventories_count" should be equal to "0"
    And   the JSON node "data->reasons->inventories" should have 0 element

  Scenario: Test can activate on a full inventory that can be activated
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/4/can-activate"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 3 element
    And   the JSON node "data->can_activate" should be true
    And   the JSON node "data->location_count" should be null
    And   the JSON node "data->reasons" should have 6 element
    And   the JSON node "data->reasons->delivery_notes_count" should be equal to "0"
    And   the JSON node "data->reasons->delivery_notes" should have 0 element
    And   the JSON node "data->reasons->move_missions_count" should be equal to "0"
    And   the JSON node "data->reasons->move_missions" should have 0 element
    And   the JSON node "data->reasons->inventories_count" should be equal to "0"
    And   the JSON node "data->reasons->inventories" should have 0 element

  Scenario: Test can activate on a closed inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/2/can-activate"
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Inventory already activated"

  Scenario: Test can activate on a partial inventory without zone
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/6/can-activate"
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Inventory has no zone"
