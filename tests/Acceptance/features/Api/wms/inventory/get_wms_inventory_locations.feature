Feature: get inventory locations
  In order to manage one inventory
  As a manager
  I need to be able to retrieve the locations of the given inventory

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_locations.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/locations"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/locations"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on unknown inventory id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/666/locations"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Inventory not found."

  Scenario: Get locations of a full inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/2/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 2 element
    And   the JSON node "data->count" should be equal to 0
    And   the JSON node "data->remaining_locations" should have 0 element

  Scenario: Get locations of a partial inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/5/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 2 elements
    And   the JSON node "data->count" should be equal to 6
    And   the JSON node "data->remaining_locations" should have 4 elements
    And   the JSON node "data->remaining_locations" should have the following keys and values
    """
    [
      {
          "location_id": "1",
          "label": "03.01.A$01.01.01"
      },
      {
          "location_id": "7",
          "label": "03.04.B$02.01.01"
      },
      {
          "location_id": "5",
          "label": "03.04.D$02.01.01"
      },
      {
          "location_id": "6",
          "label": "03.04.E$02.01.01"
      }
    ]
    """
