Feature: POST inventory
  In order to manage manage an inventory
  As a manager
  I need to be able to count an article in stock and increase its counted quantity

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/product/81078/count"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/product/81078/count"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/product/81078/count"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on unknown inventory id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/666/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 10
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Inventory not found."

  Scenario: Test on unknown product id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/product/666/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 10
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Product not found."

  Scenario: Try to count an article on a closed inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 10
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    L'inventaire "1" est cloturé
    """

  Scenario: Try to count an article on a non active inventory collect
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/3/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 10
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    Il n'y a pas de collecte active pour l'inventaire 3
    """

  Scenario: Try to count an article with a negative quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": -10
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string
    """
    La quantité ne peux pas être inférieur à 1. Quantité:-10
    """

  Scenario: Count an article successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 13
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"

  Scenario: Count an article with wrong quantity and location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 1
    }
    """
    Then  the response status code should be 200
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/products" with body:
    """
    {
      "where": {
        "product_id": {
          "_eq": "81078"
        }
      }
    }
    """
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->products[0]->product_id" should be equal to "81078"
    And   the JSON node "data->products[0]->is_validated_automatically" should be equal to "0"

  Scenario: Count an article with right quantity and location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/product/81078/count" with body:
    """
    {
      "location_id": 1,
      "quantity": 11
    }
    """
    Then  the response status code should be 200
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/2/products" with body:
    """
    {
      "where": {
        "product_id": {
          "_eq": "81078"
        }
      }
    }
    """
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->products[0]->product_id" should be equal to "81078"
    And   the JSON node "data->products[0]->is_validated_automatically" should be equal to "1"
