Feature: Get list of inventory products
  As a logistician
  I want to see the products and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/products"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/products"
    Then  the response status code should be 401

  Scenario: Get list of inventory products without filters
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/products"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->products" should have 3 elements
    And   the JSON node "data->products[0]->product_id" should be equal to "81080"

  Scenario: Get an account filtered by the product name
    # Inventory requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/1/products" with body:
    """
    {
      "where": {
        "sku": {
          "_like": "%MK%"
        }
      },
      "order_by": "short_description"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->products" should have 2 elements
    And   the JSON node "data->products[0]->product_id" should be equal to "81079"
