Feature: get inventory report

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_report.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/report"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/report"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/report"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on unknown inventory id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/666/report"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Inventory not found."

  Scenario: Generate inventory report
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "INVENTORY_READ" to user "user2"
    And   I send a "GET" request to "/api/v1/wms/inventory/1/report"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 1 element
    And   the JSON node "data->temporary_file" should be equal to the string "Rapport_inventaire_1.csv"
