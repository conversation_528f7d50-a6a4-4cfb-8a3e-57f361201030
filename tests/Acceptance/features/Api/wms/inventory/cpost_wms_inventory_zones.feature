Feature: get inventory zones

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_partial.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones"
    Then  the response status code should be 401

  Scenario: Get all inventory zones
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->zones" should have 6 elements
    And   the JSON node "data->zones[1]" should have the following keys and values
    """
    {
      "zone_id": 1,
      "name": "M1 - Allée A",
      "last_inventory_date" : "2019-08-15 09:43:56"
    }
    """
    And   the JSON node "data->zones[3]" should have the following keys and values
    """
    {
      "zone_id": 2,
      "name": "Moyen stock - Allée A - Gauche",
      "last_inventory_date" : "2019-08-12 09:43:56"
    }
    """

  Scenario: Get non-empty inventory zones
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones" with body:
    """
    {
      "where": {
        "location_count": {
          "_gt": 0
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->zones" should have 2 elements
    And   the JSON node "data->zones[0]" should have the following keys and values
    """
    {
      "zone_id": 1,
      "name": "M1 - Allée A",
      "last_inventory_date" : "2019-08-15 09:43:56"
    }
    """
    And   the JSON node "data->zones[1]" should have the following keys and values
    """
    {
      "zone_id": 4,
      "name": "TV 1",
      "last_inventory_date" : "2019-08-12 09:43:56"
    }
    """

  Scenario: Get inventory zones for a specific inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones" with body:
    """
    {
      "where": {
        "inventory_id": {
          "_eq": 3
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->zones" should have 3 elements
    And   the JSON node "data->zones[2]" should have the following keys and values
    """
    {
      "zone_id": 4,
      "name": "TV 1",
      "last_inventory_date" : "2019-08-12 09:43:56"
    }
    """

  Scenario: Get inventory zones for a specific inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones" with body:
    """
    {
      "where": {
        "warehouse_id": {
          "_eq": 1
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->zones" should have 6 elements

  Scenario: Get all inventory zones sorted by date
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventory/zones" with body:
    """
    {
      "order_by": "last_inventory_date"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->zones" should have 6 elements
    And   the JSON node "data->zones[0]->last_inventory_date" should be equal to the string "2019-08-12 09:43:56"
    And   the JSON node "data->zones[4]->last_inventory_date" should be equal to the string "2019-08-15 09:43:56"
    And   the JSON node "data->zones[5]->last_inventory_date" should be equal to the string "2019-08-15 09:43:56"
