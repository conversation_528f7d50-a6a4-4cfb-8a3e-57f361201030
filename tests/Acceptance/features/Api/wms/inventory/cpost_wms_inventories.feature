Feature: List inventories
  As a manager
  I want to see the inventories

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventories"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventories"
    Then  the response status code should be 401

  Scenario: Get all inventories (without filters)
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventories"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data->inventories" should have 4 elements

  Sc<PERSON>rio: Get only active inventories
    # Inventory requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/inventories" with body:
    """
    {
      "where": {
        "validated_at": {
          "_null": true
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->inventories" should be identical to
    """
     [
         {
             "inventory_id": 2,
             "created_at": "2020-08-08 11:38:32",
             "validated_at": null,
             "validated_by": null,
             "validated_by_name": "",
             "closed_at": null,
             "warehouse_id": 1,
             "warehouse_name": "Champigny",
             "status": "created",
             "active_inventory_collect_id": 3,
             "type": "full",
             "name": null,
             "active_collect_type": "global",
             "active_collect_number": 1
         },
         {
             "inventory_id": 3,
             "created_at": "2020-08-08 11:38:32",
             "validated_at": null,
             "validated_by": null,
             "validated_by_name": "",
             "closed_at": null,
             "warehouse_id": 3,
             "warehouse_name": "Champigny 3",
             "status": "created",
             "active_inventory_collect_id": null,
             "type": "partial",
             "name": "ce qui compte c'est le comptage",
             "active_collect_type": null,
             "active_collect_number": null
         },
         {
             "inventory_id": 4,
             "created_at": "2020-08-08 11:38:32",
             "validated_at": null,
             "validated_by": null,
             "validated_by_name": "",
             "closed_at": null,
             "warehouse_id": 3,
             "warehouse_name": "Champigny 3",
             "status": "on-going",
             "active_inventory_collect_id": null,
             "type": "partial",
             "name": "ce qui compte c'est le comptage",
             "active_collect_type": null,
             "active_collect_number": null
         }
     ]
     """

