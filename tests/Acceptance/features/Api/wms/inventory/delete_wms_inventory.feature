Feature: Delete an inventory
  In order to manage the Inventories
  As an admin
  I need to be able to delete a non validated inventory

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 401

  Scenario: Attempt to delete a non-existing inventory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/inventory/666"
    Then  the response status code should be 404

  Scenario: Delete an inventory successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/inventory/2"
    Then  the response status code should be 204

  Scenario: Delete an ongoing inventory successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/inventory/3"
    Then  the response status code should be 204
