Feature: get can close inventory

@clear-database
Scenario: Test without authorization
When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
And   I load mysql fixtures from file "inventory/inventories.sql"
And   I load mysql fixtures from file "inventory/inventory_can_close_inventory.sql"
And   I add "Content-Type" header equal to "application/json"
And   I send a "GET" request to "/api/v1/wms/inventory/3/can-close"
Then  the response status code should be 401

Scenario: Test with a non valid authorization
When  I add "Authorization" header equal to "Bearer unknown-token"
And   I send a "GET" request to "/api/v1/wms/inventory/3/can-close"
Then  the response status code should be 401

Scenario: Test on unknown inventory id
When  I add "Authorization" header equal to "Bearer user1-token-admin"
And   I send a "GET" request to "/api/v1/wms/inventory/666/can-close"
Then  the response status code should be 404
And   the response should be in JSON
And   the JSON node "status" should be equal to "error"
And   the JSON node "code" should be equal to 404
And   the JSON node "message" should be equal to "Inventory not found."

Sc<PERSON>rio: Test can close on a closed partial inventory
When  I add "Authorization" header equal to "Bearer user1-token-admin"
And   I send a "GET" request to "/api/v1/wms/inventory/2/can-close"
Then  the response status code should be 500
And   the response should be in JSON
And   the JSON node "status" should be equal to the string "error"
And   the JSON node "message" should be equal to "Inventory cannot be closed"

Scenario: Test can close on a partial inventory where spaces were not scanned
When  I add "Authorization" header equal to "Bearer user1-token-admin"
And   I send a "GET" request to "/api/v1/wms/inventory/3/can-close"
Then  the response status code should be 200
And   the response should be in JSON
And   the JSON node "status" should be equal to the string "success"
And   the JSON node "data->can_close" should be false
And   the JSON node "data->reasons->missing_locations_count" should be equal to "1"
And   the JSON node "data->reasons->missing_locations" should have 1 element

Scenario: Test can close on a partial inventory where all spaces were scanned
When  I add "Authorization" header equal to "Bearer user1-token-admin"
And   I send a "GET" request to "/api/v1/wms/inventory/4/can-close"
Then  the response status code should be 200
And   the response should be in JSON
And   the JSON node "status" should be equal to the string "success"
And   the JSON node "data->can_close" should be true
And   the JSON node "data->reasons->missing_locations_count" should be equal to "0"
And   the JSON node "data->reasons->missing_locations" should have 0 element
