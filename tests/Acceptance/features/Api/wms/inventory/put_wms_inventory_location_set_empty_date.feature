Feature: Set an inventory location scanned empty date

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/4/location/1/set-empty-date"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/4/location/1/set-empty-date"
    Then  the response status code should be 401

  Scenario: Updating a non-existing inventory fails
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "INVENTORY_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/9/location/1/set-empty-date"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Inventory not found."

  Scenario: Updating a full inventory fails
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "INVENTORY_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/2/location/1/set-empty-date"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Incompatible inventory type "full"
    """

  Scenario: Updating a non-existing inventory location fails
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "INVENTORY_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/4/location/9/set-empty-date"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "This location is not part of this inventory."

  Scenario: Update an inventory location successfully
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "INVENTORY_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/inventory/4/location/1/set-empty-date"
    Then  the response status code should be 204
