Feature: get inventory
  In order to manage manage one inventory
  As a manager
  I need to be retrieve some information on the given inventory

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "inventory/inventories.sql"
    And   I load mysql fixtures from file "inventory/inventory_not_closed.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on unknown inventory id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/666"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Inventory not found."

  Scenario: Get stock of a product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/inventory/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 3 elements
    And   the JSON node "data->inventory" should have 14 elements
    And   the JSON node "data->inventory" should have the following keys and values
    """
   {
  "inventory_id": "1",
  "created_at": "2019-08-08 11:38:32",
  "warehouse_id": "1",
  "warehouse_name": "Champigny",
  "active_collect_id": "2",
  "validated_at": "2019-08-12 09:43:56",
  "validated_by": "1000",
  "validated_by_name": "backoffice",
  "closed_at": null,
  "status": "closed",
  "active_collect_number": "2",
  "type": "full",
  "name": null,
  "collects": [
    {
      "collect_id": "1",
      "collect_type": "global",
      "collect_number": "1",
      "counted_articles": "0",
      "is_active": "0"
    },
    {
      "collect_id": "2",
      "collect_type": "produit",
      "collect_number": "2",
      "counted_articles": "0",
      "is_active": "1"
    }
  ]
}
    """
    And   the JSON node "data->statistics" should have 6 elements
    And   the JSON node "data->statistics" should have the following keys and values
    """
  [
  {
    "label": "EXPECTED",
    "total": "0"
  },
  {
    "label": "TO_COUNT",
    "total": "0"
  },
  {
    "label": "COUNTED",
    "total": "0"
  },
  {
    "label": "CAN_BE_AUTOMATICALLY_VALIDATED",
    "total": "0"
  },
  {
    "label": "MANUALLY_VALIDATED",
    "total": "0"
  },
  {
    "label": "NEEDS_REVIEW",
    "total": "0"
  }
]
    """
    And   the JSON node "data->validating_statuses" should have 3 elements
    And   the JSON node "data->validating_statuses" should have the following keys and values
    """
[
  {
    "total": "0",
    "label": "ALL"
  },
  {
    "total": "0",
    "label": "DONE"
  },
  {
    "total": "0",
    "label": "ERROR"
  }
]

    """
