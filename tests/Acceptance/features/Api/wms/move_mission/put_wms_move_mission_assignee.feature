Feature: Assign a move mission to a user
  In order to manage the Move missions
  As an admin
  I need to be able to assign a move mission to a user

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/move_mission/move_missions.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/move-mission/3/assign/1000"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/move-mission/3/assign/1000"
    Then  the response status code should be 401

  Scenario: Assign a user to move mission successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/move-mission/3/assign/1000"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->result" should be equal to the string "ok"

  Scenario: Attempt to assign a non-existing user to a move mission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/move-mission/3/assign/666"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    User with id "666" not found.
    """

  Scenario: Attempt to assign a user to a non-existing move mission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/move-mission/999/assign/1000"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'Move mission not found.'
