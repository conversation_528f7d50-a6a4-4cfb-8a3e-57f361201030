Feature: Auto pick virtual products from delivery note
  In order to auto pick virtual products after delivery note scan
  As a user
  I need to be able to get all virtual products from delivery note auto picked

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_note.sql"
    And   I send a "POST" request to "/api/v1/wms/auto-pick/virtual-products/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/auto-pick/virtual-products/123"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/wms/auto-pick/virtual-products/123"
    Then  the response status code should be 403

  Scenario: Test with token linked to an account with permission = auto pick virtual products
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/wms/auto-pick/virtual-products/123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"

  Scenario: Test with non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/wms/auto-pick/virtual-products/9999"
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "Le bon de livraison n'existe pas"
