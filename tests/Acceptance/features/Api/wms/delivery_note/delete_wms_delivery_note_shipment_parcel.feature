Feature: Delete a parcel delivery note parcel and move product to the fallback parcel for its preparation in the WMS context

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/delete_wms_delivery_note_shipment_parcel.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/4403190/parcel/455848"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/4403190/parcel/455848"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/4403190/parcel/455848"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with a non existing delivery note id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/666/parcel/455848"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Le bon de livraison 666 n'existe pas
    """

  @clear-database
  Scenario: Fail to delete the parcel if the parcel is the fist one which is considered as fallback parcel
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/delete_wms_delivery_note_shipment_parcel.sql"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/4403190/parcel/455847"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Vous ne pouvez pas supprimer le premier colis
    """

  @clear-database
  Scenario: Check successful moves to other parcels response
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/delete_wms_delivery_note_shipment_parcel.sql"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "DELETE" request to "/api/v1/wms/delivery-note/4403190/parcel/455848"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->parcels" should have 1 element
    And   the JSON node "data->parcels[0]->articles" should have 3 elements
