Feature: log on delivery note
  As a logistic manager
  I need to log information on a delivery

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note_log.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/1/log"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/1/log"
    Then  the response status code should be 401

  Scenario: Test with missing parameter
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/4263254/log"
    And   the JSON node "error" should exist
    And   the response status code should be 400
    And   the response should be in JSON

    Scenario: log successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/4263254/log"
    """
    {
      "action": "toto"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note_id" should contain "4263254"


  Scenario: log successfully with all parameter
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/4263254/log"
    """
    {
      "action": "toto",
      "description": "toto"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note_id" should contain "4263254"

  Scenario: Try log on a non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/111111/log"
    """
    {
      "action": "toto"
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should contain "An error occurred while attempting to log an action for the BL"
    And   the JSON node "message" should contain "111111"

  Scenario: Try log on a no longer existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/111112/log"
    """
    {
      "action": "toto"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"