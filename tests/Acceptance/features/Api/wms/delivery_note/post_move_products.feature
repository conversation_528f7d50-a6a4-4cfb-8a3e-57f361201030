Feature: Move multiple products for multiple delivery notes
  In order to manage product moves through API
  As a user
  I need to be able to move multiple for multiple delivery notes with a single call

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "delivery_note/multiple_move_products.sql"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/move-products"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/move-products"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/delivery-note/move-products"
    Then  the response status code should be 403

  Scenario: Picking from stock to stock is not handled
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 2,
      "delivery_ticket_ids": [123,456],
      "products" : [{"product_id": 1234, "quantity": 10}]
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "code" should be equal to "1006"
    And   the JSON node "message" should be equal to the string "Type of movement not handled"
    And   the JSON node "data->origin_location_type_id" should be equal to the number "4"
    And   the JSON node "data->destination_location_type_id" should be equal to the number "4"

  Scenario: Picking on delivery note already prepared is forbidden
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 5,
      "delivery_ticket_ids": [4263254,1234],
      "products" : [{"product_id": 81078, "quantity": 5}]
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "code" should be equal to "1009"
    And   the JSON node "message" should be equal to the string "Delivery note status is incorrect"
    And   the JSON node "data->delivery_note->delivery_note_id" should be equal to the number "4263254"

  Scenario: Picking overflow is forbidden
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 5,
      "delivery_ticket_ids": [1234,5678],
      "products" : [{"product_id": 81078, "quantity": 10}]
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "code" should be equal to "1007"
    And   the JSON node "message" should be equal to the string "You try to move too much quantity compared to delivery needs"
    And   the JSON node "data->product_id" should be equal to the number "81078"
    And   the JSON node "data->overflow" should be equal to the number "5"

  Scenario: Picking of a product on multiple delivery works
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 5,
      "delivery_ticket_ids": [1234,5678],
      "products" : [{"product_id": 81078, "quantity": 4},{"product_id": 81079, "quantity": 1}]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"

  Scenario: End of Picking of a product on multiple delivery works
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 5,
      "destination_location": 4,
      "delivery_ticket_ids": [1234,5678],
      "products" : []
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->move" should have 1 element
    And   the JSON node "data->move[0]->delivery_note_id" should be equal to the number "1234"
    And   the JSON node "data->move[0]->product_id" should be equal to the number "81078"
    And   the JSON node "data->move[0]->quantity" should be equal to the number "3"
    And   the JSON node "data->incomplete" should have 1 element
    And   the JSON node "data->incomplete[0]->delivery_note_id" should be equal to the number "5678"
    And   the JSON node "data->incomplete[0]->product_id" should be equal to the number "81078"
    And   the JSON node "data->incomplete[0]->need" should be equal to the number "3"
    And   the JSON node "data->incomplete[0]->picked" should be equal to the number "2"

  @clear-database
  Scenario: End of Picking prevent partial move
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/multiple_move_products.sql"
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 5,
      "delivery_ticket_ids": [1234,5678],
      "products" : [{"product_id": 81078, "quantity": 5}]
    }
    """
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "api/v1/wms/delivery-note/move-products" with body:
    """
    {
      "origin_location": 5,
      "destination_location": 4,
      "delivery_ticket_ids": [1234,5678],
      "products" : []
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->move" should have 1 element
    And   the JSON node "data->move[0]->delivery_note_id" should be equal to the number "1234"
    And   the JSON node "data->move[0]->product_id" should be equal to the number "81078"
    And   the JSON node "data->move[0]->quantity" should be equal to the number "3"
    And   the JSON node "data->incomplete" should have 1 element
    And   the JSON node "data->incomplete[0]->delivery_note_id" should be equal to the number "5678"
    And   the JSON node "data->incomplete[0]->product_id" should be equal to the number "81079"
    And   the JSON node "data->incomplete[0]->need" should be equal to the number "1"
    And   the JSON node "data->incomplete[0]->picked" should be equal to the number "0"

