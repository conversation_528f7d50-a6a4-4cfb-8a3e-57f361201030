Feature: Update the parcel with a parcel number

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/put_wms_parcel.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/parcel/455847"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/parcel/455847"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/wms/parcel/455847"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with a non existing parcel id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/parcel/1234" with body:
    """
     {"parcel_number": "zozo"}
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Parcel 1234 not found
    """

  Scenario: Test with a non existing parcel id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/parcel/455847" with body:    
    """
    {
      "parcel_number": "zozo"
    }
    """
    Then  the response status code should be 200
