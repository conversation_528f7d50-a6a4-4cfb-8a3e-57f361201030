Feature: Get delivery note details
  In order to manage a delivery note through the API
  As a user
  I need to be able to retrieve information about a delivery note

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/wms_delivery_note.sql"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/123"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/123"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/999"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Delivery note not found."

  Scenario: Test with not found products in produit_bon_livraison
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/456"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note" should have the following keys and values
    """
    {
      "delivery_note_id": 456,
      "status": "au depart",
      "warehouse_id": 1,
      "customer_order_id": 1712826,
      "transfer_id": null,
      "transfer_status": null,
      "transfer_destination_warehouse_id": null,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "PREPARATION_FINISHED",
      "delivery_note_products": []
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products" should have 0 elements

  Scenario: Test with products without locations
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/789"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note" should have the following keys and values
    """
    {
      "delivery_note_id": 789,
      "status": "expedie",
      "warehouse_id": 1,
      "customer_order_id": null,
      "transfer_id": 123456,
      "transfer_status": "au depart",
      "transfer_destination_warehouse_id": 18,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "CREATED",
      "delivery_note_products": []
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products" should have 1 elements
    And   the JSON node "data->delivery_note->delivery_note_products[0]" should have the following keys and values
    """
    {
      "product_id": 5635,
      "sku": "GRUNMW702700",
      "type": "article",
      "description": "T\u00e9l\u00e9viseur 70 cm - 16\/9 Grundig Sedance MW70-2700 gris polaire",
      "marque": "Arcam",
      "modele": "Sedance 70 MW 70-2700 blanc",
      "quantity": 1,
      "package_number": 1,
      "countable_manually": false,
      "is_virtual_product": false,
      "is_auto_picked": false,
      "code_128": "16350005635",
      "product_locations": []
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations" should have 0 element

  Scenario: Test with products having locations
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note" should have the following keys and values
    """
    {
      "delivery_note_id": 123,
      "status": "au depart",
      "warehouse_id": 1,
      "customer_order_id": 1712826,
      "transfer_id": null,
      "transfer_status": null,
      "transfer_destination_warehouse_id": null,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "CREATED",
      "delivery_note_products": []
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products" should have 4 elements
    And   the JSON node "data->delivery_note->delivery_note_products[0]" should have the following keys and values
    """
    {
      "product_id": 50360,
      "sku": "CARTECADEAUSVD10",
      "type": "article",
      "description": "Carte cadeau Son-Vid\u00e9o.com d'un montant de 10 euros TTC \u00e0 valoir sur l'ensemble du site Internet http:\/\/www.son-video.com",
      "marque": "Arcam",
      "modele": "10 euros",
      "quantity": 1,
      "package_number": 1,
      "countable_manually": false,
      "is_virtual_product": true,
      "is_auto_picked": false,
      "code_128": "13600050360",
      "product_locations": []
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations" should have 1 element
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations[0]->location" should have the following keys and values
    """
    {
      "location_id": 2,
      "code": "03.01.a.01.01.02",
      "label": "03.01.A$01.01.02",
      "is_active": true,
      "area_id": 1,
      "area": {},
      "warehouse": {}
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations[0]->location->area" should have the following keys and values
    """
    {
      "area_id": 1,
      "code": "03.01",
      "area_type_id": 4,
      "area_type_label": "stock",
      "warehouse_id": 1,
      "label": "Petit stock haut"
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations[0]->location->warehouse" should have the following keys and values
    """
    {
      "id": 1,
      "nom_depot": "Champigny",
      "code": "03",
      "is_active": true
    }
    """
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations[0]->quantities" should have 1 element
    And   the JSON node "data->delivery_note->delivery_note_products[0]->product_locations[0]->quantities[0]" should have the following keys and values
    """
    {
      "delivery_ticket_id": null,
      "move_mission_id": null,
      "quantity": 3
    }
    """
