Feature: Fetch info on a delivery for its preparation in the WMS context

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/delivery_note/get_wms_delivery_note_info_for_preparation.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/info-for-preparation"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/info-for-preparation"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/info-for-preparation"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with a non existing delivery note id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/666/info-for-preparation"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Le bon de livraison 666 n'existe pas
    """

  Scenario: Check successful response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/delivery-note/4403190/info-for-preparation"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node data should have at least 2 keys
    And   the JSON node "data->transport" should have the following keys and values
    """
    {
        "carrier_code": "DHL",
        "delivery_note_id": 4403190,
        "is_transfer": false,
        "is_pickup": false,
        "warehouse_id": 1,
        "carrier_id": 20,
        "carrier_name": "DHL",
        "shipment_method_id": 9,
        "shipment_method_is_mono_parcel": false,
        "carrier_support_non_native_multi_parcel": false
    }
    """
    And   the JSON node "data->products" should have 1 element
    And   the JSON node "data->products[0]" should have the following keys and values
    """
    {
      "sku": "FIIOBTR5NR",
      "article_id": 143445,
      "quantity": 1,
      "name": "Performance Audio 40i (1 m)",
      "conditioning": 1,
      "sold_by_lot": "N",
      "countable_manually": false
    }
    """
