Feature: get product's stock
  In order to manage many things through API
  As a user
  I need to be able to retrieve the current product's stock

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "product_stock.sql"
    And   I send a "GET" request to "/api/v1/product/81078/stock"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/product/81078/stock"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/product/81078/stock"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on unknown product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/product/*********/stock"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 404
    And   the JSON node "message" should be equal to "Product not found."

  Scenario: Get stock of a product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/product/81078/stock"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_stocks" should have 6 elements
    And   the JSON node "data->product_stocks[0]->product_id" should be equal to the number "81078"
    And   the JSON node "data->product_stocks[0]->warehouse_id" should be equal to the number "1"
    And   the JSON node "data->product_stocks[0]->stock_quantity" should be equal to the number "138"
    And   the JSON node "data->product_stocks[0]->safety_stock" should be equal to the number "3"
