Feature: Unlink quantity of product in a location
  In order to manage product's quantities in locations through API
  As a user with enough rights
  I need to be able to unlink quantities associated to delivery note

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "locations/put_location_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/location/1/unlink" with body:
    """
    {
      "delivery_ticket_id": 4263254
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/location/1/unlink" with body:
    """
    {
      "delivery_ticket_id": 4263254
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/location/1/unlink" with body:
    """
    {
      "delivery_ticket_id": 4263254
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Delivery_ticket_id is mandatory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/location/1/unlink" with body:
    """
    {
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "message" should be equal to the string
    """
    Missing parameter in API call.
    """

  Scenario: Unlink delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/location/1/unlink" with body:
    """
    {
      "delivery_ticket_id": 4263254
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    # Sadly we have no easy way to check that everything goes right directly in the database
    # we can only check through another api endpoint
    When I add "Authorization" header equal to "Bearer user1-token-admin"
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/wms/location/1/products"

    # locations product are sorted in ascending order, first the null results then by product id
    Then the JSON node "data->location_products[1]->product_id" should be equal to the number "81078"
    And   the JSON node "data->location_products[1]->quantities" should have 2 elements

    # quantities are sorted in ascending order: first the null then delivery ticket id / move mission id
    And   the JSON node "data->location_products[1]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[1]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->location_products[1]->quantities[0]->quantity" should be equal to "12"

    And   the JSON node "data->location_products[1]->quantities[1]->delivery_ticket_id" should be null
    And   the JSON node "data->location_products[1]->quantities[1]->move_mission_id" should be equal to the number "1"
    And   the JSON node "data->location_products[1]->quantities[1]->quantity" should be equal to "1"
