Feature: Create move product
  In order to manage product moves through API
  As a user
  I need to be able to create moves

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "locations/move_products.sql"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move"
    Then  the response status code should be 403

  Scenario: Test cannot move a product from stock if associated to a delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 2,
      "destination_location": 1,
      "quantity": 3,
      "delivery_ticket_id" : 4263250
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "Emplacement d'origine non existant : EMPT 2 - PDT 81078"

  Scenario: Test with quantity higher than quantity available in location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 2,
      "quantity": 5
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string "Quantité insuffisante dans l'emplacement d'origine : 5 > 2"

  Scenario: Test with incorrect parameter quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 2,
      "quantity": 0
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string "La quantité à déplacer est incohérente : 0"

  Scenario: Test on picking product with quantity higher than quantity from delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location":1,
      "destination_location": 5,
      "quantity": 2,
      "delivery_ticket_id": 987654
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string
#    """
#    La quantité à déplacer est incohérente : 2 (quantité BL 987654 restante : 1)
#    """

  Scenario: Test on picking product with quantity higher than quantity left to pick on the delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 4,
      "quantity": 2,
      "delivery_ticket_id": 4263254
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string
#    """
#    La quantité à déplacer est incohérente : 2 (quantité BL 4263254 restante : 1)
#    """

  Scenario: Test with missing parameter location_id_destination
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move"
    """
    {
      "origin_location": 99999
    }
    """
    And   the JSON node "error" should exist
    And   the response status code should be 400
    And   the response should be in JSON

  Scenario: Test with missing parameter location_id_origin
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "destination_location": 99999
    }
    """
    And   the JSON node "error" should exist
    And   the response status code should be 400
    And   the response should be in JSON

  Scenario: Test with missing parameter quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 99999,
      "destination_location": 9999
    }
    """
    Then  the response status code should be 400
    And   the JSON node "error" should exist
    And   the response should be in JSON

  Scenario: Test with incorrect parameter location_id_origin
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": "999",
      "destination_location": 2,
      "quantity": 1
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string
#    """
#    L'emplacement d'origine 999 est incohérent (inexistant ou inactif)
#    """

  Scenario: Test with incorrect parameter destination_location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 4,
      "destination_location": 999,
      "quantity": 1
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
#    And   the JSON node "message" should be equal to the string
#    """
#    L'emplacement de destination 999 est incohérent (inexistant ou inactif)
#    """

  Scenario: Test with incorrect parameter product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/123/move" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 2,
      "quantity": 1
    }
    """
    Then  the response status code should be 500
    And   the JSON node "status" should be equal to the string "error"
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string
    """
    L'article 123 est inexistant.
    """

  Scenario: Create a product move
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 1,
      "destination_location": 2,
      "quantity": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist

  Scenario: Create a product move associated to a delivery ticket
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 4,
      "destination_location": 1,
      "quantity": 1,
      "delivery_ticket_id" : 987654
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist

  Scenario: Create a product move associated to a move mission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/product/81078/move" with body:
    """
    {
      "origin_location": 4,
      "destination_location": 2,
      "quantity": 1,
      "move_mission_id" : 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist
