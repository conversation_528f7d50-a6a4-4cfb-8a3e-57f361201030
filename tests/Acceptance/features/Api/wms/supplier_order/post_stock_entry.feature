Feature: Make a stock entry

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "wms/supplier_order/post_stock_entry.sql"
    And   I send a "POST" request to "/api/v1/wms/stock-entry"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/stock-entry"
    Then  the response status code should be 401

  Scenario: Test with a nonexistent product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/stock-entry" with body:
    """
    {
      "supplier_order_id":1,
      "transfer_id":null,
      "product_id":404,
      "quantity":1,
      "warehouse_id":1
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Une erreur est survenue lors de l'entrée stock."

  Scenario: Test with a nonexistent supplier order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/stock-entry" with body:
    """
    {
      "supplier_order_id":404,
      "transfer_id":null,
      "product_id":81080,
      "quantity":1,
      "warehouse_id":1
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Une erreur est survenue lors de l'entrée stock."

  Scenario: Test with a valid body
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/stock-entry" with body:
    """
    {
      "supplier_order_id":1,
      "transfer_id":null,
      "product_id":81080,
      "quantity":1,
      "warehouse_id":1
    }
    """
    Then  the response status code should be 204


