Feature: Get list of warehouse locations
  As a logistic manager using the mobile app
  I need to be able to retrieve the locations of a warehouse for caching purpose

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouse/warehouse_locations.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/1/locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/1/locations"
    Then  the response status code should be 401

  Scenario: Fails with a non existing warehouse
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/666/locations"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to "Warehouse not found."

  Scenario: Get list of WMS warehouse location for Champigny
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/1/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->locations" should have 3 elements
    And   the JSON node "data->locations[0]->location_id" should be equal to the number "1"
    And   the JSON node "data->locations[0]->code" should be equal to "03.01.a.01.01.01"
    And   the JSON node "data->locations[0]->is_active" should be equal to the number "1"

  Scenario: Get list of WMS warehouse location for Le Havre
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/3/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->locations" should have 1 elements
    And   the JSON node "data->locations[0]->location_id" should be equal to the number "4"
    And   the JSON node "data->locations[0]->code" should be equal to "03.05.a.01.01.01"

  Scenario: Get list of filtered WMS warehouse locations for Le Havre
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/warehouse/3/locations" with body:
    """
    {
      "where": {
        "is_active": {
          "_eq": 0
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->locations" should have 1 elements
    And   the JSON node "data->locations[0]->location_id" should be equal to the number "4"
    And   the JSON node "data->locations[0]->code" should be equal to "03.05.a.01.01.01"
    And   the JSON node "data->locations[0]->is_active" should be equal to the number "0"
