Feature: Modify a warehouse delivery days
  As a manager
  I want to modify a warehouse's delivery days

  @clear-database
  Scenario: Test put delivery days without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouses.sql"
    And   I load mysql fixtures from file "wms/warehouses.sql"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/delivery_days"
    Then  the response status code should be 401

  Scenario: Test put delivery days with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/delivery_days"
    Then  the response status code should be 401

  Scenario: Test put delivery days with valid authorization
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STORE_OPENING_HOURS_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/delivery_days" with body:
    """
    {
        "fri": true,
        "mon": false,
        "sat": false,
        "sun": false,
        "thu": true,
        "tue": true,
        "wed": true
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have the following keys and values
    """
    [
      {
          "fri": true,
          "mon": false,
          "sat": false,
          "sun": false,
          "thu": true,
          "tue": true,
          "wed": true
      }
    ]
    """

  Scenario: Test put with no body
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/delivery_days"
    Then  the response status code should be 500

  Scenario: Test put delivery days for a non existent warehouse
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/300/delivery_days" with body:
    """
    {
          "fri": true,
          "mon": false,
          "sat": false,
          "sun": false,
          "thu": true,
          "tue": true,
          "wed": true
    }
    """
    Then  the response status code should be 404

  Scenario: Test put delivery days without the permission to do so
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove permission "STORE_OPENING_HOURS_UPDATE" from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/delivery_days" with body:
    """
    {
          "fri": true,
          "mon": false,
          "sat": false,
          "sun": false,
          "thu": true,
          "tue": true,
          "wed": true
    }
    """
    Then  the response status code should be 403
