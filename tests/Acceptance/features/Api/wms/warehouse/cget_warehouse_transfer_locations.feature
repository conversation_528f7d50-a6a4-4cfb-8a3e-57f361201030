Feature: Get warehouse active transfer locations
  In order to get warehouse transfer locations
  As a user
  I need to be able to retrieve active locations list used for transfer with their destinations

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouse/warehouse_transfer_locations.sql"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/transfer-locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/transfer-locations"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/transfer-locations"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with not found warehouse
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/9999/transfer-locations"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Warehouse not found"

  Scenario: Test with known warehouse without transfer location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/3/transfer-locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist
    And   the JSON node "data->transfer_locations" should have 0 elements

  Scenario: Load warehouse transfer locations, only active and linked to the warehouse requested, ordered by label
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/transfer-locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist
    And   the JSON node "data->transfer_locations" should have 2 elements
    # first
    And   the JSON node "data->transfer_locations[0]->location_id" should be equal to the number "2"
    And   the JSON node "data->transfer_locations[0]->code" should be equal to the string "03.transfer.lyon"
    And   the JSON node "data->transfer_locations[0]->label" should be equal to the string "Lyon"
    And   the JSON node "data->transfer_locations[0]->destination_warehouse_id" should be equal to "6"
    # second
    And   the JSON node "data->transfer_locations[1]->location_id" should be equal to the number "1"
