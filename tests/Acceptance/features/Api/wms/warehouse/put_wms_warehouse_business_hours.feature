Feature: Modify a warehouse business hours
  As a manager
  I want to modify a warehouse's business hours

  @clear-database
  Scenario: Test put business hours without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouses.sql"
    And   I load mysql fixtures from file "wms/warehouses.sql"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours"
    Then  the response status code should be 401

  Scenario: Test put business hours with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours"
    Then  the response status code should be 401

  Scenario: Test put business hours with incomplete payload
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STORE_OPENING_HOURS_UPDATE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours" with body:
    """
    {
          "fri": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "sun": [],
          "thu": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "tue": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "wed": [
              [
                  "10:00",
                  "19:00"
              ]
          ]
    }
    """
    Then  the response status code should be 400

  Scenario: Test put with no body
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours"
    Then  the response status code should be 500

  Scenario: Test put business hours for a non existent warehouse
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/300/business_hours" with body:
    """
    {
          "fri": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "mon": [],
          "sat": [
              [
                  "10:00",
                  "19:30"
              ]
          ],
          "sun": [],
          "thu": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "tue": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "wed": [
              [
                  "10:00",
                  "19:00"
              ]
          ]
    }
    """
    Then  the response status code should be 404

  Scenario: Test put business hours with the permission to do so
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours" with body:
    """
    {
          "fri": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "mon": [],
          "sat": [
              [
                  "10:00",
                  "19:30"
              ]
          ],
          "sun": [],
          "thu": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "tue": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "wed": [
              [
                  "10:00",
                  "19:00"
              ]
          ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"

  Scenario: Test put business hours without the permission to do so
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove permission "STORE_OPENING_HOURS_UPDATE" from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/wms/warehouse/2/business_hours" with body:
    """
    {
          "fri": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "mon": [],
          "sat": [
              [
                  "10:00",
                  "19:30"
              ]
          ],
          "sun": [],
          "thu": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "tue": [
              [
                  "10:00",
                  "19:00"
              ]
          ],
          "wed": [
              [
                  "10:00",
                  "19:00"
              ]
          ]
    }
    """
    Then  the response status code should be 403
