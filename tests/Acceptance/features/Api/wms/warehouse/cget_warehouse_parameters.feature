Feature: Get warehouse parameters
  In order to get warehouse parameters
  As a user
  I need to be able to retrieve warehouse parameters list

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouse/warehouse_parameters.sql"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/parameters"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/parameters"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/parameters"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with not found warehouse
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/9999/parameters"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Warehouse not found"

  Scenario: Test with known warehouse without parameters
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/3/parameters"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist
    And   the JSON node "data->warehouse_parameters" should have 0 elements

  Scenario: Load warehouse parameters details
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/warehouse/1/parameters"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should exist
    And   the JSON node "data->warehouse_parameters" should have 5 elements
    And   the JSON node "data->warehouse_parameters[0]->warehouse_id" should be equal to the number "1"
    And   the JSON node "data->warehouse_parameters[0]->parameter" should be equal to the string "entry_location"
    And   the JSON node "data->warehouse_parameters[0]->parameter_value" should be equal to "100"
