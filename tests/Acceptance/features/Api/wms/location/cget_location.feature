Feature: Get location
  In order to manage a location through the API
  As a user
  I need to be able to retrieve information about a location

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "locations/location.sql"
    And   I send a "GET" request to "/api/v1/wms/location/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/location/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/location/1"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with not found location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/9999"
    And   the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Location not found."

  Scenario: Load information on a location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "1"
    And   the JSON node "data->location->code" should be equal to "03.01.a.01.01.01"
    And   the JSON node "data->location->label" should be equal to "03.01.A$01.00.01"
    And   the JSON node "data->location->is_active" should be true
    And   the JSON node "data->location->area_id" should be equal to the number "1"
    And   the JSON node "data->location->area" should exist
    And   the JSON node "data->location->area->area_id" should be equal to the number "1"
    And   the JSON node "data->location->area->code" should be equal to "03.01"
    And   the JSON node "data->location->area->area_type_label" should be equal to "stock"
    And   the JSON node "data->location->area->warehouse_id" should be equal to the number "1"
    And   the JSON node "data->location->area->label" should be equal to "Petit stock haut"
    And   the JSON node "data->location->warehouse" should exist
    And   the JSON node "data->location->warehouse->id" should be equal to the number "1"
    And   the JSON node "data->location->warehouse->nom_depot" should be equal to "Champigny"
    And   the JSON node "data->location->warehouse->code" should be equal to "03"
    And   the JSON node "data->location->warehouse->is_active" should be true
