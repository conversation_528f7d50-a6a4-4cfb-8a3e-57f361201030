Feature: Get user location
  In order to manage an user location through the API
  As a user
  I need to be able to retrieve information about a user location if it exists
  or create a new one

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "locations/locations.sql"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/1"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Load information on a location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist

  Scenario: Test with not found warehouse
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/99"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Warehouse not found."

  Scenario: Test with not found user
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/user/99/1"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    User with id "99" not found.
    """

  Scenario: Test with existing user location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/user/1234/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "37"
    And   the JSON node "data->location->code" should be equal to "03.user.1234"
    And   the JSON node "data->location->label" should be equal to "03.USER.1234"
    And   the JSON node "data->location->is_active" should be true
    And   the JSON node "data->location->area_id" should be equal to the number "9"

  Scenario: Test with not found user location = create a new one
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/location/user/9876/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->location" should exist
    And   the JSON node "data->location->location_id" should be equal to the number "38"
    And   the JSON node "data->location->code" should be equal to "03.user.9876"
    And   the JSON node "data->location->label" should be equal to "Chariot de Toto TOTO"
    And   the JSON node "data->location->is_active" should be true
    And   the JSON node "data->location->area_id" should be equal to the number "9"
