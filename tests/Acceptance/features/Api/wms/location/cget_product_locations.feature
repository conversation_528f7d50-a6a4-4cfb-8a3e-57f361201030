Feature: Get product's locations
  In order to manage a product through the API
  As a user
  I need to be able to retrieve locations of a product

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "locations/product_locations.sql"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Test with nonexistent product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/99999/locations"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "code" should be equal to the number "404"
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'Product not found with id or sku "99999".'

  Scenario: Test with a no-stock product (not in location or quantity = 0)
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81123/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should exist
    And   the JSON node "data->product_locations" should have 0 element

  Scenario: Load information on a product's locations
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 3 elements
    # Test a location node content
    And   the JSON node "data->product_locations[0]->location->location_id" should be equal to the number "1"
    And   the JSON node "data->product_locations[0]->location->code" should be equal to "03.01.a.01.01.01"
    And   the JSON node "data->product_locations[0]->location->label" should be equal to "03.01.A$01.01.01"
    And   the JSON node "data->product_locations[0]->location->is_active" should be true
    And   the JSON node "data->product_locations[0]->location->area_id" should be equal to the number "1"
    And   the JSON node "data->product_locations[0]->location->area" should exist
    And   the JSON node "data->product_locations[0]->location->area->area_id" should be equal to the number "1"
    And   the JSON node "data->product_locations[0]->location->area->code" should be equal to "03.01"
    And   the JSON node "data->product_locations[0]->location->area->area_type_label" should be equal to "stock"
    And   the JSON node "data->product_locations[0]->location->area->warehouse_id" should be equal to the number "1"
    And   the JSON node "data->product_locations[0]->location->area->label" should be equal to "Petit stock haut"
    And   the JSON node "data->product_locations[0]->location->warehouse" should exist
    And   the JSON node "data->product_locations[0]->location->warehouse->id" should be equal to the number "1"
    And   the JSON node "data->product_locations[0]->location->warehouse->nom_depot" should be equal to "Champigny"
    And   the JSON node "data->product_locations[0]->location->warehouse->code" should be equal to "03"
    And   the JSON node "data->product_locations[0]->location->warehouse->is_active" should be true
    # Test quantities
    And   the JSON node "data->product_locations[0]->quantities" should have 1 element
    And   the JSON node "data->product_locations[0]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->product_locations[0]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->product_locations[0]->quantities[0]->quantity" should be equal to "9"

    And   the JSON node "data->product_locations[1]->location->location_id" should be equal to the number "2"
    And   the JSON node "data->product_locations[1]->quantities" should have 1 element
    And   the JSON node "data->product_locations[1]->quantities[0]->delivery_ticket_id" should be equal to "4263254"
    And   the JSON node "data->product_locations[1]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->product_locations[1]->quantities[0]->quantity" should be equal to "3"

    And   the JSON node "data->product_locations[2]->location->location_id" should be equal to the number "3"
    And   the JSON node "data->product_locations[2]->quantities" should have 2 elements
    And   the JSON node "data->product_locations[2]->quantities[0]->delivery_ticket_id" should be null
    And   the JSON node "data->product_locations[2]->quantities[0]->move_mission_id" should be null
    And   the JSON node "data->product_locations[2]->quantities[0]->quantity" should be equal to "6"
    And   the JSON node "data->product_locations[2]->quantities[1]->delivery_ticket_id" should be null
    And   the JSON node "data->product_locations[2]->quantities[1]->move_mission_id" should be equal to "1"
    And   the JSON node "data->product_locations[2]->quantities[1]->quantity" should be equal to "5"

  Scenario: Can be filtered on a location
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations?filter[location_id]=1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 1 element
    And   the JSON node "data->product_locations[0]->location->location_id" should be equal to the number "1"

  Scenario: Can be filtered on an area
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations?filter[area_id]=1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 2 elements
    And   the JSON node "data->product_locations[0]->location->location_id" should be equal to the number "1"
    And   the JSON node "data->product_locations[1]->location->location_id" should be equal to the number "2"

  Scenario: Can be filtered on a warehouse
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations?filter[warehouse_id]=2"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 1 element
    And   the JSON node "data->product_locations[0]->location->location_id" should be equal to the number "3"

  Scenario: Can be sorted in location desc direction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations?order_direction=desc"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 3 elements
    And   the JSON node "data->product_locations[0]->location->code" should be equal to "04.01.a.01.01.01"
    And   the JSON node "data->product_locations[1]->location->code" should be equal to "03.01.a.01.01.02"
    And   the JSON node "data->product_locations[2]->location->code" should be equal to "03.01.a.01.01.01"

  Scenario: Can be sorted by quantity desc direction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/wms/product/81078/locations?order_by=quantity&order_direction=desc"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->product_locations" should have 3 elements
    And   the JSON node "data->product_locations[0]->location->code" should be equal to "04.01.a.01.01.01"
    And   the JSON node "data->product_locations[1]->location->code" should be equal to "03.01.a.01.01.01"
    And   the JSON node "data->product_locations[2]->location->code" should be equal to "03.01.a.01.01.02"
