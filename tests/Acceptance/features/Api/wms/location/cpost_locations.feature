Feature: Get list of locations
  As a logistic manager
  I want to see the locations and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "wms/location/locations.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/locations"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/locations"
    Then  the response status code should be 401

  Scenario: Get list of WMS locations without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/locations"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->locations" should have 4 elements

  Scenario: Get list of WMS locations filtered by a search term like an autocomplete (via the code)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    When  I send a "POST" request to "/api/v1/wms/locations" with body:
    """
    {
      "where": {
        "_or": [
          {
            "code": {
              "_like": "03.01%"
            }
          },
          {
            "label": {
              "_like": "%03.01%"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->locations" should have 2 elements
    And   the JSON node "data->locations[0]->code" should contain "03.01.a.01.01.01"
    And   the JSON node "data->locations[1]->code" should contain "03.01.a.01.01.02"

  Scenario: Get list of WMS locations filtered by a search term like an autocomplete (via the label)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    When  I send a "POST" request to "/api/v1/wms/locations" with body:
    """
    {
      "where": {
        "_or": [
          {
            "code": {
              "_like": "TEST%"
            }
          },
          {
            "label": {
              "_like": "%TEST%"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->locations" should have 3 elements
    And   the JSON node "data->locations[0]->code" should contain "03.05.a.01.01.01"
    And   the JSON node "data->locations[1]->code" should contain "03.01.a.01.01.02"
    And   the JSON node "data->locations[2]->code" should contain "03.04.a.01.01.01"
    And   the JSON node "data->locations[0]->label" should contain "03.05.TEST$01.01.01"
    And   the JSON node "data->locations[1]->label" should contain "03.01.TEST$01.01.02"
    And   the JSON node "data->locations[2]->label" should contain "03.04.TEST$01.01.01"
