Feature: Generate a sticker
  In order to generate a sticker
  As a logistician
  I need to generate a sticker for the given delivery note

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-edn.sql"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print"
    Then  the response status code should be 401

  Scenario: Test with a non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 666,
      "printer_id": 1
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Le bon de livraison 666 n'existe pas"

  @clear-database
  Scenario: Generate a sticker successfully for DHL via envoidunet
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-edn.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4403190,
      "printer_id": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  @clear-database
  Scenario: Generate a sticker successfully for Chronopost (using a print by its id)
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-chronopost.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4403191,
      "printer_id": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  @clear-database
  Scenario: Generate a sticker successfully for Chronopost (using a print by its name)
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-chronopost.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4403191,
      "printer_name": "Zebra_dev"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  @clear-database
  Scenario: Generate a sticker successfully for Mondial Relay (using a print by its name) with a single parcel
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "wms/sticker/generate-and-print-mondial-relay.sql"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4653679,
      "expedition_parcel_id": 455847,
      "printer_name": "Zebra_dev"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  Scenario: Generate a sticker successfully for Mondial Relay (using a print by its name) for a multi parcel
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4653680,
      "expedition_parcel_id": 455849,
      "printer_name": "Zebra_dev"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  Scenario: Reprint a Mondial Relay sticker (using a print by its name) with a single parcel
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4653679,
      "expedition_parcel_id": 455847,
      "printer_name": "Zebra_dev"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"

  Scenario: Reprint a Mondial Relay sticker (using a print by its name) for a multi parcel
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/wms/sticker/generate-and-print" with body:
    """
    {
      "delivery_note_id": 4653680,
      "expedition_parcel_id": 455849,
      "printer_name": "Zebra_dev"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data[0]" should be equal to the string "ok"
