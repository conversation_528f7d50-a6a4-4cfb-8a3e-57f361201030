Feature: Retrieve a list of turnover
  @clear-database
  Scenario: Test without authorization
    When I load mysql fixtures from file "users.sql"
    And   I load sql fixtures from file "statistics/cpost_turnover.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview"
    Then  the response status code should be 401

  Scenario: Test without premission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview"
    Then  the response status code should be 403

  Scenario: Get current Overview with one period
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview" with body:
      """
      {
        "periods": {
          "J": ["2023-07-01", "2023-07-01"]
        }
      }
      """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->last_sync" should be equal to the string "2023-09-01 12:00:00"
    And   the JSON node "data->statistics" should have 1 elements
    And   the JSON node "data->statistics[0]" should have the following keys and values
      """
      {
        "period": "J",
        "from": "2023-07-01",
        "to": "2023-07-02",
        "current": {
          "turnover": 80,
          "number": 1,
          "margin": 60
        },
        "n_1": {
          "turnover": 40,
          "number": 12,
          "margin": 30
        }
      }
      """

  Scenario: Get current Overview with multiples period
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview" with body:
      """
      {
        "periods": {
          "J": ["2023-07-01", "2023-07-01"],
          "30J": ["2023-06-01", "2023-07-01"]
        }
      }
      """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->statistics" should have 2 elements
    And   the JSON node "data->statistics[1]" should have the following keys and values
      """
      {
        "period": "30J",
        "from": "2023-06-01",
        "to": "2023-07-02",
        "current": {
          "turnover": 120,
          "number": 2,
          "margin": 90
        },
        "n_1": {
          "turnover": 60,
          "number": 2,
          "margin": 35
        }
      }
      """

  Scenario: Get current Overview with filter
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "STATISTICS_GLOBAL_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/statistics/customer-order/overview" with body:
      """
      {
        "periods": {
          "30J": ["2023-06-01", "2023-07-01"]
        },
        "filters": {
          "order_status": {"_eq": "Facturée"}
        }
      }
      """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON should be equal to:
      """
      {
        "status": "success",
        "data": {
          "statistics": [
            {
              "period": "30J",
              "from": "2023-06-01",
              "to": "2023-07-02",
              "current": {
                "turnover": 80,
                "number": 1,
                "margin": 60
              },
              "n_1": {
                "turnover": 0,
                "number": 0,
                "margin": 0
              }
            }
          ],
          "last_sync": "2023-09-01 12:00:00",
          "_request": {
            "periods": {
              "30J": [
                "2023-06-01",
                "2023-07-01"
              ]
            },
            "filters": {
              "order_status": {
                "_eq": "Facturée"
              }
            }
          }
        }
      }
      """