Feature: Get list of domains
  As someone identified in the ERP
  I want to see the domains and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "category/categories.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/domains"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/domains"
    Then  the response status code should be 401

  Scenario: Get list of domains without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/domains"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data->domains" should have 4 elements

  <PERSON><PERSON><PERSON>: Get list of domains filtered by an arbitrary id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/domains" with body:
    """
    {
      "where": {
        "domain_id": {
          "_eq": 15
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->domains" should have 1 element
    And   the JSON node "data->domains[0]->domain_id" should be equal to "15"
    And   the JSON node "data->domains[0]->name" should be equal to "Enceintes"

  Scenario: Get list of domains filtered by name
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/domains" with body:
    """
    {
      "where": {
        "name": {
          "_eq": "Enceintes"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->domains" should have 1 element
    And   the JSON node "data->domains[0]->domain_id" should be equal to "15"
    And   the JSON node "data->domains[0]->name" should be equal to "Enceintes"