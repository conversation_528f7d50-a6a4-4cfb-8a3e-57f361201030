Feature: Get list of categories
  As someone identified in the ERP
  I want to see the categories and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "category/categories.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/categories"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/categories"
    Then  the response status code should be 401

  Scenario: Get list of categories without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/categories"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data->categories" should have 4 elements

  Sc<PERSON>rio: Get list of categories filtered by an arbitrary id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/categories" with body:
    """
    {
      "where": {
        "category_id": {
          "_eq": 96
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->categories" should have 1 element
    And   the JSON node "data->categories[0]->category_id" should be equal to "96"
    And   the JSON node "data->categories[0]->name" should be equal to "Distributeurs et transmetteurs"

  Scenario: Get list of categories filtered by name without upper case
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/categories" with body:
    """
    {
      "where": {
        "name": {
          "_eq": "enceintes"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->categories" should have 1 element
    And   the JSON node "data->categories[0]->category_id" should be equal to "137"
    And   the JSON node "data->categories[0]->name" should be equal to "Enceintes"