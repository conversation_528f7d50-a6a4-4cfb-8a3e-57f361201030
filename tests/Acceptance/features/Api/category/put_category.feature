Feature: Update category
  As a user with enough rights
  I need to be able to edit categories

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "category/categories.sql"
    And   I send a "PUT" request to "/api/v1/category/137"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/category/137"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/category/137"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Fails when no changes happens
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/category/137" with body:
    """
    {
      "category_id": 137,
      "name": "Enceintes",
      "domain_id": 15,
      "custom_code": "********"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "No changes occurred"

  Scenario: Failed on mismatching category
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/category/137" with body:
    """
    {
      "category_id": 999,
      "name": "Enceintes bleues pailletées",
      "domain_id": 13,
      "custom_code": "********"
    }
    """
    Then  the response status code should be 422
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "category_id mismatch"

  Scenario: Successfully update the subcategory
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/category/137" with body:
    """
    {
      "category_id": 137,
      "name": "Enceintes bleues pailletées",
      "domain_id": 13,
      "custom_code": "********"
    }
    """
    Then  the response status code should be 204
