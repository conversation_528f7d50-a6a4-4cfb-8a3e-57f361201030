Feature: Cancel remit on a customer order transaction
  In order to cancel a remit on a remitted transaction
  As a user
  I need to be able to submit the id of the transaction where the remit should be cancelled

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "customer_order_payment/put_customer_order_payment_cancel_remitted_transaction.sql"
    And   I send a "PUT" request to "/api/v1/accounting/payment/cancel-remit/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/accounting/payment/cancel-remit/1"
    Then  the response status code should be 401

  Scenario: Attempt to cancel a remit on an non existing transaction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/cancel-remit/1"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Could not load payment with id "1".
    """

  Scenario: Attempt to cancel a remit on an non remitted transaction
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/cancel-remit/3576730"
    Then  the response status code should be 403
    And   the JSON node "message" should be equal to the string
    """
    Cannot cancel remit on payment with id "3576730".
    """

  Scenario: Cancel remit on a transaction successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/accounting/payment/cancel-remit/3577208"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should exist
    And   the JSON node "data->payments" should have 1 element
    And   the JSON node "data->payments[0]->customer_order_payment_id" should be equal to "3577208"
    And   the JSON node "data->payments[0]->can_be_remitted" should be true
