Feature: Get list of suppliers

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "supplier/cpost_suppliers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/suppliers"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for suppliers API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->suppliers" should have 1 elements
    And   the JSON node "data->suppliers[0]" should be identical to
    """
    {
      "supplier_id": 252,
      "name": "AVI Champigny",
      "status": "oui",
      "origin_country_id": 67,
      "discount_rate": 0,
      "franco": 0,
      "delivery_cost": "",
      "supplier_payment_deadline": {
          "payment_deadline": "Comptant",
          "payment_deadline_id": 1
      },
      "discount_payment_deadline": 2,
      "payment": {
          "payment": "Indéfini",
          "supplier_payment_id": 1
      },
      "comment": ""
    }

    """

  Scenario: Check successful response for suppliers API with filter on brand id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers" with body:
    """
    {
      "where": {
        "brand_id": {
          "_eq": "74"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->suppliers" should have 1 elements
    And   the JSON node "data->suppliers[0]" should be identical to
    """
    {
      "supplier_id": 263,
      "name": "FOCAL",
      "status": "oui",
      "origin_country_id": 67,
      "discount_rate": 2,
      "franco": 500,
      "delivery_cost": "",
      "supplier_payment_deadline": {
          "payment_deadline": "Comptant",
          "payment_deadline_id": 1
      },
      "discount_payment_deadline": 2,
      "payment": {
          "payment": "Virement",
          "supplier_payment_id": 2
      },
      "comment": ""
    }
    """

  Scenario: Check successful response for suppliers API with filter on supplier id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers" with body:
    """
    {
      "where": {
        "supplier_id": {
          "_eq": "263"
        }
      }
    }
    """
      Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->suppliers" should have 1 elements
    And   the JSON node "data->suppliers[0]" should be identical to
    """
    {
      "supplier_id": 263,
      "name": "FOCAL",
      "status": "oui",
      "origin_country_id": 67,
      "discount_rate": 2,
      "franco": 500,
      "delivery_cost": "",
      "supplier_payment_deadline": {
          "payment_deadline": "Comptant",
          "payment_deadline_id": 1
      },
      "discount_payment_deadline": 2,
      "payment": {
          "payment": "Virement",
          "supplier_payment_id": 2
      },
      "comment": ""
    }
    """

  # "included_dependencies" Key is accepted but there are no included dependencies yet for this API
  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/suppliers" with body:
    """
    {
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->suppliers" should have 2 elements
