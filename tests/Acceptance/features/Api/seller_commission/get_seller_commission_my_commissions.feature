Feature: Get commissions of current user

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "seller_commission/commission.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/my-commissions?at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/my-commissions?at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/my-commissions?at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Works without "at" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/my-commissions"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist

  Scenario: Retrieve data
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/my-commissions?at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 1 elements
    And   the JSON node "data->commissions[0]" should have the following keys and values
    """
    {
      "full_name": "Gérard MANVUSSA",
      "username": "gege",
      "warehouse_id": 2,
      "warehouse_name": "Champigny 2",
      "warehouse_role": "RETAIL_STORE_SELLER",
      "warehouse_role_level": 1,
      "bonuses_overview": {},
      "global": {},
      "retail_store": {},
      "goals": {},
      "b2c_hifi": {},
      "b2c_image": {},
      "b2b": {},
      "long_duration_warranty": {},
      "catch_up_three_months_bonuses_overview": {},
      "catch_up_three_months_global": {},
      "catch_up_three_months_retail_store": {},
      "catch_up_three_months_goals": {},
      "catch_up_three_months_b2c_hifi": {},
      "catch_up_three_months_b2c_image": {},
      "catch_up_three_months_b2b": {},
      "catch_up_three_months_long_duration_warranty": {}
    }
    """
    And   the JSON node "data->commissions[0]->bonuses_overview" should have the following keys and values
    """
    {
      "b2c_hifi": 436,
      "b2c_image": 0,
      "b2b": null,
      "long_duration_warranty": 0,
      "team": 150,
      "manager": null,
      "total": 586
    }
    """
