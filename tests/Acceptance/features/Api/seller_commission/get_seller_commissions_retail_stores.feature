Feature: Get list of retail stores commissions

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "seller_commission/commission.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 401

  Scenario: Does not work without "type" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "error->exception[0]->message" should be equal to the string
    """
    Parameter "type" of value "NULL" violated a constraint "This value should not be null."
    """

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: A user with only RETAIL_STORE_ALL_COMMISSIONS_READ permission should be rejected
    Given I remove all permissions from user "user2"
    And   I add permission "RETAIL_STORE_ALL_COMMISSIONS_READ" to user "user2"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: A user with only RETAIL_STORE_COMMISSIONS_READ permission should only see his warehouse commissions
    Given I remove all permissions from user "user2"
    And   I add permission "RETAIL_STORE_COMMISSIONS_READ" to user "user2"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 1 elements

  Scenario: A user with both RETAIL_STORE_COMMISSIONS_READ and RETAIL_STORE_ALL_COMMISSIONS_READ permissions should see all retails commissions
    Given I remove all permissions from user "user2"
    And   I add permission "RETAIL_STORE_ALL_COMMISSIONS_READ" to user "user2"
    And   I add permission "RETAIL_STORE_COMMISSIONS_READ" to user "user2"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 2 elements

  Scenario: Works without "at" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist

  Scenario: Retrieve data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=retail_store&at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 2 elements
    And   the JSON node "data->commissions[0]" should have the following keys and values
    """
    {
      "full_name": "Seigneur ADMIN",
      "username": "admin",
      "warehouse_id": 1,
      "warehouse_name": "Champigny",
      "warehouse_role": "RETAIL_STORE_MANAGER",
      "warehouse_role_level": 1,
      "bonuses_overview": {},
      "global": {},
      "retail_store": {},
      "goals": {},
      "b2c_hifi": {},
      "b2c_image": {},
      "b2b": {},
      "long_duration_warranty": {},
      "catch_up_three_months_bonuses_overview": {},
      "catch_up_three_months_global": {},
      "catch_up_three_months_retail_store": {},
      "catch_up_three_months_goals": {},
      "catch_up_three_months_b2c_hifi": {},
      "catch_up_three_months_b2c_image": {},
      "catch_up_three_months_b2b": {},
      "catch_up_three_months_long_duration_warranty": {}
    }
    """
    And   the JSON node "data->commissions[0]->bonuses_overview" should have the following keys and values
    """
    {
      "b2c_hifi": 691,
      "b2c_image": 0,
      "b2b": 102,
      "long_duration_warranty": 150,
      "team": null,
      "manager": 250,
      "total": 1193
    }
    """
    And   the JSON node "data->commissions[0]->global" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 74225,
      "margin_tax_excluded": 26724,
      "markup_rate": 36.004041764904
    }
    """
    And   the JSON node "data->commissions[0]->retail_store" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 134332.44,
      "margin_tax_excluded": 22,
      "turnover_tax_excluded_b2c_commissionable": 15,
      "margin_tax_excluded_b2c_commissionable": 11
    }
    """
    And   the JSON node "data->commissions[0]->goals" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 98340,
      "turnover_tax_excluded_rate": 136.6
    }
    """
    And   the JSON node "data->commissions[0]->b2c_hifi" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 60195,
      "turnover_tax_excluded_commissionable": 44,
      "margin_tax_excluded": 23032,
      "margin_tax_excluded_commissionable": 45,
      "margin_rate_threshold": 36
    }
    """
    And   the JSON node "data->commissions[0]->b2c_image" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 1905,
      "turnover_tax_excluded_commissionable": 66,
      "margin_tax_excluded": 297,
      "margin_tax_excluded_commissionable": 77,
      "margin_rate_threshold": 20
    }
    """
    And   the JSON node "data->commissions[0]->b2b" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 11787,
      "turnover_tax_excluded_commissionable": 88,
      "margin_tax_excluded": 3395,
      "margin_tax_excluded_commissionable": 99,
      "margin_rate_threshold": 23
    }
    """
    And   the JSON node "data->commissions[0]->long_duration_warranty" should have the following keys and values
    """
    {
      "quantity_eligible_products": 38,
      "quantity_eligible_products_commissionable": 20,
      "quantity_warranty_sold": 4,
      "quantity_warranty_sold_commissionable": 3,
      "turnover_tax_excluded": 338,
      "attachment_rate_threshold": 15,
      "attachment_rate_intermediate_threshold": 10
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_bonuses_overview" should have the following keys and values
    """
    {
      "b2c_hifi": 0,
      "b2c_image": 42.26,
      "b2b": 37.84,
      "long_duration_warranty": 0,
      "manager": 12.34,
      "team": 0.22,
      "total": 92.66
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_global" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 88128.25,
      "margin_tax_excluded": 26865.11,
      "markup_rate": 30.484106969105
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_retail_store" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 444444,
      "margin_tax_excluded": 333333,
      "turnover_tax_excluded_b2c_commissionable": 22222,
      "margin_tax_excluded_b2c_commissionable": 111111
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_goals" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 987000,
      "turnover_tax_excluded_rate": 45.*********
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_b2c_hifi" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 61493.38,
      "turnover_tax_excluded_commissionable": 123,
      "margin_tax_excluded": 19981.88,
      "margin_tax_excluded_commissionable": 456,
      "margin_rate_threshold": 36
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_b2c_image" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 12628.62,
      "turnover_tax_excluded_commissionable": 789,
      "margin_tax_excluded": 3138.41,
      "margin_tax_excluded_commissionable": 123,
      "margin_rate_threshold": 20
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_b2b" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 14006.25,
      "turnover_tax_excluded_commissionable": 111,
      "margin_tax_excluded": 3744.82,
      "margin_tax_excluded_commissionable": 222,
      "margin_rate_threshold": 23
    }
    """
    And   the JSON node "data->commissions[0]->catch_up_three_months_long_duration_warranty" should have the following keys and values
    """
    {
      "quantity_eligible_products": 25,
      "quantity_eligible_products_commissionable": 15,
      "quantity_warranty_sold": 0,
      "quantity_warranty_sold_commissionable": 0,
      "turnover_tax_excluded": 0,
      "attachment_rate_threshold": 15,
      "attachment_rate_intermediate_threshold": 10
    }
    """
    And   the JSON node "data->commissions[1]->full_name" should be equal to "Gérard MANVUSSA"
