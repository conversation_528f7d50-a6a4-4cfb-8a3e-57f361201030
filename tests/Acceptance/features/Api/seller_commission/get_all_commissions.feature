Feature: Get list of commissions

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "seller_commission/commission.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?at=2022-07"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: A user with ALL_COMMISSIONS_READ permission should see commissions
    Given I remove all permissions from user "user2"
    And   I add permission "ALL_COMMISSIONS_READ" to user "user2"
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 6 elements

  Scenario: Works without "at" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist

  Scenario: Retrieve data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/all-commissions?at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 6 elements
    And   the JSON node "data->commissions[0]" should have the following keys and values
    """
    {
      "turnover_tax_excluded": 116,
      "product_turnover_tax_excluded": 116,
      "GLD_turnover_tax_excluded": 0,
      "margin_tax_excluded": 31,
      "markup_rate": 27.01,
      "full_name": "Seigneur ADMIN",
      "username": "admin"
    }
    """
    And   the JSON node "data->commissions[1]->full_name" should be equal to "Gérard MANVUSSA"
