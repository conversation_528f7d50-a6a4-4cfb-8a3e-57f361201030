Feature: Get list of call center commissions

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "seller_commission/commission.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=call_center&at=2022-07"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=call_center&at=2022-07"
    Then  the response status code should be 401

  Scenario: Does not work without "type" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "error->exception[0]->message" should be equal to the string
    """
    Parameter "type" of value "NULL" violated a constraint "This value should not be null."
    """

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=call_center&at=2022-07"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Works without "at" param
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=call_center"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist

  Scenario: Retrieve data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/seller-commission/commissions?type=call_center&at=2022-07"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->commissions" should exist
    And   the JSON node "data->commissions" should have 2 elements
    ###   structure is the same as retail_store, so we just test that only call center people are in the response
    And   the JSON node "data->commissions[0]->full_name" should be equal to "Alain TERRIEUR"
    And   the JSON node "data->commissions[1]->full_name" should be equal to "Alex TERRIEUR"
