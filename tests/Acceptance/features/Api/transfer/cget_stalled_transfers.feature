Feature: Retrieve a list of stalled transfers
  As someone identified in the ERP
  I want to fetch the list of stalled transfers

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "product_stock.sql"
    And   I load mysql fixtures from file "transfer/transfer.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/stalled-transfers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/stalled-transfers"
    Then  the response status code should be 401

  Scenario: Get list of stalled transfers
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/stalled-transfers"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->stalled_transfers" should have 1 elements
    And   the JSON node "data->stalled_transfers" should have the following keys and values
      """
      [
        {
          "transfers": "[{\"weight\": 0.85, \"transfer_id\": 3333333}, {\"weight\": 1.70, \"transfer_id\": 4444444}]",
          "carrier_product_id": null,
          "carrier_product_label": "Express",
          "sent_from_id": "1",
          "sent_from_name": "Champigny",
          "target_id": "2",
          "target_name": "Paris 8e",
          "total_weight": "2.55"
        }
      ]
      """
