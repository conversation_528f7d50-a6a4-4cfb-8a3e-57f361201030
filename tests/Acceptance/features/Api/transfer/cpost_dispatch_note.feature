Feature: Retrieve a filtered list of dispatch notes
    As someone identified in the ERP
    I want to list and filter the transfers available in the system

    @clear-database
    Scenario: Test without authorization
        When  I load mysql fixtures from file "users.sql"
        And   I load mysql fixtures from file "transfer/dispatch_note.sql"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/dispatch-notes"
        Then  the response status code should be 401

    Scenario: Test with a non valid authorization
        When  I add "Authorization" header equal to "Bearer unknown-token"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/dispatch-notes"
        Then  the response status code should be 401

    Scenario: Get list of transfers without filters
        When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/dispatch-notes" with body:
        """
            {
                "where": {
                    "is_sent": {
                        "_eq": 1
                    },
                    "creation_date": {
                        "_gt": "2024-04-26"
                    }
                }
            }
            """
        Then  the response status code should be 200
        And   the response should be in JSON
        And   the JSON node "status" should be equal to the string "success"
        And   the JSON node "data" pager should have a total of "5"
        And   the JSON node "data" pager should have a limit of "50"
        And   the JSON node "data->dispatch_notes" should have 5 elements
        And   the JSON node "data->dispatch_notes" should have the following keys and values
        """
            [
                {
                    "id": 7604,
                    "creation_date": "2024-05-03 15:48:59",
                    "is_sent": 1
                },
                {
                    "id": 7603,
                    "creation_date": "2024-05-02 15:51:14",
                    "is_sent": 1
                },
                {
                    "id": 7602,
                    "creation_date": "2024-04-30 15:39:58",
                    "is_sent": 1
                },
                {
                    "id": 7601,
                    "creation_date": "2024-04-29 15:28:24",
                    "is_sent": 1
                },
                {
                    "id": 7600,
                    "creation_date": "2024-04-26 14:38:46",
                    "is_sent": 1
                }
            ]
        """



