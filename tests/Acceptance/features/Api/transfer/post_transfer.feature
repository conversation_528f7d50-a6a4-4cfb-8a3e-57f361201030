Feature: manually create transfer
  In order to manage transfers through API
  As a user
  I need to be able to create products' transfer from a warehouse to another

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "product_stock.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Create a transfer with a final product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer_id" should be equal to the number "2222223"

  Scenario: Create a transfer with a package
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 13895,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer_id" should be equal to the number "2222224"

  Scenario: Create an empty transfer
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer_id" should be equal to the number "2222225"

  Scenario: Create a transfer failed if contains unknown warehouse_from
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 999,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 500
    And   the JSON node "message" should contain 'Transfer creation has failed'

  Scenario: Create a transfer failed if contains unknown product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2,
      "products": [
        {
          "product_id": 99999999,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 500
    And   the JSON node "message" should be equal to 'Transfer creation has failed: Product with key "99999999" not found.'
