Feature: Retrieve a dispatch note file
  As someone identified in the ERP
  I want to download a dispatch note

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "transfer/dispatch_note.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/dispatch-note/7602"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/dispatch-note/7602"
    Then  the response status code should be 401

  Scenario: Test with a non existing dispatch note
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/dispatch-note/666"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Dispatch note not found with id 666"

  Scenario: Get a dispatch note file
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/dispatch-note/7602"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the response should contain "content"