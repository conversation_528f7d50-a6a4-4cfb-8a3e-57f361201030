Feature: manually update transfer
  In order to manage transfers through API
  As a user
  I need to be able to add or update products of a transfer

  @clear-database
  Scenario: Create an empty transfer
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "product_stock.sql"
    And I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/transfer" with body:
    """
    {
      "warehouse_from": 1,
      "warehouse_to": 2
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer_id" should be equal to the number "2222223"


  Scenario: Test without authorization
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

 Scenario: Combine quantity and add_quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "quantity": 1,
          "add_quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 400


  Scenario: update a transfer with a final product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->warehouse_from" should have the following keys and values
    """
    {
      "code": "03",
      "name": "Champigny",
      "warehouse_id": 1
    }
    """
    And   the JSON node "data->transfer->warehouse_to" should have the following keys and values
    """
    {
      "code": "02",
      "name": "Paris 8e",
      "warehouse_id": 2
    }
    """
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
      [
        {
          "product": {
            "sku": "ARCAMRBLINKNR",
            "product_key": 81078,
            "description": "Arcam rBlink"
          },
          "quantity": 1,
          "delivery_note_id": null,
          "transfer_product_key": 222223
        }
      ]
    """

  Scenario: update a transfer on adding quantity to a final product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 81078,
          "add_quantity": 2
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
      [
        {
          "product": {
            "sku": "ARCAMRBLINKNR",
            "product_key": 81078,
            "description": "Arcam rBlink"
          },
          "quantity": 3,
          "delivery_note_id": null,
          "transfer_product_key": 222223
        }
      ]
    """

  Scenario: Update a transfer with a package
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
     {
      "products": [
        {
          "product_key": 13895,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
    [
      {
        "product": {
          "sku": "ARCAMRBLINKNR",
          "product_key": 81078,
          "description": "Arcam rBlink"
        },
        "quantity": 2,
        "delivery_note_id": null,
        "transfer_product_key": 222223
      },
      {
        "product": {
          "reference": "LBCLD25BP",
          "product_key": 81123,
          "description": "La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130"
        },
        "quantity": 1,
        "delivery_note_id": null,
        "transfer_product_key": 222225
      }
    ]
    """

  Scenario: Update a transfer with a package
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
     {
      "products": [
        {
          "product_key": 13895,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
    [
      {
        "product": {
          "sku": "ARCAMRBLINKNR",
          "product_key": 81078,
          "description": "Arcam rBlink"
        },
        "quantity": 2,
        "delivery_note_id": null,
        "transfer_product_key": 222223
      },
      {
        "product": {
          "reference": "LBCLD25BP",
          "product_key": 81123,
          "description": "La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130"
        },
        "quantity": 1,
        "delivery_note_id": null,
        "transfer_product_key": 222225
      }
    ]
    """

  Scenario: Update a transfer product with a code128
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
     {
      "products": [
        {
          "product_key": 10780081078,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
    [
      {
        "product": {
          "sku": "ARCAMRBLINKNR",
          "product_key": 81078,
          "description": "Arcam rBlink"
        },
        "quantity": 1,
        "delivery_note_id": null,
        "transfer_product_key": 222223
      },
      {
        "product": {
          "reference": "LBCLD25BP",
          "product_key": 81123,
          "description": "La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130"
        },
        "quantity": 1,
        "delivery_note_id": null,
        "transfer_product_key": 222225
      }
    ]
    """


  Scenario: add product with a ean (fix 128)
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/1222222" with body:
    """
     {
      "products": [
        {
          "product_key": 1191200128416,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->transfer->transfered_products" should have the following keys and values
    """
    [
      {
        "product": {
          "sku": "LBCLD25BP",
          "product_key": 81123,
          "description": "La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130"
        },
        "quantity": 1,
        "delivery_note_id": null,
        "transfer_product_key": 222230
      }
    ]
    """


  Scenario: Update a transfer failed if contains unknown product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/transfer/2222223" with body:
    """
    {
      "products": [
        {
          "product_key": 99999999,
          "quantity": 1
        }
      ]
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 500
    And   the JSON node "message" should be equal to 'Transfer update has failed: Product with key "99999999" not found.'
