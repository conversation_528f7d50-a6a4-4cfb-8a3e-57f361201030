Feature: Retrieve a filtered list of transfer
    As someone identified in the ERP
    I want to list and filter the transfers available in the system

    @clear-database
    Scenario: Test without authorization
        When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
        And   I load mysql fixtures from file "product_stock.sql"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/transfers"
        Then  the response status code should be 401

    Scenario: Test with a non valid authorization
        When  I add "Authorization" header equal to "Bearer unknown-token"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/transfers"
        Then  the response status code should be 401

    Scenario: Get list of transfers without filters
        When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/transfers"
        Then  the response status code should be 200
        And   the response should be in JSON
        And   the JSON node "status" should be equal to the string "success"
        And   the JSON node "data" pager should have a total of "3"
        And   the JSON node "data" pager should have a limit of "50"
        And   the JSON node "data->transfers" should have 3 elements
        And   the JSON node "data->transfers" should have the following keys and values
            """
            [
                {
                    "transfer_id": 1222222,
                    "created_at": "2022-12-22 22:22:22",
                    "closed_at": null,
                    "created_by": "backoffice",
                    "type": "demo",
                    "status": "tmp",
                    "comment": "commentaire 1222222",
                    "shipment_method_id": null,
                    "customer_order_id": null,
                    "warehouse_from": {
                        "code": "03",
                        "name": "Champigny",
                        "warehouse_id": 1
                    },
                    "warehouse_to": {
                        "code": "02",
                        "name": "Paris 8e",
                        "warehouse_id": 2
                    },
                    "transfered_products": []
                },
                {
                    "transfer_id": 2222222,
                    "created_at": "2022-12-22 22:22:22",
                    "closed_at": null,
                    "created_by": "backoffice",
                    "type": "demo",
                    "status": "tmp",
                    "comment": "commentaire 222222",
                    "shipment_method_id": null,
                    "customer_order_id": null,
                    "warehouse_from": {
                        "code": "03",
                        "name": "Champigny",
                        "warehouse_id": 1
                    },
                    "warehouse_to": {
                        "code": "02",
                        "name": "Paris 8e",
                        "warehouse_id": 2
                    },
                    "transfered_products": [
                        {
                            "product": {
                                "reference": "ARCAMRBLINKNR",
                                "product_id": 81078,
                                "description": "Arcam rBlink"
                            },
                            "quantity": 3,
                            "delivery_note_id": null,
                            "transfer_product_id": 222222
                        }
                    ]
                },
                {
                    "transfer_id": 1111111,
                    "created_at": "2011-11-11 11:11:11",
                    "closed_at": "2011-11-11 12:00:00",
                    "created_by": "backoffice",
                    "type": "demo",
                    "status": "tmp",
                    "comment": "commentaire 111111",
                    "shipment_method_id": null,
                    "customer_order_id": null,
                    "warehouse_from": {
                        "code": "03",
                        "name": "Champigny",
                        "warehouse_id": 1
                    },
                    "warehouse_to": {
                        "code": "02",
                        "name": "Paris 8e",
                        "warehouse_id": 2
                    },
                    "transfered_products": [
                        {
                            "product": {
                                "reference": "ARCAMRBLINKNR",
                                "product_id": 81078,
                                "description": "Arcam rBlink"
                            },
                            "quantity": 1,
                            "delivery_note_id": null,
                            "transfer_product_id": 111111
                        },
                        {
                            "product": {
                                "reference": "BWCCM74",
                                "product_id": 128416,
                                "description": "B&W BWCCM74"
                            },
                            "quantity": 2,
                            "delivery_note_id": null,
                            "transfer_product_id": 111112
                        }
                    ]
                }
            ]
            """

   Scenario: Get filtered transfers
        When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
        And   I add "Content-Type" header equal to "application/json"
        And   I send a "POST" request to "/api/v1/transfers" with body:
            """
            {
                "where": {
                    "transfer_id": {
                        "_eq": "1111111"
                    }
                }
            }
            """
        Then  the response status code should be 200
        And   the response should be in JSON
        And   the JSON node "status" should be equal to the string "success"
        And   the JSON node "data" pager should have a total of "1"
