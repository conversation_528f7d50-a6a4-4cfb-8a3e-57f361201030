Feature: Retrieve an article history by an id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_events.sql"
    And   I send a "POST" request to "/api/v1/system-events"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/system-events"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/system-events" with body:
    """
    {
      "where" : {
        "main_id": {
          "_eq": 99999
        }
       }
    }
    """
    Then the response status code should be 200
    And  the response should be in JSON
    And  the JSON node "status" should be equal to the string "success"
    And  the JSON node "data->system_events" should be identical to
    """
    {}
    """


  Scenario: Retrieve an article events by its ID
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/system-events" with body:
    """
    {
      "limit": 2,
      "where" : {
        "main_id": {
          "_eq": 81123
        }
       }
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a limit of "2"
    And   the JSON node "data->system_events" should have 2 elements
    And   the JSON node "data->system_events[1]" should be identical to
    """
    {
      "id_for_type": "601",
      "created_at": "2021-10-04 12:45:21",
      "type": "article.comment.legacy",
      "payload": {
        "_rel": {
          "article": 81123
        },
        "data": {
          "message": "PF 2272,80"
        },
        "meta": {
          "created_by": {
            "user_id": 1,
            "lastname": "Seigneur",
            "username": "admin",
            "firstname": "Admin"
          }
        }
      },
      "main_id": 81123,
      "emitter": null
    }
    """

  Scenario: Filter events for type all
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/system-events"
    """
    {
     "where": {
        "type": {
          "_eq": "article.comment.legacy"
        },
        "main_id": {
          "_eq": 81123
        }
     }
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->system_events" should have 2 elements
    And   the JSON node "data->system_events[1]->id_for_type" should be equal to 601

  Scenario: Filter sales channel events
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/system-events"
    """
    {
     "where": {
        "main_id": {
          "_eq": 81123
        }
     },
     "included_dependencies" : {
        "sales_channel_id": {
          "_eq": 1
        }
     }
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->system_events" should have 1 elements
    And   the JSON node "data->system_events[0]" should be identical to
    """
    {
      "id_for_type": "606",
      "created_at": "2018-09-12 10:39:11",
      "type": "article.update.sales_channel_product",
      "payload": {
          "_rel": {
              "article": 81123,
              "sales_channel_id": 1
          },
          "data": {
              "is_active": {
                  "new": true,
                  "old": false
              }
          },
          "meta": {
              "created_by": {
                  "user_id": 1,
                  "lastname": "Seigneur",
                  "username": "admin",
                  "firstname": "Admin"
              }
          }
      },
      "emitter": null,
      "main_id": 81123
    }
    """
