Feature: Update CMS system parameters via an RPC API
  In order to manage the system parameters of the CMS app
  As an administrator
  I need to be able to update some parameters via an API in CMS

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I send a "POST" request to "/api/v1/system-administration/cms-parameters"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/system-administration/cms-parameters"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/system-administration/cms-parameters"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Check response when updates failed in remote server
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bo-cms" "system_parameter:update_given_paths" to respond with:
        """
{
    "status": true,
    "success": false
}
        """
    And   I send a "POST" request to "/api/v1/system-administration/cms-parameters" with body:
    """
{
    "parameters": [
        { "name": "abandoned_cart.hours_since_last_basket", "value": "32" }
    ]
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"
    And   the JSON node "data->success" should be false
    And   the JSON node "data->updated" should not exist

  Scenario: Check response when updates are successful
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bo-cms" "system_parameter:update_given_paths" to respond with:
        """
{
    "status": true,
    "success": true,
    "updated": [
        { "name": "namespace.dummy1", "value": "32" },
        { "name": "namespace.dummy2", "value": "45" }
    ]
}
        """
    And   I send a "POST" request to "/api/v1/system-administration/cms-parameters" with body:
    """
{
    "parameters": [
        { "name": "namespace.dummy1", "value": "32" },
        { "name": "namespace.dummy2", "value": "45" }
    ]
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"
    And   the JSON node "data->success" should be true
    And   the JSON node "data->updated" should have 2 elements
    And   the JSON nodes should be equal to:
      | data.updated[0].name  | namespace.dummy1 |
      | data.updated[0].value | 32 |
