Feature: Get CMS system parameters via an RPC API
  In order to manage the system parameters of the CMS app
  As an administrator
  I need to be able to retrieve the asked system parameters

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I send a "GET" request to "/api/v1/system-administration/cms-parameters/abandoned_cart"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/system-administration/cms-parameters/abandoned_cart"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/system-administration/cms-parameters/abandoned_cart"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Fetch system parameters successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bo-cms" "system_parameter:fetch_by_path" to respond with:
        """
{
    "status": true,
    "parameters": [
        {
            "name": "abandoned_cart.created_at_threshold",
            "value": "2021-03-06 11:49:28.924329+00",
            "description": "date a partir de laquelle un panier est éligible a la relance"
        },
        {
            "name": "abandoned_cart.days_since_last_customer_order",
            "value": "50",
            "description": "Temps écoulé, en jour, depuis la dernière commande avant relance"
        },
        {
            "name": "abandoned_cart.hours_since_last_basket",
            "value": "32",
            "description": "Temps écoulé, en heure, depuis la dernière mise à jour du panier avant relance"
        },
        {
            "name": "abandoned_cart.total_price_max_amount_threshold",
            "value": "1005",
            "description": "montant maximum du panier pour relance"
        },
        {
            "name": "abandoned_cart.total_price_min_amount_threshold",
            "value": "5",
            "description": "montant minimum du panier pour relance"
        }
    ]
}
        """
    And   I send a "GET" request to "/api/v1/system-administration/cms-parameters/abandoned_cart"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "success"
    And   the JSON node "data[1]->name" should be equal to "abandoned_cart.days_since_last_customer_order"
    And   the JSON node "data[1]->value" should be equal to "50"
    And   the JSON node "data[1]->description" should be equal to "Temps écoulé, en jour, depuis la dernière commande avant relance"
