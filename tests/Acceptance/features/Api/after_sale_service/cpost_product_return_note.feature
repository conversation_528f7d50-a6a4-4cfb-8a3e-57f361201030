Feature: Get list product return notes

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "after_sale_service/product_return_notes.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/product-return-notes"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/product-return-notes"
    Then  the response status code should be 401

  Scenario: Get list of product return notes by product return notes id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/product-return-notes" with body:
    """
    {
      "where": {
        "product_return_note_id": {
          "_eq": 83938
        }
      }
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->product_return_notes" should have 1 elements
    And   the JSON node "data->product_return_notes[0]->product_return_note_id" should be equal to "83938"
    And   the JSON node "data->product_return_notes[0]->email" should be equal to "<EMAIL>"
    And   the JSON node "data->product_return_notes[0]->lastname" should be equal to "Abegg"
    And   the JSON node "data->product_return_notes[0]->firstname" should be equal to "Mark"
    And   the JSON node "data->product_return_notes[0]->address" should be equal to "23 rue richard lenoir"
    And   the JSON node "data->product_return_notes[0]->postal_code" should be equal to "75011"
    And   the JSON node "data->product_return_notes[0]->city" should be equal to "Paris"
    And   the JSON node "data->product_return_notes[0]->weight_g" should be equal to "8450"
