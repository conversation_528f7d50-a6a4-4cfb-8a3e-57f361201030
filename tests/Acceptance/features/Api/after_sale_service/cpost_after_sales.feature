Feature: Get list after sales services
  In order to manage after sales through API
  As a user
  I need to be able to get list of after sales

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "after_sale_service/cpost_after_sale_services.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/after-sale-services"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/after-sale-services"
    Then  the response status code should be 401

  Scenario: Get list of after sales by customer
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/after-sale-services" with body:
    """
    {
      "where": {
        "_and": [
          {
            "customer_id": {
              "_eq": 1
            }
          }
        ]
      },
      "limit":5,
      "included_dependencies": ["article"]
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->after_sale_services" should have 1 elements
    And   the JSON node "data->after_sale_services[0]->after_sale_service_id" should contain 17293
    And   the JSON node "data->after_sale_services[0]->product_return_note_id" should contain 83944
    And   the JSON node "data->after_sale_services[0]->customer_id" should contain "1"
    And   the JSON node "data->after_sale_services[0]->origin" should contain "son-video.com"
    And   the JSON node "data->after_sale_services[0]->customer_order_id" should contain "1"
    And   the JSON node "data->after_sale_services[0]->rma_number" should exist
    And   the JSON node "data->after_sale_services[0]->created_at" should contain "2021-06-27 23:48:06"
    And   the JSON node "data->after_sale_services[0]->sent_at" should contain "2021-06-25 11:54:27"
    And   the JSON node "data->after_sale_services[0]->reminded_at" should be null
    And   the JSON node "data->after_sale_services[0]->status" should be equal to "on_going"
    And   the JSON node "data->after_sale_services[0]->article" should not be null
    And   the JSON node "data->after_sale_services[0]->article->article_id" should contain "13895"
    And   the JSON node "data->after_sale_services[0]->article->sku" should contain "NORSTCL81123M"
    And   the JSON node "data->after_sale_services[0]->article->name" should contain "CL250 Classic 2,5 mm2 (25 m)"
    And   the JSON node "data->after_sale_services[0]->article->brand_name" should contain "NorStone"
    And   the JSON node "data->after_sale_services[0]->article->image" should be equal to "https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg"

  Scenario: Get list of after sales by customer without loading its optional dependencies
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/after-sale-services" with body:
    """
    {
      "where": {
        "_and": [
          {
            "customer_id": {
              "_eq": 1
            }
          }
        ]
      },
      "limit":5
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->after_sale_services" should have 1 elements
    And   the JSON node "data->after_sale_services[0]->after_sale_service_id" should contain 17293
    And   the JSON node "data->after_sale_services[0]->product_return_note_id" should contain 83944
    And   the JSON node "data->after_sale_services[0]->customer_order_id" should contain "1"
    And   the JSON node "data->after_sale_services[0]->customer_id" should contain "1"
    And   the JSON node "data->after_sale_services[0]->origin" should contain "son-video.com"
    And   the JSON node "data->after_sale_services[0]->rma_number" should exist
    And   the JSON node "data->after_sale_services[0]->created_at" should contain "2021-06-27 23:48:06"
    And   the JSON node "data->after_sale_services[0]->sent_at" should contain "2021-06-25 11:54:27"
    And   the JSON node "data->after_sale_services[0]->reminded_at" should be null
    And   the JSON node "data->after_sale_services[0]->status" should be equal to "on_going"
    And   the JSON node "data->after_sale_services[0]->article" should be null

  Scenario: Can search by product_return_note_id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/after-sale-services" with body:
    """
    {
      "where": {
        "_and": [
          {
            "product_return_note_id": {
              "_eq": 83944
            }
          }
        ]
      },
      "limit":5
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->after_sale_services" should have 1 elements
    And   the JSON node "data->after_sale_services[0]->after_sale_service_id" should contain 17293
    And   the JSON node "data->after_sale_services[0]->product_return_note_id" should contain 83944
