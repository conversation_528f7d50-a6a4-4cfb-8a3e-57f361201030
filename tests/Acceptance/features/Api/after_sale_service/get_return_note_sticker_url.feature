Feature: Get url for sticker for product return note

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "after_sale_service/product_return_notes.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/product-return-note/83938/sticker-url"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/product-return-note/83938/sticker-url"
    Then  the response status code should be 401

  Scenario: Test on a non existing product return notes id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/product-return-note/999/sticker-url"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Product return note not found with id "999"'

  Scenario: Test success
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/product-return-note/83938/sticker-url"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->url" should have 1 elements
    And   the JSON node "data->url" should be equal to "https://retours.mondialrelay.com/d/FRSONVID/?SiteId=RetourErp&CustomReference=83938&Email=9j8hhz1b8b6bfss%40marketplace.amazon.fr&Adress1=Abegg&Adress2=Mark&Adress3=23+rue+richard+lenoir&PostCode=75011&City=Paris&WeightInGramm=8450.000"
