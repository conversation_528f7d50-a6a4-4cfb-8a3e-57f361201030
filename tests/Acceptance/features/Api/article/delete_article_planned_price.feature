Feature: Delete an article planned price

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_planned_price.sql"
    And   I send a "DELETE" request to "/api/v1/article/planned-price/2"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "DELETE" request to "/api/v1/article/planned-price/2"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article/planned-price/2"
    Then  the response status code should be 403

  Scenario: Delete an article planned price
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article/planned-price/2"
    Then  the response status code should be 200
