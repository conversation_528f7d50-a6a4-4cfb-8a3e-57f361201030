Feature: Update a article safety stock

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "article/put_article_safety_stock_threshold.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/warehouse/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/warehouse/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/warehouse/1" with body:
    """
    {
      "safety_stock_threshold": 3
    }
    """
    Then  the response status code should be 403

  Scenario: Test with token linked to an account which do have permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/warehouse/1" with body:
    """
    {
      "safety_stock_threshold": 3
    }
    """
    Then  the response status code should be 204

  Scenario: Test a stock entry that has the same value
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/warehouse/1" with body:
    """
    {
      "safety_stock_threshold": 3
    }
    """
    Then  the response status code should be 208
