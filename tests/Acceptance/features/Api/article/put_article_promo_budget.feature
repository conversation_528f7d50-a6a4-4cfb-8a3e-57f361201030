Feature: Update an article planned price

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I load mysql fixtures from file "article/article_promo_budget.sql"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/1" with body:
    """
    {"amount": 2, "start_at": "2024-08-01 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with unknown article promo budget
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/666" with body:
    """
    {"amount": 2, "start_at": "2024-08-01 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 404
    And   the response should be in JSON

  Scenario: Failed to update an article promo budget
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/1" with body:
    """
    {"amount": 5, "start_at": "2024-08-03 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "The article promo budget overlaps another promo budget"

  Scenario: Successfully update article promo budget
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72215/promo-budget/1" with body:
    """
    {"amount": 5, "start_at": "2024-06-03 12:00:00", "end_at": "2024-06-08 12:00:00"}
    """
    Then  the response status code should be 204
