Feature: Update a article comment

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "article/put_article_comment.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/comment/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/comment/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/comment/1" with body:
    """
    {
      "data": {
        "is_active": false
      }
    }
    """
    Then  the response status code should be 403

  Scenario: Test with a non-existing article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/99999/comment/1" with body:
    """
    {
      "data": {
        "is_active": false
      }
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    No article found with id 99999.
    """

  Scenario: Test with a non-existing comment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/comment/9999" with body:
    """
    {
      "data": {
        "is_active": false
      }
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article comment not found.
    """

  Scenario: Test with an comment which do not belong to the article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/comment/4" with body:
    """
    {
      "data": {
        "is_active": false
      }
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    The comment is not related to the article.
    """

  Scenario: Test successful update
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/article/81123/comment/1" with body:
    """
    {
      "data": {
        "is_active": false
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->updated" should be equal to the number "1"

