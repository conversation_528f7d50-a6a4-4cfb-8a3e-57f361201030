Feature: Retrieve packaged articles

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_packaged_articles.sql"
    And   I send a "GET" request to "/api/v1/package/123/packaged-articles"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/package/123/packaged-articles"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/package/666/packaged-articles"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with id "666".
    """

  Scenario: Test with a regular article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/package/72215/packaged-articles"
    Then  the JSON node "code" should be equal to the number "500"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Not a package.
    """

  Scenario: Retrieve packaged articles
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/package/72218/packaged-articles"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data" should have "2" elements
    And   the JSON node "data[0]" should be identical to
    """
    {
      "packaged_article_id": 95145,
      "package_id": 72218,
      "article_id": 72215,
      "sku": "ELIPSPRESTIGE2I",
      "name": "Prestige 2i Calvados",
      "short_description": "Elipson Prestige 2i Calvados (la paire)",
      "image": "/images/ui/uiV3/graphics/no-img-300.png",
      "quantity": 2,
      "stock": 26,
      "stock_available": 0,
      "unit_selling_price": 279.50,
      "delay": 0,
      "status": "oui"
    }
    """
    And   the JSON node "data[1]" should be identical to
    """
    {
      "packaged_article_id": 95146,
      "package_id": 72218,
      "article_id": 72216,
      "sku": "ELIPSPRESTIGE_C_2I",
      "name": "Prestige C2i Calvados",
      "short_description": "Elipson Prestige C2i Calvados",
      "image": "/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg",
      "quantity": 1,
      "stock": 15,
      "stock_available": 12,
      "unit_selling_price": 199,
      "delay": 30,
      "status": "oui"
    }
    """
