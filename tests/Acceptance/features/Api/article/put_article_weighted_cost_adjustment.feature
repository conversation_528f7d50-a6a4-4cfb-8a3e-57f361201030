Feature: Update a weighted cost adjustment

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_weighted_cost_adjustment.sql"
    And   I send a "PUT" request to "/api/v1/article/81123/weighted-cost-adjustment/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article/81123/weighted-cost-adjustment/1"
    Then  the response status code should be 401

  Scenario: Test on a non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/666666/weighted-cost-adjustment/1" with body:
    """
    {
      "meta":{}
    }
    """
    Then  the response status code should be 404

  Scenario: Test on a non existing adjustment
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/81123/weighted-cost-adjustment/666" with body:
    """
    {
      "meta":{}
    }
    """
    Then  the response status code should be 404

  Scenario: Adjust weighted cost
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/81123/weighted-cost-adjustment/1" with body:
    """
    {
      "meta":{
        "commentaire":"bonjour",
        "date_range": {
            "from": "2023-05-10",
            "to": "2023-05-20"
          }
        }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"
