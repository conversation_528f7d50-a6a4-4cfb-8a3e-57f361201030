Feature: Update an article replacement

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_replacement.sql"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/BWCCM74"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/BWCCM74"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/BWCCM74"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with an non-existing replaced article that returns are readable error message in the JSON response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/NOT-EXISTING/replaced-by/BWCCM74"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Le produit remplacé "NOT-EXISTING" n'existe pas
    """

  Scenario: Test with an non-existing replaced by article that returns are readable error message in the JSON response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/NOT-EXISTING-BY"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Le produit de remplacement "NOT-EXISTING-BY" n'existe pas
    """

  Scenario: Cannot replace an article by itself
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/ARCAMRBLINKNR"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Un produit ne peut pas être remplacé par lui même
    """

  Scenario: One article cannot be replaced if it already has a replacement
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/ARCAMRBLINKNR/replaced-by/BWCCM74"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Le produit que vous souhaitez remplacer a déjà un produit de remplacement
    """

  Scenario: Avoid circular reference
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/81085/replaced-by/81078"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Référence circulaire : "81085" remplace "81078"
    """

  Scenario: Update successfully
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article-replacement/NORSTCL81123M/replaced-by/STTODO"
    Then  the response status code should be 204
