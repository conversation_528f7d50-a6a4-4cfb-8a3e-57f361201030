Feature: Retrieve an article by an id or sku
  In order to obtain some information about an article through the API
  As a user
  I need to be able to retrieve identify an article using a sku or an id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku.sql"
    And   I send a "GET" request to "/api/v1/article/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/article/123"
    Then  the response status code should be 401

  Scenario: Test with non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article/666"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with id or sku "666".
    """

  Scenario: Retrieve an article by its ID
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article/81123"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article" should exist
    And   the JSON node "data->article->product_id" should be equal to "81123"
    And   the JSON node "data->article->sku" should be equal to "LBCLD25BP"
    And   the JSON node "data->article->subcategory_id" should be equal to "144"
    And   the JSON node "data->article->created_at" should be equal to "2013-02-21"
    And   the JSON node "data->article->updated_at" should be equal to "2019-07-26 00:34:00"
    And   the JSON node "data->article->status" should be equal to "oui"
    And   the JSON node "data->article->brand_name" should be equal to "La Boite Concept"
    And   the JSON node "data->article->brand_id" should be equal to "959"
    And   the JSON node "data->article->selling_price" should be equal to "389.00"
    And   the JSON node "data->article->pvgc" should be equal to "389.00"
    And   the JSON node "data->article->ecotax" should be equal to "0.00"
    And   the JSON node "data->article->sorecop" should be equal to "0.00"
    And   the JSON node "data->article->weight" should be equal to "8.450"
    And   the JSON node "data->article->supplier_id" should be equal to "400"
    And   the JSON node "data->article->supplier_reference" should be equal to "LD-F-25mm-N-P"
    And   the JSON node "data->article->stock_quantity" should be equal to "2"
    And   the JSON node "data->article->basket_description" should be equal to "Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130"
    And   the JSON node "data->article->short_description" should be equal to "Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)"
    And   the JSON node "data->article->initial_selling_price" should be equal to "389.00"
    And   the JSON node "data->article->is_package" should be equal to "0"
    And   the JSON node "data->article->code128" should be equal to "11230081123"
    And   the JSON node "data->article->sku_havre" should be null

  Scenario: Retrieve an article by its SKU
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/article/NORSTCL81123M"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article" should exist
    And   the JSON node "data->article->product_id" should be equal to "13895"
    And   the JSON node "data->article->sku" should be equal to "NORSTCL81123M"
    And   the JSON node "data->article->subcategory_id" should be equal to "56"
    And   the JSON node "data->article->created_at" should be equal to "2003-09-22"
    And   the JSON node "data->article->updated_at" should be equal to "2019-09-04 00:34:01"
    And   the JSON node "data->article->status" should be equal to "oui"
    And   the JSON node "data->article->brand_name" should be equal to "NorStone"
    And   the JSON node "data->article->brand_id" should be equal to "520"
    And   the JSON node "data->article->selling_price" should be equal to "45.00"
    And   the JSON node "data->article->pvgc" should be equal to "62.50"
    And   the JSON node "data->article->ecotax" should be equal to "0.50"
    And   the JSON node "data->article->sorecop" should be equal to "0.00"
    And   the JSON node "data->article->weight" should be equal to "2.000"
    And   the JSON node "data->article->supplier_id" should be equal to "1"
    And   the JSON node "data->article->supplier_reference" should be null
    And   the JSON node "data->article->stock_quantity" should be equal to "235"
    And   the JSON node "data->article->basket_description" should be equal to "Câble d'enceintes Norstone Audio CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 25 m"
    And   the JSON node "data->article->short_description" should be equal to "NorStone CL250 Classic (25 m)"
    And   the JSON node "data->article->initial_selling_price" should be equal to "0.00"
    And   the JSON node "data->article->is_package" should be equal to "1"
    And   the JSON node "data->article->code128" should be equal to "18950013895"
    And   the JSON node "data->article->sku_havre" should be null
