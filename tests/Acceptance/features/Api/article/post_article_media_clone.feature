Feature: clone article media

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/post_article_media_upload.sql"
    And   I send a "POST" request to "/api/v1/article/81123/media/clone-to/NORSTCL81123M"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/81123/media/clone-to/NORSTCL81123M"
    Then  the response status code should be 401

  Scenario: Test with a non-existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And I send a POST request to "/api/v1/article/666/media/clone-to/NORSTCL81123M" with body:
    """
    {
      "selected_media_ids": []
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON

  # Only basic API tests are done here,
  # As the API only return 204 status upon a successful cloning process
  # Theres no need to duplicate all the tests here
  # The unit tests handles all the mocking stuff
