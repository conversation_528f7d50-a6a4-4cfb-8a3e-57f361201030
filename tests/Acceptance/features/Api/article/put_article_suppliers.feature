Feature: Update article information (supplier)

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/put_article.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"supplier_id": 1, "supplier_reference" : "any", "mininum_order_quantity": 1}, "scope": "suppliers"}
    """
    Then  the response status code should be 403
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"

  Scenario: Test on a non existing article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/666" with body:
    """
    {"data": {"supplier_id": 1, "supplier_reference" : "any", "mininum_order_quantity": 1}, "scope": "suppliers"}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "No article found with id 666."

  Scenario: Successfully update article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"supplier_id": 400, "supplier_reference": null, "mininum_order_quantity": 1}, "scope": "suppliers"}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 1}
    """

  Scenario: Successfully update article with new reference
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"supplier_id": 162, "supplier_reference": "RBLINK2", "mininum_order_quantity": 1}, "scope": "suppliers"}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 1}
    """
