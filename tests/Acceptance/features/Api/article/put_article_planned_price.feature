Feature: Update an article planned price

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_planned_price.sql"
    And   I send a "PUT" request to "/api/v1/article/72216/planned-price/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article/72216/planned-price/1"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/article/72216/planned-price/4" with body:
    """
    {
      "selling_price": 174.50,
      "exit_selling_price": 199.99,
      "starts_at": "2024-10-01 10:35:41",
      "ends_at": "2024-11-01 10:35:41",
      "sales_channel_ids": [1, 2, 3]
    }
    """
    Then  the response status code should be 403


  <PERSON><PERSON><PERSON>: Successfully update article planned price
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72216/planned-price/4" with body:
    """
    {
      "selling_price": 174.50,
      "exit_selling_price": 199.99,
      "starts_at": "2024-10-01 10:35:41",
      "ends_at": "2024-11-01 10:35:41",
      "sales_channel_ids": [1, 2, 3]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"
