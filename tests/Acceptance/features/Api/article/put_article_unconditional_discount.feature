Feature: Update an article unconditional discount
  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I load mysql fixtures from file "article/article_unconditional_discount.sql"
    And   I send a "PUT" request to "/api/v1/article/72215/unconditional-discount"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article/72215/unconditional-discount"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/72215/unconditional-discount" with body:
    """
    {"amount": 15}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with unknown article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/666/unconditional-discount" with body:
    """
    {"amount": 15}
    """
    Then  the response status code should be 404
    And   the response should be in JSON

  Scenario: Failed to update an article promo budget
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72215/unconditional-discount" with body:
    """
    {"amount": 100}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {"validation_errors":{"amount":"[key:amount_to_high] Amount to high : 100%"}}
    """

  Scenario: Successfully create article unconditional discount
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72216/unconditional-discount" with body:
    """
    {"amount": 15}
    """
    Then  the response status code should be 204

  Scenario: Successfully reset update article unconditional discount
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "PUT" request to "/api/v1/article/72215/unconditional-discount" with body:
    """
    {"amount": 4}
    """
    Then  the response status code should be 204
