Feature: Update an article sales channel

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3" with body:
    """
    {"is_active": true, "selling_price": 119.0}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with token linked to an account which do not have the right permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add permission "ARTICLE_SALES_CHANNEL_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3" with body:
    """
    {"is_active": true, "selling_price": 119.0}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with token linked to an account which do not have the right permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3" with body:
    """
    {"is_active": true, "selling_price": 119.0}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on an non-existing article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add permission "ARTICLE_SALES_CHANNEL_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/404/sales-channel/3" with body:
    """
    {"is_active": true, "selling_price": 119.0}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article sales channel not found.
    """

  Scenario: Test on an non-existing sales channel
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add permission "ARTICLE_SALES_CHANNEL_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/404" with body:
    """
    {"is_active": true, "selling_price": 119.0}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article sales channel not found.
    """

  Scenario: Test with a valid article sales channel
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add permission "ARTICLE_SALES_CHANNEL_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/sales-channel/3" with body:
    """
    {"is_active": false, "selling_price": 119.0}
    """
    Then  the response status code should be 204