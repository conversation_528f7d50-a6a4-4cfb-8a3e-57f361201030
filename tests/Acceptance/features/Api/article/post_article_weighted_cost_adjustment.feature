Feature: Upload a weighted cost adjustment

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_weighted_cost_adjustment.sql"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment"
    Then  the response status code should be 401

  Scenario: Test on a non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/666666/weighted-cost-adjustment" with body:
    """
    {
      "type": "supplier_credit_note",
      "amount": 50
    }
    """
    Then  the response status code should be 404

  Scenario: Create a supplier credit note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment" with body:
    """
    {
      "type": "supplier_credit_note",
      "amount": 50,
      "meta": {}
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->weighted_cost_adjustment_id" should be equal to the number "3"

  Scenario: Adjust weighted cost
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment" with body:
    """
    {
      "type": "devaluation",
      "amount": 62.5,
      "meta": {
        "quantity":12,
        "date_range": {
          "from": "2023-05-10",
          "to": "2023-05-20"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->weighted_cost_adjustment_id" should be equal to the number "4"

  Scenario: Create a weighted cost adjustment that does not exists
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment" with body:
    """
    {
      "type": "toto",
      "amount": 62.5
    }
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "type": "\"toto\" does not exists, available keys are : \"devaluation\", \"supplier_credit_note\""
      }
    }
    """

  Scenario: Create a weighted cost adjustment with wrong meta
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/weighted-cost-adjustment" with body:
    """
    {
      "type": "devaluation",
      "amount": 62.5,
      "meta": "pas un json"
    }
    """
    Then  the response status code should be 500
