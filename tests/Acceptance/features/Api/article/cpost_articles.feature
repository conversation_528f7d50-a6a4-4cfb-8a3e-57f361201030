Feature: Get list of articles
  As someone identified in the ERP
  I want to see the articles and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles"
    Then  the response status code should be 401
#
  Scenario: Get list of articles without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->articles" should have 4 elements

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/articles"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Get list of articles filtered on the sku
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles" with body:
    """
    {
      "where": {
        "_or": [
          {
            "sku": {
              "_like": "%NORST%"
            }
          },
          {
            "short_description": {
              "_like": "%NORST%"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->articles" should have 1 elements
    And   the JSON node "data->articles[0]->sku" should contain "NORSTCL81123M"

  Scenario: Get list of articles filtered on the basket description
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles" with body:
    """
    {
      "where": {
        "_or": [
          {
            "sku": {
              "_like": "%Arcam%"
            }
          },
          {
            "short_description": {
              "_like": "%Arcam%"
            }
          }
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->articles" should have 2 elements
    And   the JSON node "data->articles[0]->sku" should contain "ARCAMRBLINKNR"
    And   the JSON node "data->articles[1]->sku" should contain "BWCCM74"
    And   the JSON node "data->articles[0]->short_description" should contain "Arcam rBlink"
    And   the JSON node "data->articles[1]->short_description" should contain "Arcam BW CCM"

  Scenario: Get list of articles filtered on the product_id (to use if the search term is an integer)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/articles" with body:
    """
    {
      "where": {
        "_or": [
          {
            "sku": {
              "_like": "%81123%"
            }
          },
          {
            "short_description": {
              "_like": "%81123%"
            }
          },
          {
            "product_id": {
              "_eq": 81123
            }
          }
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->articles" should have 2 elements
    And   the JSON node "data->articles[0]->sku" should contain "LBCLD25BP"
    And   the JSON node "data->articles[1]->sku" should contain "NORSTCL81123M"
    And   the JSON node "data->articles[1]->product_id" should contain "13895"
    And   the JSON node "data->articles[1]->model" should contain "CL250 Classic 2,5 mm2 (25 m)"
    And   the JSON node "data->articles[1]->domain_id" should contain "6"
    And   the JSON node "data->articles[1]->category_id" should contain "18"
    And   the JSON node "data->articles[1]->subcategory_id" should contain "56"
    And   the JSON node "data->articles[1]->created_at" should contain "2003-09-22"
    And   the JSON node "data->articles[1]->updated_at" should contain "2019-09-04 00:34:01"
    And   the JSON node "data->articles[1]->status" should contain "oui"
    And   the JSON node "data->articles[1]->brand_id" should contain "520"
    And   the JSON node "data->articles[1]->selling_price" should contain "45"
    And   the JSON node "data->articles[1]->pvgc" should contain "62.5"
    And   the JSON node "data->articles[1]->ecotax" should contain "0.5"
    And   the JSON node "data->articles[1]->sorecop" should contain "0"
    And   the JSON node "data->articles[1]->weight" should contain "2"
    And   the JSON node "data->articles[1]->supplier_id" should contain "1"
    And   the JSON node "data->articles[1]->supplier_reference" should be null
    And   the JSON node "data->articles[1]->stock_quantity" should contain "0"
    And   the JSON node "data->articles[1]->basket_description" should contain "Câble d'enceintes Norstone Audio CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 25 m"
    And   the JSON node "data->articles[1]->short_description" should contain "NorStone CL250 Classic (25 m)"
    And   the JSON node "data->articles[1]->initial_selling_price" should contain "0"
    And   the JSON node "data->articles[1]->is_package" should contain "1"
    And   the JSON node "data->articles[1]->is_destock" should contain "0"
    And   the JSON node "data->articles[1]->sku_havre" should be null
    And   the JSON node "data->articles[1]->delivery_time" should be null
    And   the JSON node "data->articles[1]->wholesale_price" should contain "0"
    And   the JSON node "data->articles[1]->wholesale_weighted_cost" should contain "0"
