Feature: Add an comment to an article

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_comment.sql"
    And   I send a "POST" request to "/api/v1/article/81078/comment"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/81078/comment"
    Then  the response status code should be 401

  Scenario: Test with a non-existant article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/666/comment" with body:
    """
    {"data": {"message": "LDLC: 279.99", "type": "general"}}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    No article found with id 666.
    """

  Scenario: Test add an comment with an empty message
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/81078/comment" with body:
    """
    {"data": {"message": "", "type": "general"}}
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "message": "This value should not be blank."
      }
    }
    """

  Scenario: Test add an comment with an empty type
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/81078/comment" with body:
    """
    {"data": {"message": "FNAC: 220.50€", "type": ""}}
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "type": "The value you selected is not a valid choice."
      }
    }
    """

  Scenario: Test successfully add an comment to an article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_COMMENT_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/81078/comment" with body:
    """
    {"data": {"message": "LDLC: 279.99", "type": "general"}}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 1 element
    And   the JSON node "data->article_comment_id" should be equal to the number 6
