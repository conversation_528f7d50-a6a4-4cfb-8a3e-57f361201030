Feature: Update article information

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/put_article.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"status": "any"}, "scope": "status"}
    """
    Then  the response status code should be 403
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"


  Scenario: Try to update with a missing required column
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_STATUS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"unknown_column": "any"}, "scope": "status"}
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "status": "[key:unknown_value] value \"\" : unknown"
      }
    }
    """

  Scenario: Test on a non existing article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_STATUS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/666" with body:
    """
    {"data": {"status": "any"}, "scope": "status"}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "No article found with id 666."

  Scenario: Successfully update article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_STATUS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81078" with body:
    """
    {"data": {"status": "tmp"}, "scope": "status"}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 1}
    """

  Scenario: Successfully update article bis
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_GENERAL_INFORMATION_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/128416" with body:
    """
    {
      "data": {
        "subcategory_id": 144,
        "short_description": "Mercurochrome le pansement des héros !",
        "brand_id": 520,
        "name": "Pansement",
        "basket_description": "Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros ! Mercurochrome le pansement des héros !",
        "color_id": 1,
        "packages": 20,
        "manufacturer_warranty_years": 0,
        "marketplace_description": "Les marketplaces : où trouver le parfait équilibre entre choix infini et confusion totale",
        "customs_code": "8989898989",
        "ecotax_code": "01210"
       },
      "scope": "general_information"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 2}
    """

