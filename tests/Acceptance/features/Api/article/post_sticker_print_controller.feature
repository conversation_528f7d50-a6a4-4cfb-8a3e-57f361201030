Feature: Generate and print a sticker

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "transaction_sqlqueue.sql"
    And   I load mysql fixtures from file "article/generate-and-print-code128.sql"
    And   I send a "POST" request to "/api/v1/article/72215/sticker/code-128/print"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/sticker/code-128/print"
    Then  the response status code should be 401

  Scenario: Test without specified printer ID or name
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/sticker/code-128/print" with body:
    """
    {
      "quantity": 1
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'Either printer_id or printer_name should be provided.'

  Scenario: Test with a non existing article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/666/sticker/code-128/print" with body:
    """
    {
      "printer_id": 30,
      "printer_name": "Print_Zebra_1",
      "quantity": 1
    }
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'Article not found with id or sku "666".'

  Scenario: Generate a Code128 sticker successfully
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/sticker/code-128/print" with body:
    """
    {
      "printer_id": 30,
      "printer_name": "Print_Zebra_1",
      "quantity": 1
    }
    """
    Then  the response status code should be 204

  Scenario: Generate a Destock sticker successfully
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/ELIPSPRESTIGE2I/sticker/destock/print" with body:
    """
    {
      "printer_id": 7,
      "quantity": 1
    }
    """
    Then  the response status code should be 204
