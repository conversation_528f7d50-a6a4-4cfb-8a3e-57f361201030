Feature: Add an article to a package

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/packaged_articles.sql"
    And   I send a "POST" request to "/api/v1/package/123/add-article"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/package/123/add-article"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/81078/add-article" with body:
    """
    {"quantity": 1, "article_id": 123}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with an unexisting packaged article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/120087/add-article" with body:
    """
    {"quantity": 1, "article_id": 666}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with id "666".
    """

  Scenario: Test with an unexisting package
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/666/add-article" with body:
    """
    {"quantity": 1, "article_id": 120087}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article not found with id "666".
    """

  Scenario: Test with an article which is not a package
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/81078/add-article" with body:
    """
    {"quantity": 1, "article_id": 123}
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article with id 81078 is not a package.
    """

  Scenario: Test to add more than 8 packaged articles
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/72218/add-article" with body:
    """
    {"quantity": 1, "article_id": 120087}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "packaged_article": "[key:too_many_packaged_articles] value \"120087\" : A package cannot have more than 8 articles"
      }
    }
    """

  Scenario: Test with an already existing packaged article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/78404/add-article" with body:
    """
    {"quantity": 1, "article_id": 120087}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "packaged_article": "[key:packaged_article_already_in_package] value \"120087\" : Article(s) with id(s) 78226 already exists in package"
      }
    }
    """

  Scenario: Test with a destock
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/78404/add-article" with body:
    """
    {"quantity": 1, "article_id": 128417}
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Cannot add a destock to a package.
    """

  Scenario: Test with quantity at 0
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/81078/add-article" with body:
    """
    {"quantity": 0, "article_id": 123}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "quantity": "This value should be positive."
      }
    }
    """

  Scenario: Test add an article to package
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/package/78404/add-article" with body:
    """
    {"quantity": 1, "article_id": 72216}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->packaged_articles" should have 2 elements
    And   the JSON node "data->packaged_articles" should be identical to
    """
    [
      {
        "packaged_article_id": 119545,
        "package_id": 78404,
        "article_id": 72216,
        "sku": "ELIPSPRESTIGE_C_2I",
        "name": "Prestige C2i Calvados",
        "short_description": "Elipson Prestige C2i Calvados",
        "image": "/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_300_square.jpg",
        "quantity": 1,
        "stock": 15,
        "stock_available": 15,
        "unit_selling_price": 199,
        "delay": 30,
        "status": "oui"
      },
      {
        "packaged_article_id": 107583,
        "package_id": 78404,
        "article_id": 78226,
        "sku": "NORSTB2501M",
        "name": "B250 Classic Black 2 x 2,5 mm2 (1 m)",
        "short_description": "NorStone B250 Classic Black (1 m)",
        "image": "/images/ui/uiV3/graphics/no-img-300.png",
        "quantity": 100,
        "stock": 3513,
        "stock_available": 0,
        "unit_selling_price": 2.99,
        "delay": 0,
        "status": "oui"
      }
    ]
    """
