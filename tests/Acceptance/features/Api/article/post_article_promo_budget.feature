Feature: Add a promo budget on an article

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/get_article_by_id_or_sku_v2.sql"
    And   I load mysql fixtures from file "article/article_promo_budget.sql"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-08-01 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-08-01 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-08-01 12:00:00", "end_at": "2024-08-08 12:00:00"}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Add an article promo budget with amount over the buying price
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 20000, "start_at": "2024-09-01 12:00:00", "end_at": "2024-09-08 12:00:00"}
    """
    Then  the response status code should be 400
    And the JSON node "status" should be equal to the string "error"
    And the JSON node "message" should be equal to the string "The article promo budget cannot be higher than buying price"

  Scenario: Add an article promo budget success
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-09-01 12:00:00", "end_at": "2024-09-08 12:00:00"}
    """
    Then  the response status code should be 200

  Scenario: Test with overlapping date range
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-08-06 12:00:00", "end_at": "2024-08-12 12:00:00"}
    """
    Then  the response status code should be 400
    And the JSON node "status" should be equal to the string "error"
    And the JSON node "message" should be equal to the string "The article promo budget overlaps another promo budget"

  Scenario: Test with a start_at after the end
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/article/72215/promo-budget" with body:
    """
    {"amount": 2, "start_at": "2024-05-06 12:00:00", "end_at": "2024-04-12 12:00:00"}
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {"validation_errors":{"start_at":"[key:starts_at_must_be_before_ends_at] start date must be before end date","end_at":"[key:starts_at_must_be_before_ends_at] start date must be before end date"}}
    """
