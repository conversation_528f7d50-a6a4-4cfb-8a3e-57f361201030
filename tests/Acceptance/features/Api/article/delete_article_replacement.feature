Feature: Delete an article replacement

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/article_replacement.sql"
    And   I send a "DELETE" request to "/api/v1/article-replacement/ARCAMRBLINKNR"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "DELETE" request to "/api/v1/article-replacement/ARCAMRBLINKNR"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article-replacement/ARCAMRBLINKNR"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with an non-existing article that returns are readable error message in the JSON response
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article-replacement/NOT-EXISTING"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Le produit remplacé n'existe pas
    """

  Scenario: Test with an article with no replacement
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article-replacement/TOO-EXPENSIVE"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    L'article "TOO-EXPENSIVE" n'a pas d'article remplaçant
    """

  Scenario: Delete an article replacement
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/article-replacement/ARCAMRBLINKNR"
    Then  the response status code should be 204
