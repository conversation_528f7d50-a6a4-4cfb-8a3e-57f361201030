Feature: Delete a packaged article

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "article/packaged_articles.sql"
    And   I send a "DELETE" request to "/api/v1/package/123/packaged-article/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "DELETE" request to "/api/v1/package/123/packaged-article/123"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/package/81078/packaged-article/123"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with an unexisting packaged article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/package/120087/packaged-article/123"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Packaged article not found.
    """

  Scenario: Test with an unexisting package
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/package/666/packaged-article/95149"
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Packaged article not found in package.
    """

  Scenario: Delete a packaged article from a package
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PACKAGE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/package/78404/packaged-article/107583"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"deleted": 1}
    """
