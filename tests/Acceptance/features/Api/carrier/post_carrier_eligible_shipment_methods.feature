Feature: Retrieve a filtered list of eligible shipment methods
  As someone identified in the ERP

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "carrier/post_carrier_eligible_shipment_methods.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods"
    Then  the response status code should be 401

  Scenario: Test response with an invalid payload, should return the validation errors
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods" with body:
    """
    {}
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should contain "items: This value should not be blank."
    And   the JSON node "message" should contain "shipping_address: This value should not be blank."

  Scenario: Test response with an invalid payload, should return the info about the wrong sku
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods" with body:
    """
    {
      "items": [
        {
          "sku": "NOT_EXISTING_SKU",
          "quantity": 1,
          "price_vat_excluded": 309,
          "price": 309,
          "total_price": 309
        }
      ],
      "shipping_address": {
        "title": "M.",
        "firstname": "Gerard",
        "lastname": "Manvussa",
        "cellphone": "",
        "city": "Nantes",
        "postal_code": "44100",
        "address": "38 rue de la ville en bois",
        "country_code": "FR"
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should contain 'Article with sku "NOT_EXISTING_SKU" does not exists'

  Scenario: Retrieve the eligible shipment methods successfully
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 13, "cost": 50}
    ]
    """
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods" with body:
    """
    {
      "items": [
        {
          "sku": "ARCAMRBLINKNR",
          "quantity": 1,
          "price_vat_excluded": 309,
          "price": 309,
          "total_price": 309
        }
      ],
      "shipping_address": {
        "title": "M.",
        "firstname": "Gerard",
        "lastname": "Manvussa",
        "cellphone": "",
        "city": "Nantes",
        "postal_code": "44100",
        "address": "38 rue de la ville en bois",
        "country_code": "FR"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 1 element
    And   the JSON node "data[0]" should be identical to
    """
    {
       "shipment_method_id": 13,
       "label": "Express",
       "comment": "Livraison pour Ile de France",
       "carrier_name": "Novea",
       "is_retail_store": false,
       "cost": 50,
       "tags": [
         "express"
       ]
    }
    """

  Scenario: Retrieve the eligible shipment methods successfully with an Easylounge app token
    When  I add "Authorization" header equal to "Bearer app-token-easylounge"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 13, "cost": 50}
    ]
    """
    And   I send a "POST" request to "/api/v1/carrier/eligible-shipment-methods" with body:
    """
    {
      "items": [
        {
          "sku": "ARCAMRBLINKNR",
          "quantity": 1,
          "price_vat_excluded": 309,
          "price": 309,
          "total_price": 309
        }
      ],
      "shipping_address": {
        "title": "M.",
        "firstname": "Gerard",
        "lastname": "Manvussa",
        "cellphone": "",
        "city": "Nantes",
        "postal_code": "44100",
        "address": "38 rue de la ville en bois",
        "country_code": "FR"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should have 1 element
    And   the JSON node "data[0]" should be identical to
    """
    {
       "shipment_method_id": 13,
       "label": "Express",
       "comment": "Livraison pour Ile de France",
       "carrier_name": "Novea",
       "is_retail_store": false,
       "cost": 50,
       "tags": [
         "express"
       ]
    }
    """
