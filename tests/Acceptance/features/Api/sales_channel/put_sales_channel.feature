Feature: Update a sales channel

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I send a "PUT" request to "/api/v1/sales-channel/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/sales-channel/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/sales-channel/3" with body:
    """
    {
      "sales_channel_id": 3,
      "minimum_margin_rate": 13,
      "minimum_available_quantity": 0,
      "average_commission_rate": 18.67
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON


  Scenario: Test with invalid parameters sales channel id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/sales-channel/3" with body:
    """
    {
      "sales_channel_id": 5,
      "minimum_margin_rate": 13,
      "minimum_available_quantity": 0,
      "average_commission_rate": 18.67
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON


  Scenario: Update a sale channel success
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/sales-channel/3" with body:
    """
    {
      "sales_channel_id": 3,
      "minimum_margin_rate": 13,
      "minimum_available_quantity": 0,
      "average_commission_rate": 18.67
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->updated" should be equal to the number "1"
