Feature: Add products to a pricing strategy

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "pricing_strategy/pricing_strategy.sql"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/1/products" with body:
    """
    {"product_ids": [81078]}
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/1/products" with body:
    """
    {"product_ids": [81078]}
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/1/products" with body:
    """
    {"product_ids": [81078]}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Add products to an non-existing strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/1/products" with body:
    """
    {"product_ids": [81078]}
    """
    Then  the response status code should be 404
    And the JSON node "status" should be equal to the string "error"
    And the JSON node "message" should be equal to the string "Pricing strategy not found with id 1"

  Scenario: Add non-existing products to a strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5/products" with body:
    """
    {"product_ids": [9999999999]}
    """
    Then  the response status code should be 204

  Scenario: Add product to strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5/products" with body:
    """
    {"product_ids": [81124]}
    """
    Then  the response status code should be 204
