Feature: Update a pricing strategy

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "pricing_strategy/pricing_strategy.sql"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5" with body:
    """
    {
      "pricing_strategy_id": 5,
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5" with body:
    """
    {
      "pricing_strategy_id": 5,
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5" with body:
    """
    {
      "pricing_strategy_id": 5,
      "name": "Stratégie test",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Edit a pricing strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/5" with body:
    """
    {
      "pricing_strategy_id": 5,
      "name": "tata toto",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": ["BOULANGER"]
    }
    """
    Then  the response status code should be 204

  Scenario: Edit a pricing strategy
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/pricing-strategy/666" with body:
    """
    {
      "pricing_strategy_id": 5,
      "name": "tata toto",
      "starts_at": "2024-05-06 10:00:00",
      "ends_at": "2024-05-07 10:00:00",
      "activation_status": "ACTIVATED",
      "weekdays_increment_amount": 0,
      "weekdays_min_margin_rate": 10,
      "weekend_increment_amount": 0,
      "weekend_min_margin_rate": 10,
      "sales_channels": [],
      "competitors": []
    }
    """
    Then  the response status code should be 400
