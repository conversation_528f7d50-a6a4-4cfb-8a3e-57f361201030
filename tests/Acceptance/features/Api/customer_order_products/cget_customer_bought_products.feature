Feature: Get list of customer's bought products
  In order to manage customer's data
  As a user
  I need to be able to get a list of customer's bought products, paginated

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "customer_order_product/cget_customer_bought_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/customer/1/bought-products"
    Then  the response status code should be 401


  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/customer/1/bought-products"
    Then  the response status code should be 401

  Scenario: Get list of customer order products related to customer
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/customer/1/bought-products"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->bought_products" should have 2 element
    And   the JSON node "data->bought_products[0]" should be identical to
    """
    {
      "customer_id": 1,
      "customer_order_created_at": "2022-12-25 16:56:00",
      "article_id": 81078,
      "sku": "ARCAMRBLINKNR",
      "article_image": "https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_600_max_width.jpg",
      "article_name": "rBlink",
      "brand_name": "Arcam",
      "quantity": 2,
      "selling_price": 224,
      "buy_price": 133.29,
      "description": "rBlink",
      "customer_orders": [
        {
          "customer_order_id": 1,
          "has_ongoing_premium_warranty": true
        },
        {
          "customer_order_id": 2,
          "has_ongoing_premium_warranty": false
        }
      ]
    }
    """



