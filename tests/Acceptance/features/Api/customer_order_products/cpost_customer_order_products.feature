Feature: Get list of customer order products
  In order to manage customer order products through the API
  As a user
  I need to be able to get list of customer order products

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "customer_order_product/cpost_customer_order_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-products"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-products"
    Then  the response status code should be 401

  Scenario: Get list of customer order products related to customer
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-products" with body:
    """
    {
      "where": {
        "_and": [
          {
            "customer_id": {
              "_eq": 1
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_products" should have 2 elements
    And   the JSON node "data->customer_order_products[0]" should have the following keys and values
    """
      {
        "customer_order_product_id": 10,
        "customer_order_id": 1,
        "customer_id": 1,
        "customer_order_created_at": "1990-01-01 00:00:00",
        "customer_order_closed_at": null,
        "article_id": 81078,
        "sku": "ARCAMRBLINKNR",
        "article_name": "rBlink",
        "brand_name": "Arcam",
        "article_image": "https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_600_max_width.jpg",
        "tax_rate": 0.2,
        "quantity": 1,
        "selling_price": 249,
        "buy_price": 131.58,
        "description": "rBlink",
        "ecotax_price": null,
        "extension_warranty_duration": null,
        "extension_warranty_price": 0,
        "extension_warranty_tax": 0,
        "extension_warranty_commission": 0,
        "extension_warranty_commission_tax": 0,
        "extension_warranty_seller": null,
        "damage_and_theft_warranty_duration": null,
        "damage_and_theft_warranty_price": 0,
        "damage_and_theft_warranty_commission": 0,
        "damage_and_theft_warranty_commisision_tax": 0,
        "damage_and_theft_warranty_seller": null,
        "discount_type": null,
        "discount_amount": 0,
        "discount_description": null,
        "delivery_note_id": null,
        "availability": null,
        "available_supplier_orders": null,
        "available_transfers": null
      }
    """                                                                                                            |

  Scenario: Get list of customer order products related to a customer order with availability
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order-products" with body:
    """
    {
      "where": {
        "_and": [
          {
            "customer_order_id": {
              "_eq": 1
            }
          }
        ]
      },
      "included_dependencies": ["availability", "available_supplier_orders", "available_transfers"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_products" should have 1 elements
    And   the JSON node "data->customer_order_products[0]" should have the following keys and values
    """
      {
        "customer_order_product_id": 10,
        "customer_order_id": 1,
        "customer_id": 1,
        "customer_order_created_at": "1990-01-01 00:00:00",
        "customer_order_closed_at": null,
        "article_id": 81078,
        "sku": "ARCAMRBLINKNR",
        "article_image": "https://image.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_600_max_width.jpg",
        "article_name": "rBlink",
        "brand_name": "Arcam",
        "tax_rate": 0.2,
        "quantity": 1,
        "selling_price": 249,
        "buy_price": 131.58,
        "description": "rBlink",
        "ecotax_price": null,
        "extension_warranty_duration": null,
        "extension_warranty_price": 0,
        "extension_warranty_tax": 0,
        "extension_warranty_commission": 0,
        "extension_warranty_commission_tax": 0,
        "extension_warranty_seller": null,
        "damage_and_theft_warranty_duration": null,
        "damage_and_theft_warranty_price": 0,
        "damage_and_theft_warranty_commission": 0,
        "damage_and_theft_warranty_commisision_tax": 0,
        "damage_and_theft_warranty_seller": null,
        "discount_type": null,
        "discount_amount": 0,
        "discount_description": null,
        "delivery_note_id": null,
        "availability": [],
        "available_supplier_orders": [],
        "available_transfers": []
      }
    """

    And   the JSON node "data->customer_order_products[0]->availability" should have 1 elements
    And   the JSON node "data->customer_order_products[0]->availability[0]" should have the following keys and values
    """
      {
          "customer_order_id": 1,
          "product_id": 81078,
          "availability_date": "1990-01-01",
          "store_pickup_id": null,
          "customer_order_product_quantity": 1,
          "sent_quantity": 0,
          "available_quantity_in_store": 0,
          "warehouses_stock": 0,
          "customer_order_status": "valid"
      }
    """

    And   the JSON node "data->customer_order_products[0]->available_supplier_orders" should have 2 elements
    And   the JSON node "data->customer_order_products[0]->available_supplier_orders[0]" should have the following keys and values
    """
      {
          "supplier_order_id": 1,
          "supplier_order_status": "en preparation",
          "delivery_expected_date": null
      }
    """
