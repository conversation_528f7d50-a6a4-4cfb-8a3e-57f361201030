Feature: Upload an media

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I send a "POST" request to "/api/v1/knowledge-base/media/upload"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/knowledge-base/media/upload"
    Then  the response status code should be 401

  Scenario: Check response when no data has been provided
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a POST request to "/api/v1/knowledge-base/media/upload"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    "file" key name was not found on the request payload
    """

  Scenario: Check response when the media has been successfully uploaded by an kb id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a POST request to "/api/v1/knowledge-base/media/upload" with parameters:
      | key  | value    |
      | file | @../../fixtures/files/test.jpg |
    Then  the response status code should be 200
    And   the response should be in JSON
    # don't test the entirety on the string since the end of the file is dynamically generated with a random string
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->target_path" should contain "images/knowledge-base/"