Feature: Retrieve a delivery note
  In order to obtain some information about a delivery note through the API
  As a user
  I need to be able to retrieve a delivery note using its id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "delivery_note/delivery_note.sql"
    And   I send a "GET" request to "/api/v1/delivery-note/123"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/delivery-note/123"
    Then  the response status code should be 401

  Scenario: Test with non existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/delivery-note/666"
    Then  the JSON node "code" should be equal to the number "404"
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Delivery note not found.
    """

  Scenario: Test with an existing delivery note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/delivery-note/456"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->delivery_note" should have the following keys and values
    """
    {
      "delivery_note_id": 456,
      "status": "au depart",
      "warehouse_id": 1,
      "customer_order_id": 1712826,
      "transfer_id": null,
      "transfer_status": null,
      "transfer_destination_warehouse_id": null,
      "invoice_id": null,
      "creation_date": "2019-08-29 04:05:37",
      "prepared_at": "2019-08-29 11:41:10",
      "carrier_id": 2,
      "carrier_product_id": 1,
      "is_prepared_in_expedition": false,
      "is_pickup": false,
      "tracking_id": null,
      "store_pickup_started_at": null,
      "picked_by": null,
      "picked_by_name": "",
      "workflow_status": "PREPARATION_FINISHED",
      "delivery_note_products": []
    }
    """
    # products
    And   the JSON node "data->delivery_note->delivery_note_products[0]" should have the following keys and values
    """
    {
      "product_id": 70520,
      "sku": "YAMEPH100SI",
      "type": "article",
      "description": "C\u00e2ble d'enceinte QED Performance Audio 40i, longueur 1 m",
      "marque": "Arcam",
      "modele": "EPH-100",
      "quantity": 1,
      "package_number": 1,
      "countable_manually": false,
      "is_virtual_product": false,
      "is_auto_picked": false,
      "code_128": "15200070520",
      "locations": []
    }
    """
    # locations where the current product is stored
    And   the JSON node "data->delivery_note->delivery_note_products[0]->locations[0]" should have the following keys and values
    """
    {
      "product_location_id": "2",
      "location_id": "3",
      "location_label": "04.01.A$01.01.01",
      "location_code": "04.01.a.01.01.01",
      "location_is_active": "1",
      "product_id": "70520",
      "sku": "YAMEPH100SI",
      "short_description": "Yamaha EPH-100",
      "delivery_ticket_id": null,
      "move_mission_id": null,
      "is_linked": "0",
      "is_user_location": "0",
      "user_fullname": null,
      "quantity": "6",
      "area_label": "Tout le stock",
      "area_type_id": "4",
      "area_type_label": "stock",
      "warehouse_id": "1",
      "warehouse_name": "Champigny",
      "warehouse_display_order": "1",
      "is_transfer_location": "0"
    }
    """
