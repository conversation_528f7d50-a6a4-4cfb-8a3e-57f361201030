Feature: Update detail of delivery note
  As someone identified in the ERP

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "shipment/cpost_shipment_delivery_notes.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403190"
    Then  the response status code should be 401


  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403190"
    Then  the response status code should be 401

  Scenario: Test with empty data
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403191" 
    Then  the response status code should be 500
    And   the JSON should be equal to:
    """     
    {
      "status": "error",
      "message": "data is empty",
      "code": 500,
      "data": []
    }
    """

    Scenario: Test with an unknow delivery note
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/0" with body:
    """
    {
      "firstname": "Jean"
    }
    """
    Then  the response status code should be 404
    And   the JSON should be equal to:
    """
    {
      "status": "error",
      "message": "Shipment delivery note with delivery note 0 not found",
      "code": 404,
      "data": []
    }
    """


    Scenario: Test with a closed shipment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403191" with body:
    """
    {
        "firstname": "Jean"
    }
      """
    Then  the response status code should be 500
    And   the JSON should be equal to:
    """
    {
      "status": "error",
      "message": "Shipment 9019 is closed",
      "code": 500,
      "data": []
    }
    """

    Scenario: Test with a shipment method of another carrier use in the shipment shipment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403190" with body:
    """
    {
      "firstname": "Jean",
      "shipment_method_id": 8
    }
    """
    Then  the response status code should be 500
    And   the JSON should be equal to:
    """
    {
      "status": "error",
      "message": "Invalid shipment_method_id for this delivery note",
      "code": 500,
      "data": []
    }
    """

    Scenario: Test ok
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "DELIVERY_NOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/delivery-note/4403190" with body:
    """
    {
      "firstname": "Jean",
      "lastname": "Bon"
    }
    """
    Then  the response status code should be 200
    And   the JSON should be equal to:
    """
    {
      "status": "success",
      "data": []
    }
    """