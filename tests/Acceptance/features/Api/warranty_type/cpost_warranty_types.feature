Feature: Get list of warranty types
  As someone identified in the ERP
  I want to see the warranty types

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "warranty_type/warranty_types.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/warranty-types"
    Then  the response status code should be 401

  Scenario: Get list of warranty types without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/warranty-types"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->warranty_types" should have 3 elements

  Scenario: Get list of warranty types filtered by an arbitrary type
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/warranty-types" with body:
    """
    {
      "where": {
        "type": {
          "_eq": "SON"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->warranty_types" should have 1 elements
    And   the JSON node "data->warranty_types[0]->type" should be equal to "SON"
    And   the JSON node "data->warranty_types[0]->label" should be equal to "Son"
    And   the JSON node "data->warranty_types[0]->description" should be equal to "Garantie pour les produits appartenant à la famille du son"
