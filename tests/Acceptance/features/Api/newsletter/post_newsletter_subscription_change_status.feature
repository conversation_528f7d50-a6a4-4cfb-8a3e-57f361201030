Feature: api to update the customer's newsletter subscription status

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "prospect.sql"
    And   I send a "POST" request to "/api/v1/newsletter-subscription-change-status" with body:
    """
    {
      "email": "<EMAIL>",
      "status": "unsubscribed"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/newsletter-subscription-change-status" with body:
    """
    {
      "email": "<EMAIL>",
      "status": "unsubscribed"
    }
    """
    Then  the response status code should be 401

  Scenario: Test change subscription status to unsubscribe
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:unsubscribe_newsletter" to respond with:
    """
    {
      "is_change": true
    }
    """
    And   I send a "POST" request to "/api/v1/newsletter-subscription-change-status" with body:
    """
    {
      "email": "<EMAIL>",
      "status": "subscribed"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data.is_change" should be true

  Scenario: Test change subscription status to subscribe
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:subscribe_newsletter" to respond with:
    """
    {
      "is_change": true
    }
    """
    And   I send a "POST" request to "/api/v1/newsletter-subscription-change-status" with body:
    """
    {
      "email": "<EMAIL>",
      "status": "unsubscribed"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data.is_change" should be true

  Scenario: Test change subscription status to subscribe failed
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:subscribe_newsletter" to respond with:
    """
    {
      "is_change": false,
      "message": "Error"
    }
    """
    And   I send a "POST" request to "/api/v1/newsletter-subscription-change-status" with body:
    """
    {
      "email": "<EMAIL>",
      "status": "unsubscribed"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data.is_change" should be false
    And   the JSON node "data.message" should be equal to "Error"
