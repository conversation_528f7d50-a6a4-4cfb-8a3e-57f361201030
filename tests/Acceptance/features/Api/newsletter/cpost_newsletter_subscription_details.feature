Feature: Get list of newsletter subscription details
  As someone identified in the ERP
  I want to see the newsletter subscription details and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I load mysql fixtures from file "customer/cpost_customers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/newsletter-subscription-details"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/newsletter-subscription-details"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:get_status_newsletter" to respond with:
    """
    {
      "status": "subscribed",
      "origin" : "erp",
      "created_at" : "2024-01-01",
      "last_activity" : "2024-01-20"
    }
    """
    And   I send a "POST" request to "/api/v1/newsletter-subscription-details"
    """
    {
      "email_address": "<EMAIL>"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for newsletter subscription details API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:get_status_newsletter" to respond with:
    """
    {
      "status": "subscribed",
      "origin" : "erp",
      "created_at" : "2024-01-01",
      "last_activity" : "2024-01-20"
    }
    """
    And   I send a "POST" request to "/api/v1/newsletter-subscription-details"
    """
    {
      "email_address": "<EMAIL>"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->details" should exist
    And   the JSON node "data->details" should have the following keys and values
    """
    {
      "status": "subscribed",
      "origin": "erp",
      "created_at": "2024-01-01",
      "last_activity": "2024-01-20"
    }
    """