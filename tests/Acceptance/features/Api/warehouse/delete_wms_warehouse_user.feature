Feature: Unassign a WMS user to a warehouse
  As a manager
  I need to be able to unassign a WMS user from a warehouse

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouses.sql"
    And   I load mysql fixtures from file "wms/warehouses.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/warehouse/1/user/1"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/warehouse/1/user/1"
    Then  the response status code should be 401

  Scenario: Failed to unassign a user
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/warehouse/2/user/1"
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string
    """
    Could not un-assign user "1" from warehouse "2".
    """

  Scenario: Unassign a user successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/warehouse/1/user/1"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
