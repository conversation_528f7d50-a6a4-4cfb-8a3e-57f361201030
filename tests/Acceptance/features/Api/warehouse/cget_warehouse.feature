Feature: get list of warehouses
  In ordering to manage warehouses through API
  As a user
  I need to be able to list warehouses

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "warehouses.sql"
    And   I send a "GET" request to "/api/v1/warehouses"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/warehouses"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/warehouses"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Get list of warehouses
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/warehouses"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    # pager
    And   the JSON node "data" pager should have a total of "16"
    And   the JSON node "data" pager should have a limit of "50"
    # warehouses
    And   the JSON node "data->warehouses" should have 16 elements
    And   the JSON node "data->warehouses[0]->warehouse_id" should be equal to the number "1"
    And   the JSON node "data->warehouses[0]->name" should be equal to "Champigny"
    And   the JSON node "data->warehouses[0]->ordering" should be equal to the number "1"
    # check ordering
    And   the JSON node "data->warehouses[1]->warehouse_id" should be equal to the number "3"
    And   the JSON node "data->warehouses[1]->ordering" should be equal to the number "2"
    And   the JSON node "data->warehouses[2]->warehouse_id" should be equal to the number "14"
    And   the JSON node "data->warehouses[2]->ordering" should be equal to the number "14"
    And   the JSON node "data->warehouses[3]->warehouse_id" should be equal to the number "11"
    And   the JSON node "data->warehouses[3]->ordering" should be equal to the number "99"
    And   the JSON node "data->warehouses[3]->name" should be equal to "Antibes"
    And   the JSON node "data->warehouses[4]->warehouse_id" should be equal to the number "12"
    And   the JSON node "data->warehouses[4]->ordering" should be equal to the number "99"
    And   the JSON node "data->warehouses[4]->name" should be equal to "Bordeaux"
    And   the JSON node "data->warehouses[5]->warehouse_id" should be equal to the number "10"
    And   the JSON node "data->warehouses[5]->ordering" should be equal to the number "99"
    And   the JSON node "data->warehouses[5]->name" should be equal to "Grenoble"

  Scenario: Get list of paginated warehouses
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/warehouses?page=2&limit=5"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "16"
    And   the JSON node "data" pager should have a limit of "5"
    And   the JSON node "data->warehouses" should have 5 elements
    And   the JSON node "data->warehouses[0]->warehouse_id" should be equal to the number "10"
