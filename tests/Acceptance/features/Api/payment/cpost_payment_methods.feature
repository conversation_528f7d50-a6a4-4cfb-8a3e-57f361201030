Feature: Get list of payment methods

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    When  I load mysql fixtures from file "payment/cpost_payment_methods.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/payment_methods"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/payment_methods"
    Then  the response status code should be 401

  Scenario: Get list of active payments methods
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/payment_methods" with body:
    """
    {
       "where":{
          "actif":{
             "_eq":"Y"
          }
       },
       "order_by":"payment_id ASC",
       "limit":100
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->payment_methods" should have 47 elements
