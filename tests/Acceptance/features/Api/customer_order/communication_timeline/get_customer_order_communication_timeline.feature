Feature: Retrieve the communication events of a customer_order

  @clear-database
  Scenario: Load base fixtures
    When I load mysql fixtures from file "users.sql"
    And  I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "customer_order.sql"
    And  I load mysql fixtures from file "customer_order/get_customer_order_communication_timeline.sql"
    And  I load sql fixtures from file "customer_order/get_customer_order_communication_timeline.sql"

  Scenario: Test without authorization
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/customer-order/1/communication-timeline"
    Then the response status code should be 401

  Scenario: Test with a non valid authorization
    When I add "Authorization" header equal to "Bearer unknown-token"
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/customer-order/1/communication-timeline"
    Then the response status code should be 401

  Scenario: Retrieve events without permission
    When I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/customer-order/1/communication-timeline"
    Then the response status code should be 200
    And  the response should be in JSON
    And  the JSON node "status" should be equal to the string "success"
    And  the JSON node "data->events" should have 7 elements

    # First event
    And  the JSON node "data->events[0]->type" should be equal to "email.contact"
    And  the JSON node "data->events[0]->id_for_type" should be equal to "3483c684-984b-4eb2-ad57-4345ba52dbad"
    And  the JSON node "data->events[0]->created_at" should be equal to "2022-01-13T15:55:26+01:00"
    And  the JSON node "data->events[0]->emitter->type" should be equal to "svd"
    And  the JSON node "data->events[0]->emitter->data->user_id" should be equal to the number "1"
    # -> Payload content depends on the data in database, just check that it contains something
    And  the JSON node "data->events[0]->payload" should not be null
    And  the JSON node "data->events[0]->payload->content" should not be null
    And  the JSON node "data->events[0]->payload->mailjet_message_id" should be equal to "576460762942269313"

    # Second event has purposely an unknown emitter
    And  the JSON node "data->events[1]->id_for_type" should be equal to "8a082205-badd-48f8-8333-dbfad132526f"
    And  the JSON node "data->events[1]->emitter" should be null

    # Third event has the "backoffice" emitter
    And  the JSON node "data->events[2]->id_for_type" should be equal to "39791fae-eae7-479d-8be8-cf949aeffb28"
    And  the JSON node "data->events[2]->emitter->type" should be equal to "system"

    # From the 4th event, it should be tracking events
    # So a full test is required
    And  the JSON node "data->events[3]->type" should be equal to "tracking.contact"
    And  the JSON node "data->events[3]->id_for_type" should be equal to the number "4181914"
    And  the JSON node "data->events[3]->created_at" should be equal to "2020-06-22T12:22:54+02:00"
    And  the JSON node "data->events[3]->emitter->type" should be equal to "customer"
    And  the JSON node "data->events[3]->payload" should not be null
    And  the JSON node "data->events[3]->payload->message" should not be null
    And  the JSON node "data->events[3]->payload->mailjet_message_id" should not exist

    # 6th
    And  the JSON node "data->events[5]->id_for_type" should be equal to the number "4198609"
    And  the JSON node "data->events[5]->emitter->type" should be equal to "svd"
    And  the JSON node "data->events[5]->emitter->data->user_id" should be equal to the number "2"


