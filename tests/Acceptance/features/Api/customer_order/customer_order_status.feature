Feature: Customer Order Statuses
  In order to manage customer orders
  As a user
  I need to be able to retrieve all customer order statuses

  Scenario: Test without authorization
    And   I send a "GET" request to "/api/v1/customer-order/statuses"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/customer-order/statuses"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (=authorized)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/customer-order/statuses"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Load customer order statuses
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer-order/statuses"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data.customer_order_statuses" should be identical to
     """
      [
        {
          "status_id": 1,
          "priority": -100,
          "name": "ERROR",
          "description": "Erreur"
        },
        {
          "status_id": 2,
          "priority": 0,
          "name": "CANCELLED",
          "description": "Annulée"
        },
        {
          "status_id": 3,
          "priority": 100,
          "name": "AWAITING",
          "description": "Ouverte"
        },
        {
          "status_id": 4,
          "priority": 200,
          "name": "PAID",
          "description": "Payée"
        },
        {
          "status_id": 5,
          "priority": 300,
          "name": "PREPARABLE",
          "description": "Préparable"
        },
        {
          "status_id": 6,
          "priority": 400,
          "name": "BEING_PREPARED",
          "description": "En préparation"
        },
        {
          "status_id": 7,
          "priority": 500,
          "name": "PREPARED",
          "description": "Préparée"
        },
        {
          "status_id": 8,
          "priority": 600,
          "name": "INVOICED",
          "description": "Facturée"
        }
      ]
      """
