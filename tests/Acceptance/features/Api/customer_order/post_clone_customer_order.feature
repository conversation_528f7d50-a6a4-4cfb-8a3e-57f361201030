Feature: Post clone customer order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order/clone_customer_order.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/1/clone"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/1/clone"
    Then  the response status code should be 401

  Scenario: Clone unexisting customer order
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/404/clone"
    Then  the response should be in JSON
    And   the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Customer order with id 404 does not exists'

  Scenario: Clone customer order
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order/4/clone"
    Then  the response should be in JSON
    And   the response status code should be 200
    And   the JSON node "data->customer_order_id" should be equal to the number "1"
