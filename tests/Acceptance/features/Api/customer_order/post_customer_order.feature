Feature: Post customer order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "customer_order/rpc/creator.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order"
    Then  the response status code should be 401

  Scenario: Test with no payload
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order"
    Then  the response should be in JSON
    And   the response status code should be 500

  Scenario: Create customer order with wrong payload
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
"""
{
  "type": "quotation",
  "customer_id": 1500005
}
"""
    Then  the response should be in JSON
    And   the response status code should be 400
    And   the response should contain "Invalid request parameters"

  Scenario: Create customer with wrong origin
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "video-son.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "customer_id": 1,
  "quote_id": 10,
  "warehouse_id": null,
  "ip_address": "***********",
  "billing_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "payment_mean": "CBS",
      "created_at": "2022-01-01 00:00:00",
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "type": "article",
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response should be in JSON
    And   the response status code should be 400
    And   the JSON node "status" should be equal to the string "fail"
    And   the JSON node "data" should be equal to the string
    """
    Invalid request parameters : origin: The customer order origin "video-son.com" is invalid.
    """

  Scenario: Create customer with wrong source
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "son-video.com",
  "source": "TOTO",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "customer_id": 1,
  "quote_id": 10,
  "warehouse_id": null,
  "ip_address": "***********",
  "billing_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "payment_mean": "CBS",
      "created_at": "2022-01-01 00:00:00",
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response should be in JSON
    And   the JSON node "status" should be equal to the string "fail"
    And   the JSON node "data" should be equal to the string
    """
    Invalid request parameters : source: The customer order source "TOTO" is invalid.
    """

  Scenario: Create customer with to long adresse
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "son-video.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "customer_id": 1,
  "quote_id": 10,
  "warehouse_id": null,
  "ip_address": "***********",
  "billing_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs 1 rue des fleurs ",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "payment_mean": "CBS",
      "created_at": "2022-01-01 00:00:00",
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response should be in JSON
    And   the JSON node "status" should be equal to the string "fail"
    And   the response should contain "Invalid request parameters : billing_address.address: This value is too long. It should have 500 characters or less."


  Scenario: Create customer order succeeded
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "son-video.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "customer_id": 1,
  "quote_id": 10,
  "warehouse_id": null,
  "ip_address": "***********",
  "billing_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "payment_mean": "CBS",
      "created_at": "2022-01-01 00:00:00",
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to the number "1234"

  Scenario: Create customer order succeeded with user EasyLounge
    And   I add "Authorization" header equal to "Bearer app-token-easylounge"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "ecranlounge.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "source": "web",
  "billing_address": {
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone":"",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to the number "1235"


  Scenario: Create marketplace customer order - already exists
    And   I add "Authorization" header equal to "Bearer app-token-easylounge"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "123",
  "origin": "ecranlounge.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "source": "web",
  "billing_address": {
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone":"",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response status code should be 409
    And   the JSON node "data->customer_order_id" should be equal to the number "1235"

  Scenario: Create customer order with user EasyLounge and source is not sent
    And   I add "Authorization" header equal to "Bearer app-token-easylounge"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "1236",
  "origin": "ecranlounge.com",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "billing_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "company_name": "La Cie",
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "**********",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipment_method": {
    "cost": 4.99,
    "shipment_method_id": 1
  },
  "payments": [
    {
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200,
      "unit_discount_amount": -100
    }
  ]
}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "fail"
    And   the JSON node "data" should be equal to "Parameter source can not be empty or has wrong value"

  Scenario: Create Amazon customer order with minimal valid payload
    And   I add "Authorization" header equal to "Bearer app-token-easylounge"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And  I expect RPC call to "bo-cms" "customer:get_or_create" to respond with:
    """
{
 "status": true,
 "account": {
   "customer_id": ********,
   "email": "<EMAIL>",
   "is_active": false
 }
}
    """
    And   I send a "POST" request to "/api/v1/customer-order" with body:
    """
{
  "original_customer_order_id": "1236",
  "origin": "amazon.de",
  "created_at": "2022-01-01 00:00:00",
  "estimated_delivery_date": "2022-05-27",
  "billing_address": {
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
  "shipping_address": {
    "civility": "M.",
    "firstname": "Alain",
    "lastname": "TERIEUR",
    "address": "1 rue des fleurs",
    "postal_code": "44100",
    "city": "NANTES",
    "country_code": "FR",
    "phone": "",
    "cellphone": "**********",
    "email": "<EMAIL>"
  },
    "shipment_method": {
    "cost": 4.99
  },
  "payments": [
    {
      "amount": 1104.99
    }
  ],
  "products": [
    {
      "sku": "ARCAMRBLINKNR",
      "description": "R\\u00e9cepteur Audio Bluetooth APTX Arcam rBlink",
      "ecotax_price": 0,
      "sorecop_price": 0,
      "quantity": 1,
      "selling_price_tax_included": 1200
    }
  ]
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to the number "1237"
