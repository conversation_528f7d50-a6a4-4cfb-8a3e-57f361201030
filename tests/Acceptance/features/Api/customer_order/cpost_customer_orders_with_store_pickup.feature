Feature: Get list of customer orders which have at least one delivery note in store pickup
  As someone identified in the ERP
  I want to see the customer orders which have at least one delivery note in store pickup

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order/cpost_customer_orders_with_store_pickup.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-orders/with-store-pickup"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-orders/with-store-pickup"
    Then  the response status code should be 401

  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-orders/with-store-pickup" with body:
    """
    {
      "where": {
        "_and": [
          {
            "warehouse_id": {
              "_eq": 1
            }
          }
        ]
      },
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Optional column with "imposteur" does not exists, Available keys are : "payments", "articles", "delivery_notes", "invoices", "last_internal_comment", "last_customer_comment".
    """

  Scenario: Load list without dependencies
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-orders/with-store-pickup" with body:
    """
    {
      "where": {
        "_and": [
          {
            "warehouse_id": {
              "_eq": 1
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->customer_orders" should have 2 elements
    # check main keys (customer order)
    And   the JSON nodes should be equal to:
      | data.customer_orders[0].customer_order_id       | 3                   |
      | data.customer_orders[0].warehouse_id            | 1                   |
      | data.customer_orders[0].is_shipped              | 0                   |
      | data.customer_orders[0].is_detaxed              | 0                   |
      | data.customer_orders[0].postal_code_is_valid    | 0                   |
      | data.customer_orders[0].created_at              | 2021-01-15 00:00:00 |
      | data.customer_orders[0].customer_id             | 1                   |
      | data.customer_orders[0].customer_type           | particulier         |
      | data.customer_orders[0].customer_name           | Alain AGADOU        |
      | data.customer_orders[0].is_blacklisted          | 0                   |
      | data.customer_orders[0].amount_all_tax_included | 249.00              |
      | data.customer_orders[0].company_name            |                     |
      | data.customer_orders[0].payment_fraud_detection |                     |
    # check dependencies
    And   the JSON node "data->customer_orders[0]->payments" should have 0 elements
    And   the JSON node "data->customer_orders[0]->articles" should have 0 elements
    And   the JSON node "data->customer_orders[0]->delivery_notes" should have 0 elements
    And   the JSON node "data->customer_orders[0]->invoices" should have 0 elements
    And   the JSON node "data->customer_orders[0]->last_internal_comment" should have 0 elements
    And   the JSON node "data->customer_orders[0]->last_customer_comment" should have 0 elements

  Scenario: Load list including dependencies
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-orders/with-store-pickup" with body:
    """
    {
      "where": {
        "_and": [
          {
            "warehouse_id": {
              "_eq": 1
            }
          }
        ]
      },
      "included_dependencies": ["articles", "delivery_notes", "invoices", "payments", "last_internal_comment", "last_customer_comment"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->customer_orders" should have 2 elements
    # check main keys (customer order)
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].customer_order_id | 2 |

    # check dependencies
    # payments
    And   the JSON node "data->customer_orders[1]->payments" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].payments[0]->customer_order_id  | 2                   |
      | data.customer_orders[1].payments[0]->payment_id         | 11                  |
      | data.customer_orders[1].payments[0]->payment_name       | CTPE                |
      | data.customer_orders[1].payments[0]->amount             | 249                 |
      | data.customer_orders[1].payments[0]->created_at         | 2018-09-11 14:36:23 |
      | data.customer_orders[1].payments[0]->accepted_at        | 2019-10-08 19:15:40 |
      | data.customer_orders[1].payments[0]->auto_status        |                     |
      | data.customer_orders[1].payments[0]->auto_status_detail |                     |
      | data.customer_orders[1].payments[0]->auto_warranty      |                     |

    # articles
    And   the JSON node "data->customer_orders[1]->articles" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].articles[0]->customer_order_id  | 2             |
      | data.customer_orders[1].articles[0]->article_id         | 81078         |
      | data.customer_orders[1].articles[0]->sku                | ARCAMRBLINKNR |
      | data.customer_orders[1].articles[0]->quantity           | 1             |
      | data.customer_orders[1].articles[0]->short_description  | Arcam rBlink  |
      | data.customer_orders[1].articles[0]->available_quantity | 3             |

    # delivery notes
    And   the JSON node "data->customer_orders[1]->delivery_notes" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].delivery_notes[0]->customer_order_id       | 2                   |
      | data.customer_orders[1].delivery_notes[0]->delivery_note_id        | 123                 |
      | data.customer_orders[1].delivery_notes[0]->carrier_id              | 5                   |
      | data.customer_orders[1].delivery_notes[0]->carrier_name            | Emport dépôt        |
      | data.customer_orders[1].delivery_notes[0]->shipment_method_id      | 31                  |
      | data.customer_orders[1].delivery_notes[0]->shipment_method_name    | Emport Dépôt        |
      | data.customer_orders[1].delivery_notes[0]->validated_at            | 2019-08-29 20:15:09 |
      | data.customer_orders[1].delivery_notes[0]->validated_by            | backoffice          |
      | data.customer_orders[1].delivery_notes[0]->is_prepared             | true                |
      | data.customer_orders[1].delivery_notes[0]->parcel_tracking_number  |                     |
      | data.customer_orders[1].delivery_notes[0]->store_pickup_started_at |                     |
      | data.customer_orders[1].delivery_notes[0]->workflow_status         | CREATED             |

    # invoices
    And   the JSON node "data->customer_orders[1]->invoices" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].invoices[0]->customer_order_id | 2                   |
      | data.customer_orders[1].invoices[0]->invoice_id        | 456                 |
      | data.customer_orders[1].invoices[0]->invoice_type      | facture             |
      | data.customer_orders[1].invoices[0]->delivery_note_id  | 123                 |
      | data.customer_orders[1].invoices[0]->created_at        | 2018-04-18 15:09:03 |

    # last internal comment
    And   the JSON node "data->customer_orders[1]->last_internal_comment" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].last_internal_comment[0]->customer_order_id | 2                   |
      | data.customer_orders[1].last_internal_comment[0]->created_by        | Seigneur ADMIN      |
      | data.customer_orders[1].last_internal_comment[0]->created_at        | 2021-04-01 19:54:37 |
      | data.customer_orders[1].last_internal_comment[0]->message           | ASDF                |

    # last customer comment
    And   the JSON node "data->customer_orders[1]->last_customer_comment" should not be null
    And   the JSON nodes should be equal to:
      | data.customer_orders[1].last_customer_comment[0]->customer_order_id | 2                   |
      | data.customer_orders[1].last_customer_comment[0]->created_at        | 2021-04-03 00:35:38 |
      | data.customer_orders[1].last_customer_comment[0]->message           | BAR                 |
