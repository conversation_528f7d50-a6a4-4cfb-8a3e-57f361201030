Feature: Update a stock move

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "stock_move/cpost_stock_moves.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test with a decimal number and permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205" with body:
    """
    {
      "buy_price": 43.3
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->new_weighted_cost" should be equal to 0.01

  Scenario: Test with a int number and permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205" with body:
    """
    {
      "buy_price": 44
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->new_weighted_cost" should be equal to 0.01


  Scenario: Test with a string and permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/stock-move/7551205" with body:
    """
    {
      "buy_price": "toto"
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON


