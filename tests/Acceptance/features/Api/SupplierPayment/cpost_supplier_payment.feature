Feature: Get list of supplier payment

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "supplier/cpost_suppliers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-payment"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-payment"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/supplier-payment"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for suppliers API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-payment" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->supplier_payment" should have 1 elements
    And   the JSON node "data->supplier_payment[0]" should be identical to
    """
    {
      "supplier_payment_id": 1,
      "payment": "Indéfini"
    }
    """

  Scenario: Check successful response for suppliers API with filter on brand id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-payment" with body:
    """
    {
      "where": {
        "supplier_payment_id": {
          "_eq": "2"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_payment" should have 1 elements
    And   the JSON node "data->supplier_payment[0]" should be identical to
    """
    {
      "supplier_payment_id": 2,
      "payment": "Virement"
    }
    """
