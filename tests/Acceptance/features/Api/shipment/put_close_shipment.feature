# see tests/Unit/SonVideo/Erp/Shipment/Manager/ShipmentCloseManager.php


Feature: Close a shipment 
  As someone identified in the ERP

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "shipment/cpost_shipments.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/shipment/0/close" 
    Then  the response status code should be 401

  Scenario: Test without premission
   When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/shipment/9017/close" 
    Then  the response status code should be 403

  Scenario: Close a valid shipment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I send a "PUT" request to "/api/v1/shipment/9017/close" 
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"

  Scenario: try to close an unknown shipment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I send a "PUT" request to "/api/v1/shipment/0/close" 
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And the JSON should be equal to:
    """
    {
        "status": "error",
        "message": "Shipment 0 not found",
        "code": 404,
        "data": []
    }
    """

  Scenario: try to close a valid shipment
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I send a "PUT" request to "/api/v1/shipment/9018/close" 
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"