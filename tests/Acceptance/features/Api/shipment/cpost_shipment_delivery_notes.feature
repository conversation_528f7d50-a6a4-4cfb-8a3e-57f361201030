Feature: Retrieve a filtered list of shipments
  As someone identified in the ERP
  I want to list and filter the shipment delivery notes available in the system

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "shipment/cpost_shipment_delivery_notes.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment/9018/delivery-notes"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment/9018/delivery-notes"
    Then  the response status code should be 401

  Scenario: Test without premission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/shipment/9018/delivery-notes"
    Then  the response status code should be 403

  Scenario: Get list of shipment delivery notes without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "SHIPMENT_READ" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment/9018/delivery-notes"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->shipment_delivery_notes" should have 4 elements
    And   the JSON node "data->shipment_delivery_notes[1]" should have the following keys and values
      """
      {
      "shipment_delivery_note_id": 391953,
      "delivery_note_id": 4403189,
      "parcel_weight": 0.46,
      "shipment_method_id": 9,
      "shipment_method_name": "DHL Express 24h",
      "parcel_quantity": 4,
      "parcels": [
      {
      "id": 455848,
      "number": "B"
      },
      {
      "id": 455849,
      "number": "C"
      }
      ],
      "status": 1,
      "have_insurance": false,
      "customer": {
      "city": "NANTES",
      "email": "<EMAIL>",
      "phone": "0155092043",
      "address": [
      "38 rue de la ville en bois",
      "",
      "",
      ""
      ],
      "country": "FRANCE",
      "company": "PHC Holding",
      "civility": "M.",
      "lastname": "Liguili",
      "cellphone": "0155092043",
      "firstname": "Guy",
      "country_id": 67,
      "postal_code": "44100"
      }
      }
      """