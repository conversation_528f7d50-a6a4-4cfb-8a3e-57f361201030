Feature: Get list of payments from the v2 API
  As someone identified in the ERP
  I want to see the users and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And  I load mysql fixtures from file "customer_order/cpost_customer_order_payments.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customer-order-payments"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customer-order-payments"
    Then  the response status code should be 401

  Scenario: Get list of customer order payments without filters
    # Customer order payments requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customer-order-payments"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data->payments" should have 3 elements

  Scenario: Get list of customer order payments filtered by a customer order id
    # Customer order payments requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customer-order-payments" with body:
    """
    {
      "where": {
        "customer_order_id": {
          "_eq": 2
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->payments" should have 2 elements
    And   the JSON node "data->payments[0]" should have the following keys and values
    """
    {
      "customer_order_payment_id": 102,
      "customer_order_id": 2,
      "customer_order_origin": "F905HE21163-A",
      "customer_order_created_at": "1990-01-01 00:00:00",
      "customer_order_total_amount": 0,
      "customer_id": 1,
      "customer_email": "<EMAIL>",
      "unique_id": 1,
      "payment_id": 11,
      "type": "paiement",
      "warehouse_id": null,
      "warehouse_name": null,
      "created_at": "2018-09-11 14:36:23",
      "created_by": "admin",
      "created_amount": 249,
      "created_proof": null,
      "created_origin": "son-video.com",
      "accepted_at": "2019-10-08 19:15:40",
      "accepted_by": "admin",
      "accepted_amount": 249,
      "accepted_proof": "XXXXXX",
      "cancelled_at": "2019-10-08 19:15:40",
      "cancelled_by": "admin",
      "cancelled_amount": 249,
      "remit_asked_at": null,
      "remit_asked_by": null,
      "remitted_at": null,
      "remitted_by": null,
      "remitted_amount": 0,
      "remitted_proof": null,
      "remit_note": null,
      "remit_rate": 1,
      "unpaid_overdue_at": null,
      "unpaid_overdue_by": null,
      "unpaid_overdue_amount": 0,
      "auto_status": null,
      "auto_status_detail": null,
      "auto_warranty": null,
      "auto_warranty_detail": null,
      "country_ip": null,
      "country_country_code": null,
      "payment_mean": "CTPE",
      "payment_description": "Carte terminal de paiement",
      "payment_created_remotely": "N",
      "use_remit_proof": "Y",
      "remit_proof_source": "manuel",
      "remit_proof_type": "No remise",
      "remit_proof_validation_regex": "^\\d{1,64}$",
      "use_remit_note": "N",
      "remit_note_validation_regex": "",
      "computed_customer_firstname": "Henri",
      "computed_customer_lastname": "BAEYENS",
      "computed_customer_name": "BAEYENS Henri",
      "computed_created_by": "Seigneur ADMIN",
      "computed_accepted_by": "Seigneur ADMIN",
      "computed_remitted_by": "",
      "computed_cancelled_by": "Seigneur ADMIN",
      "computed_transaction_status": "CANCELLED",
      "can_be_accepted": false,
      "can_be_cancelled": false,
      "can_be_remitted": false,
      "can_cancel_remit": false,
      "should_be_remitted": false,
      "source_group": null,
      "source": null,
      "remit_error": null
    }
    """

  Scenario: Get list of customer order payments filtered by a customer order id and get back only specified keys
    # Customer order payments requires a specific permission, we use the admin token and not the user2-token-without-permission
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customer-order-payments" with body:
    """
    {
      "where": {
        "customer_order_id": {
          "_eq": 2
        }
      },
      "fields": ["customer_order_payment_id", "accepted_by", "should_be_remitted"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data->payments" should have 2 elements
    And   the JSON node "data->payments[0]" should have the following keys and values
    """
    {
      "customer_order_payment_id": 102,
      "accepted_by": "admin",
      "should_be_remitted": false
    }
    """
