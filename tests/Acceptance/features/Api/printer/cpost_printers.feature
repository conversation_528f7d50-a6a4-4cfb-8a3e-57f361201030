Feature: Get list of printers

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "printer/cpost_printers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/printers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/printers"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/printers"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for countries API with proper type casting
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/printers" with body:
    """
    {
      "where": {
        "_and": [
          {"printer_id": {"_eq": 3}}
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->printers" should have 1 elements
    And   the JSON node "data->printers[0]" should have the following keys and values
    """
    {
      "name": "Zebra_prod_3",
      "printer_id": 3,
      "cups_name": "Zebra_prod_3",
      "warehouse_id": 1,
      "type": "102x152",
      "status": "active",
      "created_at": "2019-08-06 12:57:12"
    }
    """
