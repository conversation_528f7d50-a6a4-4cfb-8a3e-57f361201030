Feature: Get list of quote shipment methods

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/get_quote_shipment_methods.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/quote/13/shipment-methods"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/quote/13/shipment-methods"
    Then  the response status code should be 401

  Scenario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "GET" request to "/api/v1/quote/999/shipment-methods"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "999"'

  <PERSON><PERSON>rio: Test success call
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
        """
        [
          {"shipment_method_id": 1, "cost": 9.9}
        ]
        """
    And   I send a "GET" request to "/api/v1/quote/13/shipment-methods"

    Then  last RPC call service should be "bridge"
    And   last RPC call method should be "carrier:get_carriers_for_order"
    And   last RPC call args node "0" should have 3 elements
    # items
    And   last RPC call args node "0->items" should have 1 element
    And   last RPC call args node "0->items->0->article_id" should be equal to the number "81078"
    And   last RPC call args node "0->items->0->type" should be equal to the string "article"
    And   last RPC call args node "0->items->0->sku" should be equal to the string "ARBITRARYSKU"
    And   last RPC call args node "0->items->0->quantity" should be equal to the number "1"
    And   last RPC call args node "0->items->0->total_price" should be equal to the number "249"
    And   last RPC call args node "0->items->0->price_vat_excluded" should be equal to the number "207.5"
    And   last RPC call args node "0->items->0->price" should be equal to the number "249"
    And   last RPC call args node "0->items->0->order_line_no" should be equal to the number "1"
    And   last RPC call args node "0->items->0->description" should be equal to the string "Récepteur Audio Bluetooth APTX Arcam rBlink"
    # customer
    And   last RPC call args node "0->customer" should have 2 element
    And   last RPC call args node "0->customer->customer_id" should be equal to the number "2"
    And   last RPC call args node "0->customer->email" should be equal to the string "<EMAIL>"
    # shipping address
    And   last RPC call args node "0->shipping_address" should have 8 elements
    And   last RPC call args node "0->shipping_address->title" should be equal to the string "M."
    And   last RPC call args node "0->shipping_address->firstname" should be equal to the string "Alain"
    And   last RPC call args node "0->shipping_address->lastname" should be equal to the string "TERIEUR"
    And   last RPC call args node "0->shipping_address->cellphone" should be equal to the string "0606060606"
    And   last RPC call args node "0->shipping_address->city" should be equal to the string "NANTES"
    And   last RPC call args node "0->shipping_address->postal_code" should be equal to the string "44100"
    And   last RPC call args node "0->shipping_address->address" should be equal to the string "1 rue des fleurs"
    And   last RPC call args node "0->shipping_address->country_code" should be equal to the string "FR"

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->shipment_methods" should have 1 elements
    And   the JSON node "data->shipment_methods[0]" should have the following keys and values
    """
    {
      "shipment_method_id": 1,
      "label": "Colissimo domicile",
      "comment": "Livraison sous 48/72h au domicile remis contre signature",
      "carrier_name": "Colissimo",
      "is_retail_store": false,
      "cost": 9.9
    }
    """
