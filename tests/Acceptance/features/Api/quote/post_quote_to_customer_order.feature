Feature: Create a customer order from a quote

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/post_quote_to_customer_order.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/1/customer-order"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/1/customer-order"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/1/customer-order"
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Try to create a customer order on a non-existant quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/44/customer-order"
    Then  the response should be in JSON
    And   the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Quote not found with id "44"
    """

  Scenario: Cant convert order
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/105/customer-order"
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Quote can not be converted"
    And   the JSON node "code" should be equal to the string "1012"
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "data" should have 5 elements
    And   the JSON node "data" should be identical to
    """
    [
      {
        "error": "expired",
        "message": "The quote has expired"
      },
      {
        "error": "shipping_address_empty",
        "message": "The shipping address can't be empty"
      },
      {
        "error": "billing_address_empty",
        "message": "The billing address can't be empty"
      },
      {
        "error": "shipment_method_empty",
        "message": "The shipment method can't be empty"
      },
      {
        "error": "quote_lines_empty",
        "message": "The quote has no line"
      }
    ]
    """

  Scenario: Create a customer order
    Given I clear RPC cache
    And I expect RPC call to "bo-cms" "quote:upsert" to respond with:
    """
    {
      "updated_quote_id": 13
    }
    """
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/13/customer-order"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to the number "1234"
    And   last "bo-cms->quote:upsert" RPC call args node "0" should be equal to the number "13"

  Scenario: Check draft can be converted with quotation shipment method
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/201/customer-order"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customer_order_id" should be equal to the number "1235"
