Feature: Update quote line product information

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/put_quote_line_product.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 403

  Scenario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/666/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "666"'

  Scenario: Test on a non existing quote line product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/355" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string 'Quote line product not found with id "355"'

  Scenario: Test on a quote line product linked to another quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string 'Mismatch between quote and quote line product'

  Scenario: Try to update with no data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "No data supplied for update"

  Scenario: Try to update with a column that does not exists
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 2,
        "foo": "bar"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Invalid column(s): [foo]. Authorized columns are: [quantity, unit_discount_amount, selected_warranties]"

  Scenario: Update a quote line product with the same value does not raise an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 1
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "0"

  Scenario: Update a quote line product on a "quotation" quote already sent should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/quote-line-product/1" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Quote is locked"

  Scenario: Update a quote line product on an "offer" quote already sent should be ok
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11/quote-line-product/2" with body:
    """
    {
      "data": {
        "quantity": 2
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->updated" should be equal to the number "1"

    # Check if the quote total has been updated
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quotes" with body:
    """
    {
      "where": {
        "_and": [
          {"quote_id": {"_eq": 11}}
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->quotes" should have 1 element
    And   the JSON node "data->quotes[0]->total_tax_included" should be equal to 498

  Scenario: Update a quote line product with an incorrect unit_discount_amount value raises an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "unit_discount_amount": 15.00
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should contain "Unit discount amount must be negative"

  Scenario: Update a quote line product discount amount with margin rate less than 0 raises an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "unit_discount_amount": -91.00
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Margin rate (-0.00029141590117217) can't be less than "0"
    """

  Scenario: Update a quote line product discount amount with margin rate less than 0.05 raises an error
            if user has not QUOTE_DISCOUNT_ADMIN right
    When  I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "unit_discount_amount": -90.00
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Margin rate (0.0060056657223795) can't be less than "0.05"
    """

  Scenario: Update a quote line product
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "quantity": 2,
        "unit_discount_amount": -90.8
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Add/Remove a warranty successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "selected_warranties": [
          {
            "type": "extension",
            "label": "Extension de garantie 5 ans",
            "unit_selling_price_tax_included": 99,
            "duration": 5
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line-product/3" with body:
    """
    {
      "data": {
        "selected_warranties": []
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"
