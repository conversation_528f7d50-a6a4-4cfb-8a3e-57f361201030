Feature: Get list of quote_subtype

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "quote_subtype/cpost_quote_subtype.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote-subtype"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote-subtype"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/quote-subtype"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response with a filter on quote_subtype label
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote-subtype" with body:
    """
        {
      "where": {
        "_and": [
          {"label": {"_like": "%Cla%"}}
        ]
      }
    }

    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->quote_subtype" should have 1 elements
    And   the JSON node "data->quote_subtype[0]" should have the following keys and values
    """
    {
      "type": "CLASSIQUE",
      "label": "Classique"
    }
    """
