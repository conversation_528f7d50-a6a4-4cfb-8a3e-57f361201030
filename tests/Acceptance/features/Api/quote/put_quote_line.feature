Feature: change quote line position

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/put_quote_line.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3" with body:
      """
      {"display_order": 10}
      """
    Then  the response status code should be 403

  <PERSON>enario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/666/quote-line/3" with body:
      """
      {"display_order": 10}
      """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'The quote_line 3 is unknown from quote 666'

  Scenario: Test on a non existing quote line
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/666" with body:
      """
      {"display_order": 10}
      """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'The quote_line 666 is unknown from quote 12'


  Scenario: increase quote line position
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3" with body:
      """
      {"display_order": 10}
      """
    Then  the response status code should be 200
    And   the JSON node "data->quote->quote_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates" should have 12 elements
    And   the JSON node "data->quote->quote_line_aggregates[0]->quote_line_id" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[0]->display_order" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[1]->quote_line_id" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[1]->display_order" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[2]->quote_line_id" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[2]->display_order" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[3]->quote_line_id" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[3]->display_order" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[4]->quote_line_id" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[4]->display_order" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[5]->quote_line_id" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[5]->display_order" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[6]->quote_line_id" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[6]->display_order" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[7]->quote_line_id" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[7]->display_order" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[8]->quote_line_id" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[8]->display_order" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[9]->quote_line_id" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[9]->display_order" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[10]->quote_line_id" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[10]->display_order" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[11]->quote_line_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates[11]->display_order" should be equal to the number "12"

    Scenario: Decrease quote line position
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3" with body:
      """
      {"display_order": 3}
      """
    Then  the response status code should be 200
    And   the JSON node "data->quote->quote_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates" should have 12 elements
    And   the JSON node "data->quote->quote_line_aggregates[0]->quote_line_id" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[0]->display_order" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[1]->quote_line_id" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[1]->display_order" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[2]->quote_line_id" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[2]->display_order" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[3]->quote_line_id" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[3]->display_order" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[4]->quote_line_id" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[4]->display_order" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[5]->quote_line_id" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[5]->display_order" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[6]->quote_line_id" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[6]->display_order" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[7]->quote_line_id" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[7]->display_order" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[8]->quote_line_id" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[8]->display_order" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[9]->quote_line_id" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[9]->display_order" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[10]->quote_line_id" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[10]->display_order" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[11]->quote_line_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates[11]->display_order" should be equal to the number "12"

    Scenario: Increase quote line position over max line size
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3" with body:
      """
      {"display_order": 325}
      """
    Then  the response status code should be 200
    And   the JSON node "data->quote->quote_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates" should have 12 elements
    And   the JSON node "data->quote->quote_line_aggregates[0]->quote_line_id" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[0]->display_order" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[1]->quote_line_id" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[1]->display_order" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[2]->quote_line_id" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[2]->display_order" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[3]->quote_line_id" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[3]->display_order" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[4]->quote_line_id" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[4]->display_order" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[5]->quote_line_id" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[5]->display_order" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[6]->quote_line_id" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[6]->display_order" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[7]->quote_line_id" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[7]->display_order" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[8]->quote_line_id" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[8]->display_order" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[9]->quote_line_id" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[9]->display_order" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[10]->quote_line_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates[10]->display_order" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[11]->quote_line_id" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[11]->display_order" should be equal to the number "12"

    Scenario: Decrease quote line with negative position
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/quote-line/3" with body:
      """
      {"display_order": -681}
      """
    Then  the response status code should be 200
    And   the JSON node "data->quote->quote_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates" should have 12 elements
    And   the JSON node "data->quote->quote_line_aggregates[0]->quote_line_id" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[0]->display_order" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[1]->quote_line_id" should be equal to the number "1"
    And   the JSON node "data->quote->quote_line_aggregates[1]->display_order" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[2]->quote_line_id" should be equal to the number "2"
    And   the JSON node "data->quote->quote_line_aggregates[2]->display_order" should be equal to the number "3"
    And   the JSON node "data->quote->quote_line_aggregates[3]->quote_line_id" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[3]->display_order" should be equal to the number "4"
    And   the JSON node "data->quote->quote_line_aggregates[4]->quote_line_id" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[4]->display_order" should be equal to the number "5"
    And   the JSON node "data->quote->quote_line_aggregates[5]->quote_line_id" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[5]->display_order" should be equal to the number "6"
    And   the JSON node "data->quote->quote_line_aggregates[6]->quote_line_id" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[6]->display_order" should be equal to the number "7"
    And   the JSON node "data->quote->quote_line_aggregates[7]->quote_line_id" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[7]->display_order" should be equal to the number "8"
    And   the JSON node "data->quote->quote_line_aggregates[8]->quote_line_id" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[8]->display_order" should be equal to the number "9"
    And   the JSON node "data->quote->quote_line_aggregates[9]->quote_line_id" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[9]->display_order" should be equal to the number "10"
    And   the JSON node "data->quote->quote_line_aggregates[10]->quote_line_id" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[10]->display_order" should be equal to the number "11"
    And   the JSON node "data->quote->quote_line_aggregates[11]->quote_line_id" should be equal to the number "12"
    And   the JSON node "data->quote->quote_line_aggregates[11]->display_order" should be equal to the number "12"