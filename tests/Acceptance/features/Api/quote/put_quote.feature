Feature: Update quote information

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/put_quote.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10" with body:
    """
    {}
    """
    Then  the response status code should be 403

  Scenario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/666" with body:
    """
    {
      "data": {
        "type": "quotation",
        "foo": "bar"
      }
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "666"'

  Scenario: Try to update with no data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10" with body:
    """
    {}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "No data supplied for update"

  Scenario: Try to update with a column that does not exists
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11" with body:
    """
    {
      "data": {
        "type": "quotation",
        "foo": "bar"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Invalid column(s): [foo]. Authorized columns are: [type, created_by, quote_subtype, expired_at, valid_until, message, billing_address, shipping_address, shipment_method]"

  Scenario: Update a quote on a "quotation" quote already sent should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10" with body:
    """
    {
      "data": {
        "type": "quotation"
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Quote is locked"

  Scenario: Update a quote on an ordered quote should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/14" with body:
    """
    {
      "data": {
        "message": "test"
      }
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Quote is locked"

  Scenario: Update the quote creator of another user should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/16" with body:
    """
    {
      "data": {
        "created_by": 123
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "The user 1 cannot change the creator of the quote 16 which belong to user 2"

  Scenario: Update the creator of a sent quote should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/13" with body:
    """
    {
      "data": {
        "created_by": 2
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "The creator of a sent quote cannot be changed"

  Scenario: Update a quote with the same value does not raise an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11" with body:
    """
    {
      "data": {
        "quote_subtype": "LOAN"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "0"

  Scenario: Update only one column of a quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11" with body:
    """
    {
      "data": {
        "quote_subtype": "INTRAGROUP"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Update a quote partially
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/11" with body:
    """
    {
      "data": {
        "type": "quotation",
        "message": "Very important !!",
        "billing_address": null
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote with a non-default shipment-method id should fail
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 3, "cost": 9.9 }
      }
    }
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'You are not allowed choose this shipment method'


  Scenario: Updating a quote with a non-default shipment-method price should fail if initial cost is not provided
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 1, "cost": 2.2 }
      }
    }
    """
    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string 'You are not allowed choose this shipment method'

  Scenario: Updating a quote with a non-default shipment-method price works when providing an initial_cost (free shipping)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 1, "cost": 0, "initial_cost": 9.9 }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote with a default shipment-method
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 9, "cost": 1.9 }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote with a non-default shipment-method id with quotation right
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add permission "QUOTE_SHIPMENT_METHOD_CUSTOMIZE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 3, "price": 8.8 }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote with a non-default shipment-method price with quotation right
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add permission "QUOTE_SHIPMENT_METHOD_CUSTOMIZE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I expect RPC call to "bridge" "carrier:get_carriers_for_order" to respond with:
    """
    [
      {"shipment_method_id": 1, "cost": 9.9},
      {"shipment_method_id": 9, "cost": 1.9}
    ]
    """
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipment_method": { "shipment_method_id": 1, "cost": 8.8 }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Update a quote with a retail store
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "shipping_address": {
          "firstname": "Jean",
          "lastname": "Valjean",
          "shipment_method_id": 37,
          "address": "3 rue du voleur",
          "city": "Paris",
          "country": {
            "country_code": "FR",
            "country_id": 1,
            "name": "FRANCE"
          },
          "postal_code": "75000",
          "cellphone": "06 01 02 03 04",
          "civility": "Mme"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote's subtype without permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "LOAN"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype from INTRAGROUP to LOAN without right permission1
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "LOAN"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype from INTRAGROUP to LOAN without right permission2
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user1"
    And   I add permission "QUOTE_SUBTYPE_INTRAGROUP_SELECT" to user "user1"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "LOAN"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype from LOAN to INTRAGROUP without right permission1
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user1"
    And   I add permission "QUOTE_SUBTYPE_INTRAGROUP_SELECT" to user "user1"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/17" with body:
    """
    {
      "data": {
        "quote_subtype": "INTRAGROUP"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype from LOAN to INTRAGROUP without right permission2
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/17" with body:
    """
    {
      "data": {
        "quote_subtype": "INTRAGROUP"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype from INTRAGROUP to an invalid subtype
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add permission "QUOTE_SUBTYPE_INTRAGROUP_SELECT" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "FOOBAR"
      }
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should be equal to the string "You are not allowed to change the quote's subtype"

  Scenario: Updating a quote's subtype with permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add permission "QUOTE_SUBTYPE_INTRAGROUP_SELECT" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "CLASSIQUE"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Updating a quote's subtype with permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "QUOTE_WRITE" to user "user2"
    And   I add permission "QUOTE_SUBTYPE_INTRAGROUP_SELECT" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "quote_subtype": "INTRAGROUP"
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"

  Scenario: Update a quote entirely
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12" with body:
    """
    {
      "data": {
        "type": "offer",
        "created_by": 2,
        "expired_at": "2022-12-12 12:12:12",
        "valid_until": 99,
        "message": "Very important !!",
        "billing_address": {
          "firstname": "Jean",
          "lastname": "Valjean",
          "address": "3 rue du voleur",
          "city": "Paris",
          "country": {
            "country_code": "FR",
            "country_id": 1,
            "name": "FRANCE"
          },
          "postal_code": "75000",
          "cellphone": "06 01 02 03 04",
          "civility": "Mme"
        },
        "shipping_address": {
          "firstname": "Jean",
          "lastname": "Valjean",
          "shipment_method_id": 37,
          "address": "3 rue du voleur",
          "city": "Paris",
          "country": {
            "country_code": "FR",
            "country_id": 1,
            "name": "FRANCE"
          },
          "postal_code": "75000",
          "cellphone": "06 01 02 03 04",
          "civility": "Mme"
        },
        "shipment_method": { "shipment_method_id": 10, "price": 4.99 }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "1"
