Feature: Apply a discount globally on all given quote products

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/put_quote_allocate_discount_globally.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally" with body:
    """
    {
      "amount": 10
    }
    """
    Then  the response status code should be 403

  Scenario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/666/allocate-discount-globally" with body:
    """
    {
      "amount": 10
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "666"'

  Scenario: Try to update with no data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/12/allocate-discount-globally" with body:
    """
    {}
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Parameter "amount" of value "NULL" violated a constraint "This value should not be null."
    """

  Scenario: Update a quote line product on a "quotation" quote already sent should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/40/allocate-discount-globally" with body:
    """
    {
      "amount": -10
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "Quote is locked"
    And   the JSON node "code" should be equal to the number "1016"

  Scenario: Update a quote line product with an incorrect unit_discount_amount value raises an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally" with body:
    """
    {
      "amount": 10
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Unit discount amount must be negative, "10" given
    """
    And   the JSON node "code" should be equal to the number "1013"

  Scenario: Update a quote line product discount amount with margin rate less than 0.05 raises an error if user has not QUOTE_DISCOUNT_ADMIN right
    When  I add permission "QUOTE_WRITE" to user "user2"
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/30/allocate-discount-globally" with body:
    """
    {
      "amount": -1150
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Margin rate (0.04) can't be less than "0.05"
    """
    And   the JSON node "code" should be equal to the number "1015"
    And   the JSON node "data" should be identical to
    """
    {
      "margin_rate_with_discount": 0.03999999999999986,
      "min_margin_rate": 0.05,
      "sku": "QACOQ3050INRMT"

    }
    """

  Scenario: Update a quote line product discount amount with margin rate less than 0 raises an error
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/30/allocate-discount-globally" with body:
    """
    {
      "amount": -1201
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Margin rate (-0.00083402835696417) can't be less than "0"
    """
    And   the JSON node "code" should be equal to the number "1015"
    And   the JSON node "data" should be identical to
    """
    {
      "margin_rate_with_discount": -0.0008340283569641747,
      "min_margin_rate": 0,
      "sku": "QACOQ3050INRMT"

    }
    """

  Scenario: Run the discount dispatch in DRY-RUN mode
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally" with body:
    """
    {
      "amount": -350
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "0"

  Scenario: Update a quote line product successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/10/allocate-discount-globally" with body:
    """
    {
      "amount": -350,
      "mode": "NORMAL"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "4"

  Scenario: Update a quote line product successfully with QUOTE_DISCOUNT_ADMIN
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/quote/30/allocate-discount-globally" with body:
    """
    {
      "amount": -1150,
      "mode": "NORMAL"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->updated" should be equal to the number "2"
