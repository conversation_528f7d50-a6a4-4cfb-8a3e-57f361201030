Feature: Create an internal comment on quote

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/put_quote.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/internal-comment"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/internal-comment"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/internal-comment" with body:
    """
    {}
    """
    Then  the response status code should be 403

  Scenario: Test on a non existing quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/666/internal-comment" with body:
    """
    {
      "data": {
        "message": "test"
      }
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "666"'

  Scenario: Try to create with no data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/internal-comment" with body:
    """
    {}
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "No data supplied"

  Scenario: Try to create a system event message
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/internal-comment" with body:
    """
    {
      "data": {
        "message": "test"
      }
    }
    """
    Then  the response status code should be 200
