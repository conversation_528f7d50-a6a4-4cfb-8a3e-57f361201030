Feature: Send quote Email via mailing
  In order to manage our emails
  As a user
  I need to be able to send a quote email via our mailing system or read the errors

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/post_send_quote_email.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/send-email"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/send-email"
    Then  the response status code should be 401

  Scenario: Fails the email because the request payload is invalid
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/666/send-email"
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Quote not found with id "666".'

  Scenario: Fails the email because the quote type "draft" cannot be send
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/13/send-email"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string 'Quote of type "draft" cannot be sent.'

  Scenario: Fails the email because invalid quote of type "quotation" cannot be send
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/14/send-email"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string 'Quote cannot be sent.'

  Scenario: Fails the email because invalid quote of type "offer" cannot be send
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/15/send-email"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string 'Quote cannot be sent.'

  Scenario: Successfully send the email for quote of type "offer"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I flush Synapps events
    And   I clear RPC cache
    And   I expect RPC call to "herald" "email:send" to respond with:
    """
    {
      "data": {
        "Messages": [
          {
            "To": "test",
            "Cc": "test",
            "Bcc": "test",
            "CustomID": "test"
          }
        ]
      }
    }
    """
    And I expect RPC call to "bo-cms" "quote:upsert" to respond with:
    """
    {
      "updated_quote_id": 12
    }
    """
    And I expect RPC call to "bo-cms" "token:generate_for_quotation" to respond with:
    """
    {
      "token": {
        "token": "auieauieauieauie",
        "customer_id": 12,
        "purpose": "AUTOLOGIN",
        "expire_at": {
          "date": "2022-06-02 15:50:59.191912",
          "timezone_type": 1,
          "timezone": "+02:00"
        }
      }
    }
    """
    And   I send a "POST" request to "/api/v1/quote/16/send-email"
    Then  the response status code should be 200

  Scenario: Successfully handle the request and send the quote email
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I flush Synapps events
    And   I clear RPC cache
    And   I expect RPC call to "herald" "email:send" to respond with:
    """
    {
      "data": {
        "Messages": [
          {
            "To": "test",
            "Cc": "test",
            "Bcc": "test",
            "CustomID": "test"
          }
        ]
      }
    }
    """
    And I expect RPC call to "bo-cms" "quote:upsert" to respond with:
    """
    {
      "updated_quote_id": 12
    }
    """
    And I expect RPC call to "bo-cms" "token:generate_for_quotation" to respond with:
    """
    {
      "token": {
        "token": "auieauieauieauie",
        "customer_id": 12,
        "purpose": "AUTOLOGIN",
        "expire_at": {
          "date": "2022-06-02 15:50:59.191912",
          "timezone_type": 1,
          "timezone": "+02:00"
        }
      }
    }
    """
    And   I send a "POST" request to "/api/v1/quote/12/send-email"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   I should have sent a synapps event with subject "quote.sent.erp_server" and payload:
    """
    {
      "quote_id": 12
    }
    """
    And   last "bo-cms->quote:upsert" RPC call args node "0" should be equal to the number "12"
