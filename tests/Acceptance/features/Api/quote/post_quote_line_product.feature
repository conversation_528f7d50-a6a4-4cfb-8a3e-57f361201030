Feature: Add a product to a quote

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "quote/post_quote_line_product.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/10/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/10/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/10/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Try to add a product on a non-existant quote
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/666/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response should be in JSON
    And   the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Quote not found with id "666"
    """

  Scenario: Try to add a product with a non existing sku
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/11/quote-line-product" with body:
    """
    {
      "sku": "DUMMY"
    }
    """
    Then  the response should be in JSON
    And  the response status code should be 404
    And   the JSON node "message" should be equal to the string
    """
    Product not found with sku "DUMMY"
    """

  Scenario: Try to add a product with status 'non'
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/11/quote-line-product" with body:
    """
    {
      "sku": "BWCCM74"
    }
    """
    Then  the response should be in JSON
    And  the response status code should be 400
    And   the JSON node "code" should be equal to the string "1017"
    And   the JSON node "message" should be equal to the string
    """
    An error occurred while attempting to add the sku "BWCCM74" to the quote
    """

  Scenario: Try to add a product with selling price generally observed equal 0
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/11/quote-line-product" with body:
    """
    {
      "sku": "LBCLD25BP"
    }
    """
    Then  the response should be in JSON
    And  the response status code should be 400
    And   the JSON node "code" should be equal to the string "1017"
    And   the JSON node "message" should be equal to the string
    """
    An error occurred while attempting to add the sku "LBCLD25BP" to the quote
    """


  Scenario: insert a quote line product on a "quotation" quote already sent should fail
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/10/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "message" should contain "Quote is locked"

  Scenario: insert a quote line product on a "offer" quote already sent should be ok
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/11/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 200
    ### The added line should contain the same selected warranties
    And   the JSON node "data->quote_lines[1]->data->selected_warranties" should have 1 element
    And   the JSON node "data->quote_lines[1]->data->selected_warranties[0]" should have the following keys and values
    """
    {
      "type": "extension",
      "label": "Extension de garantie 5 ans",
      "duration": 5,
      "unit_selling_price_tax_included": 99
    }
    """

  Scenario: Add product to an empty quote successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/quote-line-product" with body:
    """
    {
      "sku": "ARCAMRBLINKNR"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->quote_lines" should have 1 element
    And   the JSON node "data->quote_lines[0]" should have the following keys and values
    """
    {
      "quote_line_id": 4,
      "display_order": 1,
      "type": "product",
      "data": []
    }
    """

    ### Check data structure
    And   the JSON node "data->quote_lines[0]->data" should have the following keys and values
    """
    {
      "status": "oui",
      "product": [],
      "quantity": 1,
      "product_id": 81078,
      "stock": 3,
      "unit_discount_amount": 0,
      "quote_line_product_id": 4,
      "delay": 0,
      "selected_warranties": [],
      "group_brand": null,
      "unbasketable_reason": "DISCONTINUED"
    }
    """

    ### Check data structure
    And   the JSON node "data->quote_lines[0]->data->product" should have the following keys and values
    """
    {
      "product_id" : 81078,
      "sku": "ARCAMRBLINKNR",
      "image": "/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg",
      "weight": 0.85,
      "description": "Récepteur Audio Bluetooth APTX Arcam rBlink",
      "outsize": false,
      "short_description": "Arcam rBlink",
      "selling_price_tax_included": 249,
      "ecotax_price": 0.15,
      "sorecop_price": 0,
      "vat": 0.2,
      "purchase_price": 111.58,
      "type": "article",
      "eligible_warranties": [],
      "promo_budget" : { "amount": 20, "end_at": "2054-06-08 12:00:00" }
    }
    """

    #### Check added product has no available warranties
    And   the JSON node "data->quote_lines[0]->data->product->eligible_warranties" should have 0 element

  Scenario: Add an additional product, containing at least 1 eligible warranty, to a quote successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/quote-line-product" with body:
    """
    {
      "sku": "FOCASIBATMEVO512NR"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->quote_lines" should have 2 elements

    ### Check that display order is correctly computed
    And   the JSON node "data->quote_lines[1]" should have the following keys and values
    """
    {
      "quote_line_id": 5,
      "display_order": 2,
      "type": "product",
      "data": []
    }
    """

    #### Check added product has available warranties
    And   the JSON node "data->quote_lines[1]->data->product->eligible_warranties" should have 1 element
    And   the JSON node "data->quote_lines[1]->data->product->eligible_warranties[0]" should be identical to
    """
    {
      "type": "extension",
      "label": "Garantie premium 5 ans",
      "unit_selling_price_tax_included": 129,
      "duration": 5
    }
    """

  Scenario: Add a package, with outsize flag, to a quote successfully
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/quote/12/quote-line-product" with body:
    """
    {
      "sku": "ELIPSPRESTIGEPK2I"
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->quote_lines" should have 4 elements
    And   the JSON node "data->quote_lines[3]->data->product" should have the following keys and values
    """
    {
      "sku": "ELIPSPRESTIGEC2I",
      "vat": 0.2,
      "type": "article",
      "image": "/images/ui/uiV3/graphics/no-img-300.png",
      "weight": 52.4,
      "outsize": true,
      "product_id": 72216,
      "description": "Enceinte centrale Elipson Prestige C2i",
      "ecotax_price": 0.3,
      "sorecop_price": 0,
      "purchase_price": 85,
      "short_description": "Elipson Prestige C2i Calvados\nIssue du pack: Prestige Cinema 2i Calvados",
      "eligible_warranties": [
          {
              "type": "extension",
              "label": "Garantie premium 5 ans",
              "duration": 5,
              "unit_selling_price_tax_included": 39
          }
      ],
      "selling_price_tax_included": 199,
      "promo_budget": null
    }
    """
