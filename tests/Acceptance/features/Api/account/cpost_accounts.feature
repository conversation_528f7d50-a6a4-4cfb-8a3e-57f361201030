Feature: Get list of user accounts
  As someone identified in the ERP
  I want to see the users and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts"
    Then  the response status code should be 401

  Scenario: Get list of accounts without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->accounts" should have 4 elements

  Scenario: Get an account filtered by its id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts" with body:
    """
    {
      "where": {
        "user_id": {
          "_eq": 1
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->accounts" should have 1 elements
    And   the JSON node "data->accounts[0]->user_id" should be equal to the number "1"

  Scenario: Get an account filtered by the username
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts" with body:
    """
    {
      "where": {
        "username": {
          "_like": "%geg%"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->accounts" should have 1 elements
    And   the JSON node "data->accounts[0]->user_id" should be equal to the number "2"
    And   the JSON node "data->accounts[0]->username" should be equal to the string "gege"
