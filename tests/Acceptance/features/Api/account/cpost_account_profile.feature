Feature: Get list of user accounts
  As someone identified in the ERP
  I want to update my profile

  @clear-database
  Scenario: Get an account filtered by its id
    When  I load mysql fixtures from file "users.sql"
    And  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/account/1628/profile" with body:
    """
    {
      "barcode": "1628tmp",
      "civility": "Mr.",
      "company": "Son Video Distribution",
      "email": "<EMAIL>",
      "first_name": "<PERSON>",
      "full_name": "<PERSON> BAEYENS",
      "internal_phone_number": null,
      "is_active": true,
      "is_employee": true,
      "job": "",
      "last_name": "Baeyens",
      "mobile_phone_number": null,
      "seller_commission_role": null,
      "seller_commission_role_level": null,
      "signature": "Bonne journée à vous et je reste disponible par mail.",
      "site": "CHAMPIGNY",
      "suppliers": null,
      "user_groups": "inventaire,ean<PERSON><PERSON>,ean<PERSON><PERSON><PERSON>,stats,admin,com,maj_categorie,edite_px_achat,edite_poids,edite_nb_colis,edite_ean,supprime_produit,detaxe_cmd,accepte_cmd,clone_cmd,cloture_cmd,edite_surcout,edite_frais_port,edite_remise_min,fait_bl,fait_facture,fait_avoir,supprime_bl,imprime_bl,valide_bl,declare_bl_perdu,reservation,edite_no_colis,ajoute_pmt,ajoute_rmbt,accepte_pmt,remise_pmt,fournisseur,entree_stock,init_stock,redevance,remise_devis_libre,emport_depot,suivi_msg,reexpedition,admin_tache,lvr_particulier,intragroupe,init_cmd_px_vte,commande_article,cms,prospect_blacklist,newsletter,imprime_code128,sav,solde_produit,datagridDownload,PDT_ART_maj_prixVente,superadmin,envoi_facture,codepromo,impression_cc,magasinier_inventaire,modif_stock_securite,PCT_MDP_update,entete_svd,supprime_pmt_attente,modif_nb_bl_auto,cmd_edition_contact,emport_paris,maj_paiement,svn_symf_mod,html_editor,fdp_devis_libre,cmd_choix_transporteur,supprime_entree_stock,destocke_commande_fournisseur,accepte_pmt_cetelem,crea_frn,option_devis_libre,tpt_devis_libre,commande_fournisseur,annule_bl,utilisateur,vidage_cache,admin_inventaire,associer_commande_a_transfert,bypass_control_bl,refacturation",
      "user_id": 1628,
      "username": "henri.baeyens",
      "warehouse_id": null
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->user_id" should be equal to the number "1628"

  Scenario: Get an account filtered by its id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/accounts" with body:
    """
    {
      "where": {
        "user_id": {
          "_eq": 1628
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->accounts" should have 1 elements
    And   the JSON node "data->accounts[0]->user_id" should be equal to the number "1628"
    And   the JSON node "data->accounts[0]->signature" should be equal to the string "Bonne journée à vous et je reste disponible par mail."
