Feature: Post supplier order
  As someone identified in the ERP
  I want to create a supplier order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/post_supplier_order.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/3/product"
    Then  the response status code should be 401

  Scenario: Test without authorization
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/3/product"
    Then  the response status code should be 403

  Scenario: Create supplier order succeeded
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/3/product" with body:
    """
{
  "product_id": 81078,
  "quantity": 1,
  "price": 10
}
    """
    Then  the response status code should be 201
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->supplier_order_product->supplier_order_product_id" should be equal to the number "7"
    And   the JSON node "data->supplier_order_product->ordered_quantity" should be equal to the number "1"
    And   the JSON node "data->supplier_order_product->bought_price_tax_excluded" should be equal to the number "10"

  Scenario: Update supplier order succeeded
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/3/product" with body:
    """
{
  "product_id": 81078,
  "quantity": 2,
  "price": 13.5
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->supplier_order_product->supplier_order_product_id" should be equal to the number "7"
    And   the JSON node "data->supplier_order_product->ordered_quantity" should be equal to the number "3"
    And   the JSON node "data->supplier_order_product->bought_price_tax_excluded" should be equal to the number "13.5"

  Scenario: Create supplier order but supplier_order doesn't exists
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/666/product" with body:
    """
{
  "product_id": 81078,
  "quantity": 2,
  "price": 13.5
}
    """
    Then  the response status code should be 404


  Scenario: Create supplier order but product doesn't exists
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-order/3/product" with body:
    """
{
  "product_id": 0,
  "quantity": 2,
  "price": 13.5
}
    """
    Then  the response status code should be 500

