Feature: Get list of country

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "country/cpost_countries.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/countries"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/countries"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/countries"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for countries API with proper type casting
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/countries" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "26"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->countries" should have 1 elements
    And   the JSON node "data->countries[0]" should have the following keys and values
    """
    {
      "country_id": 67,
      "name": "FRANCE",
      "country_code": "FR",
      "group": "France",
      "display_order": 1,
      "postal_code_regex": "^[0-9]{5}$",
      "postal_code_info": "5 chiffres"
    }
    """

  Scenario: Check request on country_code works
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/countries" with body:
    """
    {
      "where": {
        "country_code": {
          "_eq": "WF"
        }
      },
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->countries" should have 1 elements
    And   the JSON node "data->countries[0]" should have the following keys and values
    """
    {
      "country_id": 210,
      "name": "WALLIS ET FUTUNA (Îles)",
      "country_code": "WF",
      "group": "France",
      "display_order": 12,
      "postal_code_regex": "^986[0-9]{2}$",
      "postal_code_info": "986 suivi de 2 chiffres"
    }
    """
