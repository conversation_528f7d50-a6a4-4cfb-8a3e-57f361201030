Feature: Get an image from a protected filesystem

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I send a "GET" request to "/api/v1/uploads/images/knowledge-base/toto.jpg"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/uploads/images/knowledge-base/toto.jpg"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have any permission (= authorized!) and image does not exist
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/uploads/images/knowledge-base/nope.bmp"
    Then  the response status code should be 404
    And   the response should contain "Image not found"

  Scenario: Get an image successfully
    When  There is a file in "uploads_s3" named "images/knowledge-base/toto.jpg" from "test.jpg"
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/uploads/images/knowledge-base/toto.jpg"
    Then  the response status code should be 200
    And   the header "Content-type" should be equal to "image/jpeg"
