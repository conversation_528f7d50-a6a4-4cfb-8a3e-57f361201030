Feature: Update customer

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users_backoffice.sql"
    And  I load mysql fixtures from file "customer/customers.sql"
    And   I send a "PUT" request to "/api/v1/customer/25"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/25"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/5" with body:
    """
    {"customer_id":5,"type":"entreprise","blacklist":false}
    """
    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Test on a non existing customer
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/5" with body:
    """
    {"customer_id":55555,"type":"entreprise","blacklist":false}
    """
    Then  the response status code should be 404
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Customer with id 55555 not found."

  Scenario: Check successful response for customers update
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And I add "Content-Type" header equal to "application/json"
    And I send a "PUT" request to "/api/v1/customer/5" with body:
    """
    {"customer_id":5,"accept_marketing_emails":false,"atradius":"Validé","balance_acceptance":false,"blacklist":false,"classification":"Centrale","company_name":"pouet pouet","encours_sfac":5500,"incoterm":"DAT","npai":true,"tva_number":"303","type":"entreprise"}
    """
    Then  the response status code should be 204
