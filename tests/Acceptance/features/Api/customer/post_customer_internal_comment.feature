Feature: Create an internal comment on customer

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "customer/customers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/2/internal-comment"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/2/internal-comment"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/2/internal-comment" with body:
    """
    {
      "message": "test"
    }
    """
    Then  the response status code should be 403

  Scenario: Test on a non existing customer
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/666/internal-comment" with body:
    """
    {
      "message": "test"
    }
    """
    Then  the response status code should be 404
    And   the JSON node "message" should be equal to the string 'Customer with id 666 not found.'

  Scenario: Try to create with no data
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/2/internal-comment" with body:
    """
    {
      "message": ""
    }
    """
    Then  the response status code should be 400
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to "No data supplied"

  Scenario: Try to create a system event message
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer/2/internal-comment" with body:
    """
    {
      "message": "test"
    }
    """
    Then  the response status code should be 204
