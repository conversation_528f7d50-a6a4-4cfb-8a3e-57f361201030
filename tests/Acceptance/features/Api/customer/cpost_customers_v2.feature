Feature: Get list of customers
  As someone identified in the ERP
  I want to see the customers and eventually filter them

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I load mysql fixtures from file "customer/cpost_customers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customers"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v2/customers"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for customers API v2 with proper type casting
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:get_status_newsletter" to respond with:
    """
    {
      "status": "inactive",
      "origin" : null,
      "created_at" : null,
      "last_activity" : null
    }
    """
    And   I send a "POST" request to "/api/v2/customers" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "5"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->customers" should have 1 elements
    And   the JSON node "data->customers[0]" should be identical to
    """
    {
      "customer_id": 1,
      "email_address": "<EMAIL>",
      "type": "particulier",
      "civility": "M.",
      "firstname": "Arthur",
      "lastname": "Baudouin",
      "company_name": "The Entreprise",
      "address": "tmp",
      "zip_code": "44100",
      "country_id": 67,
      "phone": "0200112233",
      "mobile_phone": "0600112233",
      "customer_type": "particulier",
      "is_blacklisted": false,
      "accept_marketing_emails": true,
      "created_at": "1990-01-01 00:00:00",
      "modified_at": "1990-01-01 00:00:00",
      "birthdate": "2022-01-27 00:00:00",
      "computed_name": "M. ARTHUR BAUDOUIN",
      "country_name": "FRANCE",
      "country_code": "FR",
      "has_ongoing_premium_warranty": false,
      "customer_orders_aggregates": null,
      "credit_notes_aggregates": null,
      "newsletter": null,
      "encours_interne": 0,
      "encours_sfac": 0,
      "atradius": "Pas soumis",
      "balance_acceptance": false,
      "classification": null,
      "incoterm": null,
      "npai": false,
      "tva_number": null
    }
    """

  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customers" with body:
    """
    {
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    Optional column with "imposteur" does not exists, Available keys are : "customer_orders_aggregates", "credit_notes_aggregates", "newsletter".
    """

  Scenario: Check successful response for customers API v2 with additional optional columns
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer:get_status_newsletter" to respond with:
    """
    {
      "status": "subscribed",
      "origin" : "erp",
      "created_at" : "2024-01-01 00:00:00",
      "last_activity" : "2024-01-20 00:00:00"
    }
    """
    And   I send a "POST" request to "/api/v2/customers" with body:
    """
    {
      "where": {
        "_and": [
          {"customer_id": {"_eq": 1}}
        ]
      },
      "included_dependencies": ["customer_orders_aggregates", "credit_notes_aggregates", "newsletter"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->customers[0]" should be identical to
    """
    {
      "customer_id": 1,
      "email_address": "<EMAIL>",
      "type": "particulier",
      "civility": "M.",
      "firstname": "Arthur",
      "lastname": "Baudouin",
      "company_name": "The Entreprise",
      "address": "tmp",
      "zip_code": "44100",
      "country_id": 67,
      "phone": "0200112233",
      "mobile_phone": "0600112233",
      "customer_type": "particulier",
      "is_blacklisted": false,
      "accept_marketing_emails": true,
      "created_at": "1990-01-01 00:00:00",
      "modified_at": "1990-01-01 00:00:00",
      "birthdate": "2022-01-27 00:00:00",
      "computed_name": "M. ARTHUR BAUDOUIN",
      "country_name": "FRANCE",
      "country_code": "FR",
      "has_ongoing_premium_warranty": false,
      "customer_orders_aggregates": {
        "count": 3,
        "revenue_generated": 1485.99
      },
      "credit_notes_aggregates": {
        "total_credit": 0,
        "used_credit": 0,
        "remaining_credit": 0
      },
      "newsletter": {
        "status": "subscribed",
        "origin" : "erp",
        "created_at" : "2024-01-01 00:00:00",
        "last_activity" : "2024-01-20 00:00:00"
      },
      "encours_interne": 0,
      "encours_sfac": 0,
      "atradius": "Pas soumis",
      "balance_acceptance": false,
      "classification": null,
      "incoterm": null,
      "npai": false,
      "tva_number": null
    }
    """

  Scenario: Has correct numbers in credit_notes_aggregates when eligible
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v2/customers" with body:
    """
    {
      "where": {
        "_and": [
          {"customer_id": {"_eq": 2}}
        ]
      },
      "included_dependencies": ["credit_notes_aggregates"]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->customers[0]->credit_notes_aggregates" should be identical to
    """
    {
      "total_credit": 545,
      "used_credit": 534.89,
      "remaining_credit": 10.11
    }
    """

 