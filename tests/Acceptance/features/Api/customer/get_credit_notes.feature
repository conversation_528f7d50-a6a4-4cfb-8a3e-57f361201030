Feature: Retrieve a customer's credit notes list
  In order to consult credit notes of a customer
  As a user
  I need to be able to retrieve them through an api

  @clear-database
  Scenario: Can't access without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "customer_order.sql"
    And   I load mysql fixtures from file "customer/get_credit_notes.sql"
    And   I send a "GET" request to "/api/v1/customer/2/credit-notes"
    Then  the response status code should be 401

  Scenario: Can't access with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/customer/2/credit-notes"
    Then  the response status code should be 401

  Scenario: Can access with a no-permission user works (no permission required)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "GET" request to "/api/v1/customer/2/credit-notes"
    Then  the response status code should be 200

  Scenario: Returns an empty list on an unknown customer works
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer/999999999/credit-notes"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "0"
    And   the JSON node "data" pager should have a limit of "999"
    And   the JSON node "data->credit_notes" should have 0 element

  Scenario: Returns an empty list on a customer which have no credit note
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer/1/credit-notes"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "0"
    And   the JSON node "data" pager should have a limit of "999"
    And   the JSON node "data->credit_notes" should have 0 element

  Scenario: Returns complete list ordered by newer first on a customer which have credit notes
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/customer/2/credit-notes"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    # pager
    And   the JSON node "data" pager should have a total of "2"
    And   the JSON node "data" pager should have a limit of "999"
    # full content test
    And   the JSON node "data->credit_notes" should have 2 elements
    And   the JSON node "data->credit_notes[0]" should have 5 elements
    And   the JSON node "data->credit_notes[0]->credit_note_id" should be equal to the number "1755868"
    And   the JSON node "data->credit_notes[0]->created_at" should be equal to "2015-12-23 17:44:00"
    And   the JSON node "data->credit_notes[0]->customer_order_origin" should be equal to the number "1076434"
    And   the JSON node "data->credit_notes[0]->initial_amount" should be equal to the number "16"
    And   the JSON node "data->credit_notes[0]->available_amount" should be equal to the number "10.11"
    And   the JSON node "data->credit_notes[1]->credit_note_id" should be equal to the number "1737672"
    And   the JSON node "data->credit_notes[1]->created_at" should be equal to "2015-12-02 12:18:06"
    And   the JSON node "data->credit_notes[1]->customer_order_origin" should be equal to the number "1060105"
    And   the JSON node "data->credit_notes[1]->initial_amount" should be equal to the number "529"
    And   the JSON node "data->credit_notes[1]->available_amount" should be equal to the number "0"

