Feature: Reply to a customer message
  As someone identified in the ERP
  I want to reply to a customer message

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "customer/customer_messages.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-message/2/reply"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-message/2/reply"
    Then  the response status code should be 401

  Scenario: Check fail response when message is empty
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-message/2/reply" with body:
    """
    {
      "message": ""
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Parameter "message" of value "" violated a constraint "This value should not be blank."
    """
  Scenario: Check fail response when customer message does not exist
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/customer-message/222222/reply" with body:
    """
    {
      "message": "Sed ac pellentesque eros. Nulla a justo nec ipsum rhoncus egestas. Duis consectetur iaculis risus a imperdiet. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. In hac habitasse platea dictumst. Nam sit amet cursus eros, non tristique nunc. Donec bibendum, libero vitae congue maximus, magna dolor convallis est, vitae dapibus ex ipsum sed turpis. Phasellus non turpis in ante dignissim bibendum at rhoncus nibh. Etiam tincidunt eleifend bibendum. Phasellus mattis varius hendrerit. Donec hendrerit maximus ipsum quis maximus. Pellentesque vel arcu dapibus, tincidunt est quis, convallis sapien. Praesent et felis quis odio bibendum volutpat non et diam."
    }
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Customer message is not found"

  Scenario: Check successful response for customers API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I flush Synapps events
    And   I clear RPC cache
    And   I expect RPC call to "herald" "email:send" to respond with:
    """
    {
      "data": {
        "Messages": [
          {
            "To": "test",
            "Cc": "test",
            "Bcc": "test",
            "CustomID": "test"
          }
        ]
      }
    }
    """
    And   I send a "POST" request to "/api/v1/customer-message/2/reply" with body:
    """
    {
      "message": "Sed ac pellentesque eros. Nulla a justo nec ipsum rhoncus egestas. Duis consectetur iaculis risus a imperdiet. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. In hac habitasse platea dictumst. Nam sit amet cursus eros, non tristique nunc. Donec bibendum, libero vitae congue maximus, magna dolor convallis est, vitae dapibus ex ipsum sed turpis. Phasellus non turpis in ante dignissim bibendum at rhoncus nibh. Etiam tincidunt eleifend bibendum. Phasellus mattis varius hendrerit. Donec hendrerit maximus ipsum quis maximus. Pellentesque vel arcu dapibus, tincidunt est quis, convallis sapien. Praesent et felis quis odio bibendum volutpat non et diam."
    }
    """
    Then  the response status code should be 204
