Feature: Delete customer address

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/customer/12/address/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/customer/12/address/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer_addresses:delete" to respond with:
    """
    {
      "status": true
    }
    """
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/customer/12/address/3"
    And   last RPC call service should be "bo-cms"
    And   last RPC call method should be "customer_addresses:delete"
    # customer_id
    And   last RPC call args node "0" should be equal to the number "12"
    # index
    And   last RPC call args node "1" should be equal to the number "3"
    Then  the response status code should be 204

  Scenario: Test delete address 0 (should be authorized)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/customer/12/address/0"
    Then  the response status code should be 204

  Scenario: Test delete address RPC fail
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I clear RPC cache
    And   I expect RPC call to "bo-cms" "customer_addresses:delete" to respond with:
    """
    {
      "status": false
    }
    """
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/customer/12/address/3"
    Then  the response status code should be 500
    And   the JSON node "message" should be equal to the string
    """
    fail to delete address in CMS
    """
