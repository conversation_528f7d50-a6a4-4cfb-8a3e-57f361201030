Feature: Retrieve a filtered list of shipment methods
  As someone identified in the ERP
  I want to list and filter the shipment methods available in the system

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "shipment_method/cpost_shipment_methods.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods"
    Then  the response status code should be 401

  Scenario: Get list of shipment_methods without filters
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "76"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->shipment_methods" should have 50 elements

  Sc<PERSON>rio: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/shipment-methods"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Get list of shipment methods filtered on an id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "where": {
        "_and": [
          {
            "carrier_id": {
              "_eq": 25
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->shipment_methods" should have 4 elements

  Scenario: Get list of quotation shipment methods
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "where": {
        "_and": [
          {
            "tags": {
              "_like": "%\"quotation\"%"
            }
          }
        ]
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "50"
    And   the JSON node "data->shipment_methods" should have 4 elements

  Scenario: Get shipment methods with all columns without dependencies
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "where": {
        "_and": [
          {
            "carrier_id": {
              "_eq": 25
            }
          }
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->shipment_methods" should have 4 elements
    And   the JSON node "data->shipment_methods[0]" should have the following keys and values
    """
    {
      "shipment_method_id": 24,
      "carrier_id": 25,
      "code": "RL",
      "label": "Relais L",
      "is_active": true,
      "comment": "Livraison en relais, 30kg / 120cm / 0,125 m3 max par colis",
      "type": "messagerie",
      "is_mono_parcel": true,
      "store_pickup_id": null,
      "tags": null,
      "carrier": null
    }
    """

  Scenario: Get shipment_methods which returns only specified columns
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "fields": ["shipment_method_id", "code", "label", "is_mono_parcel"],
      "where": {
        "_and": [
          {
            "carrier_id": {
              "_eq": 25
            }
          }
        ]
      },
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->shipment_methods" should have 1 element
    And   the JSON node "data->shipment_methods[0]" should have the following keys and values
    """
    {
      "shipment_method_id": 24,
      "code": "RL",
      "label": "Relais L",
      "is_mono_parcel": true
    }
    """

  Scenario: Attempt to load list but provide an incorrect dependency
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "included_dependencies": ["imposteur"]
    }
    """
    Then  the response status code should be 500
    And   the JSON node "message" should contain the string
    """
    "imposteur" does not exists, available keys are : "carrier"
    """


  Scenario: Get shipment methods with all columns including dependencies
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/shipment-methods" with body:
    """
    {
      "included_dependencies": ["carrier"],
      "where": {
        "_and": [
          {
            "carrier_id": {
              "_eq": 25
            }
          }
        ]
      },
      "limit": 10
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "10"
    And   the JSON node "data->shipment_methods" should have 4 elements
    And   the JSON node "data->shipment_methods[0]" should have the following keys and values
    """
    {
      "shipment_method_id": 24,
      "carrier_id": 25,
      "code": "RL",
      "label": "Relais L",
      "is_active": true,
      "comment": "Livraison en relais, 30kg / 120cm / 0,125 m3 max par colis",
      "type": "messagerie",
      "is_mono_parcel": true,
      "store_pickup_id": null,
      "tags": null,
      "carrier": {}
    }
    """
    And   the JSON node "data->shipment_methods[0]->carrier" should have the following keys and values
    """
    {
      "carrier_id": 25,
      "name": "Mondial Relay",
      "code": "MOREL",
      "is_pick_up": false,
      "is_express": false
    }
    """
