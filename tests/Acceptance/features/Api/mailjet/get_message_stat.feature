Feature: Retrieve the stat of an email from mailjet api

  Scenario: Test without authorization
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/mailjet/message/12345"
    Then the response status code should be 401

  Scenario: Test with a non valid authorization
    When I add "Authorization" header equal to "Bearer unknown-token"
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "GET" request to "/api/v1/mailjet/message/12345"
    Then the response status code should be 401

  Scenario: Retrieve events without permission
    When I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And  I add "Content-Type" header equal to "application/json"
    And  I clear MailjetApiTransactional cache
    And  I expect MailjetApiTransactional call to "messagehistory" with id "123456" to respond with:
    """
    {
      "Count": 2,
      "Data": [
         {
            "Comment": "test",
            "EventAt": 1643710667,
            "EventType": "sent",
            "State": "",
            "Useragent": "",
            "UseragentID": 0
         },
         {
            "Comment": "",
            "EventAt": 1643720666,
            "EventType": "opened",
            "State": "",
            "Useragent": "Mozilla/5.0 (Windows NT 5.1; rv:11.0) Gecko Firefox/11.0 (via ggpht.com GoogleImageProxy)",
            "UseragentID": 1234
         }
      ],
      "Total": 2
    }
    """
    And  I send a "GET" request to "/api/v1/mailjet/message/123456"
    Then the response status code should be 200
    And  the response should be in JSON
    And  the JSON node "status" should be equal to the string "success"
    And  the JSON node "data->events" should have "2" elements
    And  the JSON node "data->events[0]->EventType" should be equal to "sent"
    And  the JSON node "data->events[1]->EventType" should be equal to "opened"
