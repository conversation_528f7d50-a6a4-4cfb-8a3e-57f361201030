Feature: Send Email via mailing
  In order to manage our emails
  As a user
  I need to be able to send an email via our mailing system or read the errors

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/send-mailing" with body:
    """
{
  "key": "none",
  "data": {
    "foo": "bar"
  }
}
    """
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/send-mailing" with body:
    """
{
  "key": "none",
  "data": {
    "foo": "bar"
  }
}
    """
    Then  the response status code should be 401

  Scenario: Fails the email because the request payload is invalid
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/send-mailing" with body:
    """
{
  "key": "none",
  "data": {
    "foo": "bar"
  }
}
    """
    Then  the response status code should be 500
    And   the response should be in JSON
    And   the JSON node "status" should be equal to "error"
    And   the JSON node "code" should be equal to 500
    And   the JSON node "message" should be equal to the string
    """
    Email dispatcher not found for key "none"
    """

  Scenario: Successfully handle the request and sends the requested email
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/send-mailing" with body:
    """
{
  "key": "dummy",
  "data": {
    "foo": "bar"
  }
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->response" should be equal to "none"
