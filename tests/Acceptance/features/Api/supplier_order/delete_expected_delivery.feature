Feature: Delete an expected delivery on a supplier order product
  As someone identified in the ERP with proper right
  I want to be able to delete an expected delivery

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/delete_expected_delivery.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/2"
    Then  the response status code should be 401

  Scenario: Api requires a permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/2"
    Then  the response status code should be 403

  Scenario: Delete works
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/2"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->deleted" should be equal to the number "1"

  Scenario: Delete fails on unknown expected delivery
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/99999"
    Then  the response status code should be 404
    And   the JSON node "message" should contain 'Expected delivery not found'

  Scenario: Does not allow to delete a delivered quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/3"
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'La quantite livree couvre cette date (tout ou partiellement)'

  Scenario: Does not allow to delete a partially delivered quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/4"
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'La quantite livree couvre cette date (tout ou partiellement)'

  Scenario: Delete works on 2011-01-01 even if "delivered"
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "DELETE" request to "/api/v1/expected-delivery/7"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->deleted" should be equal to the number "1"
