Feature: Post supplier order
  As someone identified in the ERP
  I want to create a supplier order

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/post_supplier_order.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order"
    Then  the response status code should be 401

  Scenario: Test without authorization
    And   I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order"
    Then  the response status code should be 403

  Scenario: Create supplier order succeeded
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order" with body:
    """
{
  "supplier_id": 162
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->supplier_order_id" should be equal to the number "4"

  Scenario: Update supplier order succeeded
    And   I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order" with body:
    """
{
  "supplier_id": "163"
}
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->supplier_order_id" should be equal to the number "3"
