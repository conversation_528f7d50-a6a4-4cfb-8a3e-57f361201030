Feature: Create new expected dates on a supplier order
  As someone identified in the ERP with proper right
  I want to add multiple expected delivery quantities of products at a single date

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    When  I load mysql fixtures from file "supplier_order/cpost_supplier_order_products.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries"
    Then  the response status code should be 401

  Scenario: Api requires a permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries"
    Then  the response status code should be 403

  Scenario: Returns an error when creating on a nonexistent supplier product line
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/999999/expected-deliveries" with body:
    """
    {
      "date": "2011-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 999999, "quantity": 10}
      ]
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should be equal to the string 'Unknown supplier product line'

  Scenario: Does not allow to create an expected delivery in the past
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2021-09-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 2, "quantity": 0}
      ]
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'La date ne peux pas etre dans le passe (sauf 2011-01-01)'

  Scenario: Does not allow to create an expected delivery with improper quantity
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2011-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 2, "quantity": 0}
      ]
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'La quantite doit etre superieure ou egale a 1'

  Scenario: Does not allow to create an expected delivery with more quantity than the rest
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2050-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 2, "quantity": 10}
      ]
    }
    """
    Then  the response status code should be 400
    And   the JSON node "code" should be equal to "1000"
    And   the JSON node "message" should contain 'La quantite depasse celle commandee'

  Scenario: Create multiple expected dates at ones
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2050-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 2, "quantity": 5},
        {"supplier_order_product_id": 3, "quantity": 5}
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->created" should be equal to the number "2"

  Scenario: Can create expected delivery on 2011-01-01
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2011-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 3, "quantity": 1}
      ]
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data->created" should be equal to the number "1"

  Scenario: Does not allow to create an expected delivery on an already existing date
    When I add "Authorization" header equal to "Bearer user1-token-admin"
    And  I add "Content-Type" header equal to "application/json"
    And  I send a "POST" request to "/api/v1/supplier-order/1/expected-deliveries" with body:
    """
    {
      "date": "2050-01-01",
      "expected_deliveries": [
        {"supplier_order_product_id": 3, "quantity": 1}
      ]
    }
    """
    Then the response status code should be 400
    And  the JSON node "code" should be equal to "1000"
    And  the JSON node "message" should be equal to the string 'Date already exists for at least one supplier product line'