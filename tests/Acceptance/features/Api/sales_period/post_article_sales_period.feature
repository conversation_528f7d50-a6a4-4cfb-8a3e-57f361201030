Feature: Create a article sales period

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "sales_period/article_sales_period.sql"
    And   I send a "POST" request to "/api/v1/article/81123/article-sales-period"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/article/81123/article-sales-period"
    Then  the response status code should be 401

  Scenario: Test on a non existing article
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/666666/article-sales-period" with body:
    """
    {
      "is_active": false,
      "sales_period_id": 3,
      "selling_price": 123.0
      }
    """
    Then  the response status code should be 404

  Scenario: Create a article sales
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/article-sales-period" with body:
    """
    {
      "is_active": false,
      "sales_period_id": 3,
      "selling_price": 369.0
    }
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article_sales" should be equal to the number "5"


  Scenario: Create a article sales with a price higher than the current sale price
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "POST" request to "/api/v1/article/81123/article-sales-period" with body:
    """
    {
      "is_active": true,
      "sales_period_id": 3,
      "selling_price": 499.0
    }
    """

    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string "Invalid parameters"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "selling_price": "[key:price_too_high_initial_selling_price] value \"499\" : price is too high compared to initial selling price"
      }
    }
    """
