Feature: Get list of sales

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_period/cpost_sales_period.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for sales API with proper type casting
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales" with body:
    """
    {
      "limit": 5
    }
    """
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->sales" should have 5 elements
    And   the JSON node "data->sales[0]" should be identical to
    """
    {
      "sale_id": 41,
      "end_at": "2022-02-08 22:44:00",
      "start_at": "2022-01-12 07:59:00"
    }
    """
