Feature: Get list of article sales periods

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "sales_period/article_sales_period.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-periods/articles"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-periods/articles"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-periods/articles"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for sales API with proper type casting
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/sales-periods/articles" with body:
    """
    {
      "where": {
        "article_id": {
          "_eq": 81123
        }
      }
    }
    """

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->article_sales_periods" should have 1 elements
    And   the JSON node "data->article_sales_periods[0]" should be identical to
    """
    {
      "article_id": 81123,
      "sale_id": 2,
      "is_active": false,
      "order_quantity": 0,
      "sales_period_id": 2,
      "stock_30d_quantity": 0,
      "selling_price": 123,
      "initial_selling_price": 389
    }
    """
