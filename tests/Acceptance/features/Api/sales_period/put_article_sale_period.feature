Feature: Update article information (sale)

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And   I load mysql fixtures from file "sales_period/article_sales_period.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/3"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/3"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I remove all permissions from user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/2" with body:
    """
    {"is_active": 1, "selling_price": 119.0}
    """

    Then  the response status code should be 403
    And   the response should be in JSON

  Scenario: Try to update with a missing article_sales column
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/2" with body:
    """
    {}
    """

    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
         "is_active": "This value should not be null.",
         "selling_price": "This value should not be null."
      }
    }
    """

  Scenario: Try to update with an article sales for another article
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/4" with body:
    """
    {"is_active": 1, "selling_price": 119.0}
    """

    Then  the response status code should be 404
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "message" should be equal to the string
    """
    Article sales id "4" does not belong to the article with id "81123".
    """

  Scenario: Try to update with a price higher than the current sale price
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/2" with body:
    """
    {"is_active": 1, "selling_price": 999.0}
    """

    Then  the response status code should be 400
    And   the JSON node "status" should be equal to the string "error"
    And   the JSON node "data" should be identical to
    """
    {
      "validation_errors": {
        "selling_price": "[key:price_too_high_initial_selling_price] value \"999\" : price is too high compared to initial selling price"
      }
    }
    """

  Scenario: Successfully update article sale selling price
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_BUYERS_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/article/81123/article-sales-period/2" with body:
    """
    {"is_active": 0, "selling_price": 119.0}
    """

    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" should be identical to
    """
    {"updated": 1}
    """
