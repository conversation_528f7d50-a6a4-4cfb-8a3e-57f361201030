Feature: Retrieve brands by supplier id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "brand/get_suppliers_by_brand_id.sql"
    And   I send a "GET" request to "/api/v1/supplier/400/brands"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/supplier/400/brands"
    Then  the response status code should be 401

  Scenario: Retrieve brands by supplier id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/supplier/400/brands"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data.brands[0]" should be identical to
    """
    {
      "brand_id": 262,
      "name": "<PERSON>am",
      "status": "oui"
    }
    """
