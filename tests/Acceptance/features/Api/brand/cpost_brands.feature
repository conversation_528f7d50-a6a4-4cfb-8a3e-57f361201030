Feature: Get list of brands

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "brand/cpost_brands.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/brands"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/brands"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/brands"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for brands API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/brands" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "4"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->brands" should have 1 elements
    And   the JSON node "data->brands[0]" should be identical to
    """
    {
      "brand_id": 262,
      "name": "Arcam",
      "status": "oui"
    }
    """

  Scenario: Check successful response for brands API with filter on brand id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/brands" with body:
    """
    {
      "where": {
        "brand_id": {
          "_eq": "520"
        }
      }
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->brands" should have 1 element
    And   the JSON node "data->brands[0]->brand_id" should be equal to "520"
    And   the JSON node "data->brands[0]->name" should be equal to "NorStone"
