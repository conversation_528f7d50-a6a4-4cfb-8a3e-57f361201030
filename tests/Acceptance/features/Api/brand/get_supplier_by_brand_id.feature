Feature: Retrieve an supplier by brand id

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "brand/get_suppliers_by_brand_id.sql"
    And   I send a "GET" request to "/api/v1/brand/262/suppliers"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "GET" request to "/api/v1/brand/262/suppliers"
    Then  the response status code should be 401

  Scenario: Retrieve an supplier by brand id
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I send a "GET" request to "/api/v1/brand/262/suppliers"
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "data.suppliers[0]" should be identical to
    """
    {
      "supplier_id": 400,
      "name": "LA BOITE CONCEPT",
      "status": "oui",
      "origin_country_id": 67
    }
    """
    And   the JSON node "data.suppliers[1]" should be identical to
    """
    {
      "supplier_id": 162,
      "name": "PPL",
      "status": "oui",
      "origin_country_id": 67
    }
    """
