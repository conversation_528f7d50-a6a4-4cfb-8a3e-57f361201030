Feature: Update a supplier contract

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "supplier/put_suppliers.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-contract/252"
    Then  the response status code should be 401

  Scenario: Test without permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "PUT" request to "/api/v1/supplier-contract/252"
    Then  the response status code should be 403

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    When  I send a "PUT" request to "/api/v1/supplier-contract/252"
    Then  the response status code should be 403

  Scenario: Edit a supplier contract
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add permission "ARTICLE_PRICES_WRITE" to user "user2"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "PUT" request to "/api/v1/supplier-contract/252" with body:
    """
    {
      "supplier_id": 252,
      "brand_id": null,
      "supplier_contract_id": 2,
      "payment": {
        "supplier_id": 252,
        "name": "AVI Champigny",
        "status": "oui",
        "origin_country_id": 67,
        "discount_rate": 0,
        "franco": 0,
        "delivery_cost": "0",
        "discount_payment_deadline": null,
        "comment": "",
        "supplier_payment_id": 1,
        "payment_deadline_id": 1
      },
      "year": 2025,
      "discount_description": null,
      "pam": {
        "website_promotion": 2,
        "newsletters": 0,
        "blog": 3,
        "stores_presence": 0,
        "training": 0,
        "sales_inventory_reporting": 5,
        "comment": ""
      },
      "rfa": {
        "landing": [
          {
            "landing": 1,
            "starting_value": 0,
            "end_value": 100000,
            "rate_value": 1
          },
          {
            "landing": 2,
            "starting_value": 100001,
            "end_value": 200000,
            "rate_value": 2
          },
          {
            "landing": 3,
            "starting_value": 200001,
            "end_value": 300000,
            "rate_value": 3
          },
          {
            "landing": 4,
            "starting_value": 300001,
            "end_value": 400000,
            "rate_value": 4
          },
          {
            "landing": 5,
            "starting_value": 400001,
            "end_value": 500000,
            "rate_value": 5
          }
        ],
        "comment": ""
      },
      "additional_rewards": [],
      "unconditional_discount": 0
    }
    """
    Then  the response status code should be 204
