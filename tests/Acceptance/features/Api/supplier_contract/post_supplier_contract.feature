Feature: Create a supplier contract

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "supplier/post_suppliers.sql"
    And   I send a "POST" request to "/api/v1/supplier-contract"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I send a "POST" request to "/api/v1/supplier-contract"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    When  I send a "POST" request to "/api/v1/supplier-contract"
    Then  the response status code should be 403

  Scenario: Create a supplier contract
    When  I add "Authorization" header equal to "Bearer user1-token-admin"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contract" with body:
    """
    {
      "supplier_id": 400,
      "brand_id": null,
      "payment": {
        "supplier_id": 400,
        "name": "LA BOITE CONCEPT",
        "status": "oui",
        "origin_country_id": 67,
        "discount_rate": 0,
        "franco": 0,
        "delivery_cost": "0",
        "discount_payment_deadline": null,
        "comment": "",
        "supplier_payment_id": 2,
        "payment_deadline_id": 2
      },
      "year": 2025,
      "discount_description": null,
      "pam": {
        "website_promotion": 2,
        "newsletters": 0,
        "blog": 3,
        "stores_presence": 0,
        "training": 0,
        "sales_inventory_reporting": 5,
        "comment": ""
      },
      "rfa": {
        "landing": [
          {
            "landing": 1,
            "starting_value": 0,
            "end_value": 100000,
            "rate_value": 1
          },
          {
            "landing": 2,
            "starting_value": 100001,
            "end_value": 200000,
            "rate_value": 2
          },
          {
            "landing": 3,
            "starting_value": 200001,
            "end_value": 300000,
            "rate_value": 3
          },
          {
            "landing": 4,
            "starting_value": 300001,
            "end_value": 400000,
            "rate_value": 4
          },
          {
            "landing": 5,
            "starting_value": 400001,
            "end_value": 500000,
            "rate_value": 5
          }
        ],
        "comment": ""
      },
      "additional_rewards": [],
      "unconditional_discount": 0.00
    }
    """
    Then  the response status code should be 200
    And the JSON node "status" should be equal to the string "success"
    And the JSON node "data->supplier_contract_id" should be equal to the number "1"
