Feature: Get list of supplier contracts

  @clear-database
  Scenario: Test without authorization
    When  I load mysql fixtures from file "users.sql"
    And   I load mysql fixtures from file "supplier_contract/cpost_supplier_contracts.sql"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contracts"
    Then  the response status code should be 401

  Scenario: Test with a non valid authorization
    When  I add "Authorization" header equal to "Bearer unknown-token"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contracts"
    Then  the response status code should be 401

  Scenario: Test with token linked to an account which do not have permission (= authorized!)
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I send a "POST" request to "/api/v1/supplier-contracts"
    Then  the response status code should be 200
    And   the response should be in JSON

  Scenario: Check successful response for suppliers API
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contracts" with body:
    """
    {
      "limit": 1
    }
    """
    Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "3"
    And   the JSON node "data" pager should have a limit of "1"
    And   the JSON node "data->supplier_contracts" should have 1 elements
    And   the JSON node "data->supplier_contracts[0]" should be identical to
    """
    {
      "supplier_contract_id": 1,
      "supplier_id": 252,
      "brand_id": 74,
      "unconditional_discount": 0.00,
      "year": 2024,
      "discount_description": null,
      "pam": null,
      "rfa": null,
      "additional_rewards": null
    }
    """

  Scenario: Check successful response for suppliers API with filter on brand id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contracts" with body:
    """
    {
      "where": {
        "brand_id": {
          "_eq": "74"
        }
      }
    }
    """
      Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_contracts" should have 1 elements
    And   the JSON node "data->supplier_contracts[0]" should be identical to
    """
    {
      "supplier_contract_id": 1,
      "supplier_id": 252,
      "brand_id": 74,
      "unconditional_discount": 0.00,
      "year": 2024,
      "discount_description": null,
      "pam": null,
      "rfa": null,
      "additional_rewards": null
    }
    """

  Scenario: Check successful response for suppliers API with filter on supplier id
    When  I add "Authorization" header equal to "Bearer user2-token-without-permission"
    And   I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/supplier-contracts" with body:
    """
    {
      "where": {
        "supplier_id": {
          "_eq": "400"
        }
      }
    }
    """
      Then  the response status code should be 200
    And   the response should be in JSON
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data" pager should have a total of "1"
    And   the JSON node "data->supplier_contracts" should have 1 elements
    And   the JSON node "data->supplier_contracts[0]" should be identical to
    """
    {
      "supplier_contract_id": 3,
      "supplier_id": 400,
      "brand_id": 520,
      "unconditional_discount": 0.00,
      "year": 2023,
      "discount_description": null,
      "pam": null,
      "rfa": null,
      "additional_rewards": null
    }
    """
