Feature: <PERSON><PERSON>'s webhook for eav_subcategory events
  As Hasura system
  The webhook endpoint support the eav_subcategory events

  Sc<PERSON>rio: Subcategory update event should trigger a deletion when use_filter is false
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:deactivate_facets_for_subcategory" to respond with:
    """
{"deactivated_skus": ["TOTO", "TUTU"]}
    """
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "use_filters": true,
                    "subcategory_id": 4
                },
                "new": {
                    "use_filters": false,
                    "subcategory_id": 4
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->deactivated_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Nothing to do on this event."


  Scenario: Subcategory update event should trigger an update when use_filter is true
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [4],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": ["OTHER"]
}
    """
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "use_filters": false,
                    "subcategory_id": 4
                },
                "new": {
                    "use_filters": true,
                    "subcategory_id": 4
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Nothing to do on this event."


  Scenario: Subcategory create event should trigger an update (use_filter is true)
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [4],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": ["OTHER"]
}
    """
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "use_filters": true,
                    "subcategory_id": 4
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Nothing to do on this event."
