Feature: <PERSON><PERSON>'s webhook for eav_attribute_value events
  As Hasura system
  The webhook endpoint support the eav_attribute_value events

  @clear-database
  Scenario: Load base fixtures
    When I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And I load mysql fixtures from file "eav/base.sql"
    And  I load sql fixtures from file "eav/base.sql"

  Scenario: Attribute value's insertion event
    - should sync the active subcategories which use the attribute
    - should not sync product attribute
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": [],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 45084,
                    "value": "Toto",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 701,
                    "i18n": null
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_attribute_value"
        },
        "table": {
            "schema": "eav",
            "name": "attribute_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last RPC call args node "1" should have 0 element

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

  Scenario:
    Attribute value's update event should
    - sync the active subcategories which use the attribute
    - show a message when no product use the value (eav attributes)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I clear RPC cache
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": [],
  "ignored_skus": []
}
    """
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": [],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 45084,
                    "value": "Toto",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 701,
                    "i18n": null
                },
                "new": {
                    "attribute_value_id": 45084,
                    "value": "Titi",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 701,
                    "i18n": null
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_attribute_value"
        },
        "table": {
            "schema": "eav",
            "name": "attribute_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories facets
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles facets
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 0 element

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "No product use this value, nothing to do."

  Scenario:
    Attribute value's update event should
    - sync the active subcategories which use the attribute (already tested)
    - sync the eav attributes for products which use the attribute value
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I clear RPC cache
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": [],
  "ignored_skus": []
}
    """
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": ["QEDQE1455","QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 17173,
                    "value": "Fourchette",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 734,
                    "i18n": null
                },
                "new": {
                    "attribute_value_id": 17173,
                    "value": "Fourche",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 734,
                    "i18n": null
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_attribute_value"
        },
        "table": {
            "schema": "eav",
            "name": "attribute_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"

    # attributes updates
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0" should have 2 elements
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->sku" should be equal to the string "QEDQE1455"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes" should have 5 elements
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->1->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->1->eav_attributes" should have 4 elements

    And   the JSON node "data->attributes[0]->updated_skus" should exist
    And   the JSON node "data->attributes[0]->ignored_skus" should exist

  Scenario: Attribute value's delete event
    - should sync the active subcategories which use the attribute
    - should not sync product attribute
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": [],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "attribute_value_id": 45084,
                    "value": "Toto",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 701,
                    "i18n": null
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_attribute_value"
        },
        "table": {
            "schema": "eav",
            "name": "attribute_value"
        }
    }
    """
    Then  the response status code should be 200

    And   last RPC call service should be "bo-cms"
    And   last RPC call method should be "eav:update_facets"
    # subcategories
    And   last RPC call args node "0" should have 1 element
    And   last RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last RPC call args node "1" should have 0 element

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

  Scenario: Attribute value's insertion event
    - should do nothing if linked subcategory is not active
    - should not sync product attribute
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 99999,
                    "value": "Toto",
                    "display_order": 110,
                    "meta": {},
                    "attribute_id": 935,
                    "i18n": null
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_attribute_value"
        },
        "table": {
            "schema": "eav",
            "name": "attribute_value"
        }
    }
    """
    Then  the response status code should be 200

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "The linked subcategories does not use the filters. Nothing to do."
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."
