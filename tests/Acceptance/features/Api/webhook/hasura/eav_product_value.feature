Feature: <PERSON><PERSON>'s webhook for eav_product_value events
  As Hasura system
  The webhook endpoint support the eav_product_value events

  @clear-database
  Scenario: Load base fixtures
    When I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And I load mysql fixtures from file "eav/base.sql"
    And  I load sql fixtures from file "eav/base.sql"

  Scenario: Product value's insert event
    - should do nothing on facets if subcategory is inactive
    - should update products attributes
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": ["YAMEPH100SI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 18688,
                    "sku": "YAMEPH100SI"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    # facets updates
    And   the JSON node "data->facets->message" should be equal to the string "The linked subcategory does not use the filters. Nothing to do."

    # attributes updates
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->sku" should be equal to the string "YAMEPH100SI"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes" should have 2 elements
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->attribute_id" should be equal to the number "908"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values->0->attribute_value_id" should be equal to the number "23334"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->attribute_id" should be equal to the number "935"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values->0->attribute_value_id" should be equal to the number "25292"

    And   the JSON node "data->attributes->updated_skus" should exist
    And   the JSON node "data->attributes->ignored_skus" should exist
    And   the JSON node "data->attributes->updated_skus[0]" should be equal to the string "YAMEPH100SI"

  Scenario: Product value's insert event
    - should do nothing if attribute is not active
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 17172,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Not linked to an active attribute or deleted value. Nothing to do."

  Scenario: Product value's insert event
    - should sync article if subcategory and attribute are both actived
    - should update product attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 24288,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist

  Scenario: Product value's insert event
    - should sync article if attribute value can't be found
    - should update product attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "INSERT",
            "data": {
                "old": null,
                "new": {
                    "attribute_value_id": 999999,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist


  Scenario: Product value's delete event
    - should do nothing if subcategory is inactive
    - should update product attributes
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": ["YAMEPH100SI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "attribute_value_id": 23334,
                    "sku": "YAMEPH100SI"
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    # facets updates
    And   the JSON node "data->facets->message" should be equal to the string "The linked subcategory does not use the filters. Nothing to do."

    # attributes updates
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->sku" should be equal to the string "YAMEPH100SI"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes" should have 2 elements
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->attribute_id" should be equal to the number "908"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values->0->attribute_value_id" should be equal to the number "23334"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->attribute_id" should be equal to the number "935"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values->0->attribute_value_id" should be equal to the number "25292"

    And   the JSON node "data->attributes->updated_skus" should exist
    And   the JSON node "data->attributes->ignored_skus" should exist
    And   the JSON node "data->attributes->updated_skus[0]" should be equal to the string "YAMEPH100SI"

  Scenario: Product value's delete event
    - should do nothing if attribute is not active
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "attribute_value_id": 17173,
                    "sku": "QEDQE6119"
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Not linked to an active attribute or deleted value. Nothing to do."

  Scenario: Product value's delete event
    - should sync article if subcategory and attribute are both actived
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "attribute_value_id": 1356,
                    "sku": "QEDQE6119"
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist

  Scenario: Product value's delete event
    - should sync article if attribute value can't be found
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "attribute_value_id": 999999,
                    "sku": "QEDQE6119"
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist


  Scenario: Product value's update event
    - should do nothing if subcategory is inactive
    - should update product attributes
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": ["YAMEPH100SI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 23334,
                    "sku": "YAMEPH100SI"
                },
                "new": {
                    "attribute_value_id": 23334,
                    "sku": "YAMEPH100SI"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    # facets updates
    And   the JSON node "data->facets->message" should be equal to the string "The linked subcategory does not use the filters. Nothing to do."

    # attributes updates
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->sku" should be equal to the string "YAMEPH100SI"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes" should have 2 elements
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->attribute_id" should be equal to the number "908"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->0->values->0->attribute_value_id" should be equal to the number "23334"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->attribute_id" should be equal to the number "935"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes->1->values->0->attribute_value_id" should be equal to the number "25292"

    And   the JSON node "data->attributes->updated_skus" should exist
    And   the JSON node "data->attributes->ignored_skus" should exist
    And   the JSON node "data->attributes->updated_skus[0]" should be equal to the string "YAMEPH100SI"

  Scenario: Product value's update event
    - should do nothing if attribute is not active
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 17173,
                    "sku": "QEDQE6119"
                },
                "new": {
                    "attribute_value_id": 17172,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Not linked to an active attribute or deleted value. Nothing to do."

  Scenario: Product value's update event
    - should sync article if subcategory and attribute are both actived
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 1356,
                    "sku": "QEDQE6119"
                },
                "new": {
                    "attribute_value_id": 1363,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist

  Scenario: Product value's update event
    - should sync article if attribute value can't be found
    - should update products attributes (already tested)
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [],
  "updated_skus": ["QEDQE6119"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "attribute_value_id": 1356,
                    "sku": "QEDQE6119"
                },
                "new": {
                    "attribute_value_id": 999999,
                    "sku": "QEDQE6119"
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_product_value"
        },
        "table": {
            "schema": "eav",
            "name": "product_value"
        }
    }
    """
    Then  the response status code should be 200

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 0 element
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
