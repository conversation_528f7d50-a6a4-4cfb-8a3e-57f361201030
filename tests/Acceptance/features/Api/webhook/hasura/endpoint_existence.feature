Feature: Endpoint for <PERSON><PERSON>'s webhook system exists
  As Hasura system
  There is an endpoint to call as a webhook

  Scenario: Without authentication header
    When  I add "Content-Type" header equal to "application/json"
    And   I send a "POST" request to "/api/v1/webhook/hasura"
    Then  the response status code should be 401
    And   the JSON node "message" should be equal to the string "[ERP-SERVER] X-Webhook-Auth-Secret header is not defined or has not the expected content"

  Scenario: Wrong authentication header value
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "not-the-expected-value"
    And   I send a "POST" request to "/api/v1/webhook/hasura"
    Then  the response status code should be 401
    And   the JSON node "message" should be equal to the string "[ERP-SERVER] X-Webhook-Auth-Secret header is not defined or has not the expected content"

  Scenario: Good authentication, missing/wrong payload content
    # missing id
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura"
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "id" of value "NULL" violated a constraint "This value should not be null."'

    # wrong id
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "1234"
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "id" of value "1234" violated a constraint "This is not a valid UUID."'

    # missing created_at
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462"
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "created_at" of value "NULL" violated a constraint "This value should not be null."'

    # wrong created_at
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "123412345"
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "created_at" of value "123412345" violated a constraint "This value is not a valid datetime."'

    # missing event
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "2020-10-26T18:43:09.679595"
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "event" of value "NULL" violated a constraint "This value should not be null."'

    # missing trigger
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "2020-10-26T18:43:09.679595",
      "event": {
        "session_variables": {
            "x-hasura-role": "user",
            "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
        },
        "op": "UPDATE",
        "data": {}
      }
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "trigger" of value "NULL" violated a constraint "This value should not be null."'

    # missing table
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "2020-10-26T18:43:09.679595",
      "event": {
        "session_variables": {
            "x-hasura-role": "user",
            "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
        },
        "op": "UPDATE",
        "data": {}
      },
      "trigger": {
        "name": "eav_test_attribute"
      }
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "table" of value "NULL" violated a constraint "This value should not be null."'

    # missing delivery_info
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "2020-10-26T18:43:09.679595",
      "event": {
        "session_variables": {
            "x-hasura-role": "user",
            "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
        },
        "op": "UPDATE",
        "data": {}
      },
      "trigger": {
        "name": "eav_test_attribute"
      },
      "table": {
        "schema": "eav",
        "name": "attribute"
      }
    }
    """
    Then  the response status code should be 400
    And   the JSON node "message" should be equal to the string 'Parameter "delivery_info" of value "NULL" violated a constraint "This value should not be null."'

  Scenario: Unknown event (no handler)
    When  I add "Content-Type" header equal to "application/json"
    And   I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
      "id": "e8e6add1-1055-4480-b411-71e6618bc462",
      "created_at": "2020-10-26T18:43:09.679595",
      "event": {
        "session_variables": {
            "x-hasura-role": "user",
            "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
        },
        "op": "UPDATE",
        "data": {
            "old": {
                "definition": {
                    "type": "text"
                },
                "name": "3D auieuaie",
                "meta": {
                    "suffix": null,
                    "prefix": null,
                    "unit": null,
                    "label": "Compatible 3D"
                },
                "attribute_id": 246,
                "i18n": null
            },
            "new": {
                "definition": {
                    "type": "text"
                },
                "name": "3D auie",
                "meta": {
                    "suffix": null,
                    "prefix": null,
                    "unit": null,
                    "label": "Compatible 3D"
                },
                "attribute_id": 246,
                "i18n": null
            }
        }
      },
      "trigger": {
        "name": "eav_test_attribute"
      },
      "table": {
        "schema": "eav",
        "name": "unknown_table"
      },
      "delivery_info": {
        "max_retries": 0,
        "current_retry": 0
      }
    }
    """
    Then  the response status code should be 501
    And   the JSON node "message" should be equal to the string '[ERP-SERVER][Hasura Webhook] No handler found for key : "eav.unknown_table.update"'
