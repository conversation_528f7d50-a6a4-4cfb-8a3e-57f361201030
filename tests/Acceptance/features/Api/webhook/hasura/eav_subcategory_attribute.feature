Feature: <PERSON><PERSON>'s webhook for eav_subcategory_attribute events
  As Hasura system
  The webhook endpoint support the eav_subcategory_attribute events

  @clear-database
  Scenario: Load base fixtures
    When I load mysql fixtures from file "sales_channel/sales_channels.sql"
    And I load mysql fixtures from file "eav/base.sql"
    And  I load sql fixtures from file "eav/base.sql"

  Scenario: Subcategory attribute's deletion event should trigger nothing if category's filters not used
    # not in database
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 928,
                    "subcategory_id": 666
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Subcategory 666 does not use the filters. Nothing to do."
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

    # not use filters
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 935,
                    "subcategory_id": 258
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Subcategory 258 does not use the filters. Nothing to do."
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

  Scenario: Subcategory attribute's deletion event should trigger nothing if attribute was inactive
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "INACTIVE",
                    "attribute_id": 928,
                    "subcategory_id": 56
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Attribute 928 was inactive. Deletion has no side effect."
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

  Scenario: Subcategory attribute's deletion event should in other cases
            trigger an update for subcategory and related articles
    And  I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "DELETE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 928,
                    "subcategory_id": 56
                },
                "new": null
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200

    And   last RPC call service should be "bo-cms"
    And   last RPC call method should be "eav:update_facets"
    # subcategories
    And   last RPC call args node "0" should have 1 element
    And   last RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last RPC call args node "1" should have 2 elements
    And   last RPC call args node "1->0->sku" should be equal to the string "QEDQE1455"
    And   last RPC call args node "1->0->eav_facets" should have 3 elements
    And   last RPC call args node "1->1->sku" should be equal to the string "QEDQE6119"
    And   last RPC call args node "1->1->eav_facets" should have 3 elements

    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Not an update, nothing to do."

  Scenario: Subcategory attribute's update event
    - should trigger nothing if category's filters not used
    - do nothing for product's eav attribute if display order has not changed
    # not in database
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "INACTIVE",
                    "attribute_id": 928,
                    "subcategory_id": 666
                },
                "new": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 928,
                    "subcategory_id": 666
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Subcategory 666 does not use the filters. Nothing to do."
    And   the JSON node "data->attributes->message" should be equal to the string "Display order has not changed, nothing to do."

    # not use filters
    When I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "INACTIVE",
                    "attribute_id": 935,
                    "subcategory_id": 258
                },
                "new": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 935,
                    "subcategory_id": 258
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"
    And   the JSON node "data->facets->message" should be equal to the string "Subcategory 258 does not use the filters. Nothing to do."
    And   the JSON node "data->attributes->message" should be equal to the string "Display order has not changed, nothing to do."

  Scenario: Subcategory attribute's update event
    - should in case of filter (de)activation trigger an update for subcategory and related articles
    - do nothing for product's eav attribute if display order has not changed
    #
    # activation case
    #
    And  I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "INACTIVE",
                    "attribute_id": 928,
                    "subcategory_id": 56
                },
                "new": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 928,
                    "subcategory_id": 56
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 2 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE1455"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "1->1->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->1->eav_facets" should have 3 elements

    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Display order has not changed, nothing to do."

    #
    # deactivation case
    #
    And  I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_OPENED",
                    "attribute_id": 928,
                    "subcategory_id": 56
                },
                "new": {
                    "display_order": 100,
                    "filter_status": "INACTIVE",
                    "attribute_id": 928,
                    "subcategory_id": 56
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 2 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->sku" should be equal to the string "QEDQE1455"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->0->eav_facets" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "1->1->sku" should be equal to the string "QEDQE6119"
    And   last "bo-cms->eav:update_facets" RPC call args node "1->1->eav_facets" should have 3 elements

    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Display order has not changed, nothing to do."

  Scenario: Subcategory attribute's update event
    - should in case of filter opening/closing trigger an update for subcategory only
    - do nothing for product's eav attribute if display order has not changed
    And  I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 928,
                    "subcategory_id": 56
                },
                "new": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_OPENED",
                    "attribute_id": 928,
                    "subcategory_id": 56
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 0 element

    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->message" should be equal to the string "Display order has not changed, nothing to do."

  Scenario: Subcategory attribute's update event should in case of display order change
    - trigger an update for subcategory only
    - update products attributes linked to the attribute when display order change
    And  I add "Content-Type" header equal to "application/json"
    And  I add "X-Webhook-Auth-Secret" header equal to "123soleil"
    And   I expect RPC call to "bo-cms" "eav:update_facets" to respond with:
    """
{
  "updated_subcategory_ids": [56],
  "updated_skus": ["TOTO", "TITI"],
  "ignored_skus": []
}
    """
    And  I expect RPC call to "bo-cms" "eav:update_articles_attributes" to respond with:
    """
{
  "updated_skus": ["QEDQE1455"],
  "ignored_skus": []
}
    """
    And  I send a "POST" request to "/api/v1/webhook/hasura" with body:
    """
    {
        "event": {
            "session_variables": {
                "x-hasura-role": "user",
                "x-hasura-user-id": "f8284275-b1c4-42c0-b37b-639335ef730d"
            },
            "op": "UPDATE",
            "data": {
                "old": {
                    "display_order": 100,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 701,
                    "subcategory_id": 56
                },
                "new": {
                    "display_order": 51,
                    "filter_status": "ACTIVE_CLOSED",
                    "attribute_id": 701,
                    "subcategory_id": 56
                }
            }
        },
        "created_at": "2020-10-28T13:13:57.765301",
        "id": "37790ee9-d076-48b6-93be-3ee5c4f842a2",
        "delivery_info": {
            "max_retries": 0,
            "current_retry": 0
        },
        "trigger": {
            "name": "eav_subcategory_attribute"
        },
        "table": {
            "schema": "eav",
            "name": "subcategory_attribute"
        }
    }
    """
    Then  the response status code should be 200
    And   the JSON node "status" should be equal to the string "success"

    # subcategories
    And   last "bo-cms->eav:update_facets" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->subcategory_id" should be equal to the number "56"
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions" should have 3 elements
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_162" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_887" should exists
    And   last "bo-cms->eav:update_facets" RPC call args node "0->0->eav_facet_definitions->attribute_id_922" should exists
    # articles
    And   last "bo-cms->eav:update_facets" RPC call args node "1" should have 0 element
    # products attributes
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0" should have 1 element
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->sku" should be equal to the string "QEDQE1455"
    And   last "bo-cms->eav:update_articles_attributes" RPC call args node "0->0->eav_attributes" should have 5 elements

    And   the JSON node "data->facets->updated_subcategory_ids" should exist
    And   the JSON node "data->facets->updated_skus" should exist
    And   the JSON node "data->facets->ignored_skus" should exist
    And   the JSON node "data->attributes->updated_skus" should exist
    And   the JSON node "data->attributes->ignored_skus" should exist
