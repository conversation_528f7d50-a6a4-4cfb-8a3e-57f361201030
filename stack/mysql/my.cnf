[mysqld]
skip-host-cache
skip-name-resolve
datadir=/var/lib/mysql
!includedir /etc/mysql/conf.d/

secure_file_priv=""

# ----------------------------------------------
# Enable the binlog for replication & CDC
# ----------------------------------------------

# Enable binary replication log and set the prefix, expiration, and log format.
# The prefix is arbitrary, expiration can be short for integration tests but would
# be longer on a production system. Row-level info is required for ingest to work.
# expire_logs_days = 1
# log-bin          = mysql-bin
# log-bin-index    = mysql-bin.index
# max_binlog_size  = 300M
# binlog_format    = row
# socket           = mysql.sock

# copied from Marc config in Ansible
symbolic-links=0
max_allowed_packet=32M

large-pages

sql_mode=TRADITIONAL

net_write_timeout=60
wait_timeout=28800
connect_timeout=10
interactive_timeout=28800

innodb_autoinc_lock_mode=2
innodb_rollback_on_timeout
innodb_lock_wait_timeout=100

tmp_table_size          = 32M
max_heap_table_size     = 32M
query_cache_size        = 16M
query_cache_type        = 1
thread_cache_size       = 50
open_files_limit        = 5000

skip-external-locking
innodb_file_per_table
