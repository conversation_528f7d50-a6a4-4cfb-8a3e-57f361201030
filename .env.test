# In all environments, the following files are loaded if they exist,
# the later taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices/configuration.html#infrastructure-related-configuration

KERNEL_CLASS='App\Kernel'
DEFAULT_LOCALE=fr

###> symfony/framework-bundle ###
APP_ENV=test
SERVER_ENV=test
APP_SECRET=123456
#TRUSTED_PROXIES=127.0.0.1,*********
#TRUSTED_HOSTS='^localhost|example\.com$'
###< symfony/framework-bundle ###

ACTIVE_FEATURES='["some_feature_for_test", "another_feature_for_test"]'

ASSETS_CDN_IMAGES=https://dfxqtqxztmxwe.cloudfront.net

###> pomm-project/pomm-bundle ###
# postgre
DATABASE_HOST=erp-postgresql
DATABASE_PORT=5432
DATABASE_NAME=svd_erp_test
DATABASE_NAME_TEMPLATE=svd_erp_test_template
DATABASE_SU_PASSWORD=postgres
DATABASE_URL=pgsql://erp_user:azerty@$DATABASE_HOST:$DATABASE_PORT/$DATABASE_NAME
DATABASE_ADMIN_URL=pgsql://postgres:postgres@$DATABASE_HOST:$DATABASE_PORT/$DATABASE_NAME

# data_warehouse
DATABASE_DATA_WAREHOUSE_URL=$DATABASE_URL
###< pomm-project/pomm-bundle ###

INTERNAL_API_SECRET=ChangeMe

###> son-video/synapps-client ###
BEANSTALK_HOST=beanstalkd
BEANSTALK_PORT=11300
SYNAPPS_SERVICE_URI='http://synapps.lxc/api'
SYNAPPS_TUBE=test
SYNAPPS_CLIENT_ID=ChangeMeToo
###> son-video/synapps-client ###

# Secret to generate JWT tokens
JWT_SECRET=ThisIsTheKeyToSignJWTToken

HAL_URI=http://127.0.0.1

BEHAT_BASE_URL=http://test.erp.lxc

# dbal
DATABASE_LEGACY_URL=mysql://root:root@erp-server-mysql-test:3306/backOffice?serverVersion=5.7.31
DATABASE_LEGACY_READONLY_URL=mysql://root:root@erp-server-mysql-test:3306/backOffice?serverVersion=5.7.31

# phcdb
PHCDB_HOST=erp-server-mysql-test
PHCDB_USER=root
PHCDB_PASSWORD=root
PHCDB_PORT=3306
PHCDB_NAME=backOffice

###> sentry/sentry-symfony ###
SENTRY_DSN=
###< sentry/sentry-symfony ###

CUPS_IP=erp-server-cups
CUPS_PORT=631

EASYLOUNGE_API_ENDPOINT=
EASYLOUNGE_API_KEY=

CARRIER_V2_BASE_URL=
CARRIER_AWS_ENDPOINT=

ENVOIDUNET_WEBSERVICE_ACCOUNT=
ENVOIDUNET_WEBSERVICE_KEY=
ENVOIDUNET_WEBSERVICE_URL=

CHRONOPOST_WEBSERVICE_URL=
CHRONOPOST_WEBSERVICE_ACCOUNT=
CHRONOPOST_WEBSERVICE_PASSWORD=

MONDIAL_RELAY_WEBSERVICE_URL=
MONDIAL_RELAY_WEBSERVICE_SHOP_IDENTIFIER=
MONDIAL_RELAY_WEBSERVICE_PRIVATE_KEY=

ERPV1_API_ENDPOINT=
ERPV1_PAYMENT_WEBSERVICE_URL=

# Please use an "env.test.local" (ignored by git) file to not potentially break things on CI
# If you need to use the two variables below
#DEBUG_PHCDB=false
#SKIP_DB_INIT=false

DEBUG_MODE=ON
# To debug fixtures if necessary uncomment the line below
#DEBUG_MODE=QUERY

# Secret value that Hasura must send in Headers to authenticate its calls (X-Webhook-Auth-Secret)
HASURA_WEBHOOK_SECRET=123soleil

ERP_GRAPHQL_URL=https://graphql.test
ERP_GRAPHQL_ADMIN_API_KEY=TestSecret
FO_ROOT_WEB_SSL=https://test.fo-cms.lxc
ERP_CLIENT_ROOT_URL=https://127.0.0.1:8080
ERP_CLIENT_LEGACY_URL=https://0127.0.0.1:8080/legacy

OGONE_STATUS_API_ENDPOINT=
OGONE_STATUS_API_USERID=
OGONE_STATUS_API_ACCOUNT1_PSPID=
OGONE_STATUS_API_ACCOUNT1_PSWD=
OGONE_STATUS_API_ACCOUNT2_PSPID=
OGONE_STATUS_API_ACCOUNT2_PSWD=

ELASTIC_SEARCH_URLS=[]

RELEASE_NOTES_ENDPOINT=https://api.releasenotes.io/api/v1/projects/4120
RELEASE_NOTES_TOKEN=

MONDIAL_RELAY_HOST=
MONDIAL_RELAY_USERNAME=
MONDIAL_RELAY_PASSWORD=

MONDIAL_RELAY_S3_BUCKET=
MONDIAL_RELAY_S3_PREFIX=

# https://app.mailjet.com/account/api_keys
MAILJET_TRANSACTIONAL_API_KEY='test'
MAILJET_TRANSACTIONAL_API_SECRET='test'

# Set to false to prevent MailJet to issue a real call to the API
MAILJET_USE_API=true

# Metabase stuff
METABASE_URL=https://metabase.lxc
METABASE_SECRET_KEY=7cef150497bddc95e3411033bececb9de1e7e2839700bc8e2e35776d958b3d24

# PDF Genrator
ERP_GOTENBERG_ENDPOINT=http://gotenberg:3000

# Group Digital credentials
GROUP_DIGITAL_API_LOGIN=
GROUP_DIGITAL_API_PASSWORD=
GROUP_DIGITAL_FTP_USERNAME=
GROUP_DIGITAL_FTP_PASSWORD=
GROUP_DIGITAL_FTP_USE_SSL=

# ESL configuration
ESL_BASE_URL=
ESL_LOGIN=
ESL_PASSWORD=

# Payment API V2
PAYMENT_V2_API_URL=
PAYMENT_V2_INTERNAL_API_SECRET=PAYMENT_V2_INTERNAL_API_SECRET_KEY
