includes:
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-symfony/rules.neon
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon

parameters:
    symfony:
        containerXmlPath: var/cache/dev/AppApp_KernelDevDebugContainer.xml
    level: 0
    paths:
        - src
        - tests
    tmpDir: tools/phpstan
    ignoreErrors:
        - message: '#^Service "[^"]+" is private.$#'

services:
    # SerializerInterface::deserialize() return type
    -
        factory: PHPStan\Type\Symfony\SerializerDynamicReturnTypeExtension(App\Adapter\Serializer\SerializerInterface, deserialize)
        tags: [phpstan.broker.dynamicMethodReturnTypeExtension]

    # SerializerInterface::denormalize() return type
    -
        factory: PHPStan\Type\Symfony\SerializerDynamicReturnTypeExtension(App\Adapter\Serializer\SerializerInterface, denormalize)
        tags: [phpstan.broker.dynamicMethodReturnTypeExtension]
