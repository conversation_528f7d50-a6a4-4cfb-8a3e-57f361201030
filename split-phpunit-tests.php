<?php

/**
 * Script pour diviser les tests PHPUnit en plusieurs fichiers de configuration.
 *
 * Ce script:
 * 1. Exécute la commande PHPUnit pour générer un fichier XML contenant la liste des tests
 * 2. Extrait les noms de classes de test du fichier XML
 * 3. Formate les chemins selon les besoins
 * 4. Divise la liste en 3 parties égales
 * 5. Génère 3 fichiers de configuration PHPUnit
 * 6. Supprime le fichier XML temporaire
 */

// TODO implement the gitlab CI steps to generate the file and the steps to run them in parallel
// Example command to run them: vendor/bin/phpunit --configuration phpunit-unit-3.xml --testdox
// No coverage since the xml would be split

/**
 * Divise les tests d'une suite spécifique en plusieurs fichiers de configuration PHPUnit.
 *
 * @param string $test_suite Nom de la suite de tests (e2e, unit, etc.)
 * @param int    $num_chunks Nombre de fichiers à générer (défaut: 3)
 *
 * @return void
 */
function split_phpunit_tests($test_suite, $num_chunks = 3)
{
    // Étape 1: Exécuter la commande PHPUnit pour générer le fichier XML
    $xml_file = sprintf('to-split-%s.xml', $test_suite);

    // Supprimer le fichier s'il existe déjà
    if (file_exists($xml_file)) {
        unlink($xml_file);
        echo sprintf('Ancien fichier %s supprimé.%s', $xml_file, PHP_EOL);
    }

    // Exécuter la commande PHPUnit pour générer le fichier XML
    echo sprintf('Exécution de PHPUnit pour générer la liste des tests %s...%s', $test_suite, PHP_EOL);
    $command = sprintf('vendor/bin/phpunit --list-tests-xml %s --testsuite %s', $xml_file, $test_suite);
    exec($command, $output, $return_code);

    if (0 !== $return_code) {
        echo sprintf("Erreur lors de l'exécution de la commande PHPUnit. Code de retour: %d%s", $return_code, PHP_EOL);
        echo sprintf(
            "Vérifiez que PHPUnit est correctement installé et que la suite de tests '%s' existe.%s",
            $test_suite,
            PHP_EOL
        );

        return;
    }

    echo sprintf('Commande PHPUnit exécutée avec succès.%s', PHP_EOL);

    // Étape 2: Charger le fichier XML
    $xml = simplexml_load_string(file_get_contents($xml_file));
    if (false === $xml) {
        echo sprintf('Erreur lors de la lecture du fichier XML.%s', PHP_EOL);

        return;
    }

    // Étape 3: Extraire les noms de classes de test
    $test_classes = [];
    foreach ($xml->testCaseClass as $test_case) {
        $class_name = (string) $test_case['name'];
        $test_classes[] = $class_name;
    }

    // Si aucun test n'est trouvé, sortir de la fonction
    if (empty($test_classes)) {
        echo sprintf("Aucun test trouvé dans la suite '%s'.%s", $test_suite, PHP_EOL);
        // Supprimer le fichier XML temporaire
        if (file_exists($xml_file)) {
            unlink($xml_file);
        }

        return;
    }

    // Étape 4: Formater les chemins
    $formatted_paths = [];
    foreach ($test_classes as $class_name) {
        // Remplacer les \ par /
        $path = str_replace('\\', '/', $class_name);
        // Ajouter le préfixe tests/ et le suffixe .php
        $formatted_path = sprintf('tests/%s.php', $path);
        $formatted_paths[] = $formatted_path;
    }

    // Étape 5: Diviser la liste en parties égales
    $chunks = array_chunk($formatted_paths, ceil(count($formatted_paths) / $num_chunks));

    // Étape 6: Modèle de fichier XML
    $xml_template = <<<XML
    <?xml version="1.0" encoding="UTF-8"?>
    <phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.6/phpunit.xsd"
             bootstrap="tests/bootstrap.php"
             colors="true"
             executionOrder="depends,defects"
             failOnWarning="true">
        <php>
            <ini name="display_errors" value="1"/>
            <ini name="error_reporting" value="-1"/>
            <server name="APP_ENV" value="test" force="true"/>
            <server name="SHELL_VERBOSITY" value="-1"/>
            <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
            <server name="SYMFONY_PHPUNIT_VERSION" value="9.5"/>
            <server name="SYMFONY_DEPRECATIONS_HELPER" value="disabled"/>
        </php>

        <testsuites>
            <testsuite name="collected">
    {files}
            </testsuite>
        </testsuites>
    </phpunit>
    XML;

    // Étape 7: Générer les fichiers XML
    for ($i = 0, $i_max = count($chunks); $i < $i_max; ++$i) {
        // Créer la liste des fichiers avec implode pour éviter un retour à la ligne vide à la fin
        $file_entries = [];
        foreach ($chunks[$i] as $path) {
            $file_entries[] = sprintf('            <file>%s</file>', $path);
        }

        // Utiliser implode pour joindre les entrées avec un retour à la ligne
        $files_list = implode(PHP_EOL, $file_entries);

        // Utiliser strtr pour remplacer {files}
        $xml_content = strtr($xml_template, ['{files}' => $files_list]);

        // Générer le nom du fichier de sortie avec le nom de la suite de tests
        $output_file = sprintf('phpunit-%s-%d.xml', $test_suite, $i + 1);
        file_put_contents($output_file, $xml_content);
        echo sprintf('Fichier %s créé avec %d tests.%s', $output_file, count($chunks[$i]), PHP_EOL);
    }

    echo sprintf(
        'Traitement terminé pour la suite %s. %d tests répartis en %d fichiers.%s',
        $test_suite,
        count($test_classes),
        count($chunks),
        PHP_EOL
    );

    // Étape 8: Supprimer le fichier XML temporaire
    if (file_exists($xml_file)) {
        unlink($xml_file);
        echo sprintf('Fichier temporaire %s supprimé.%s', $xml_file, PHP_EOL);
    }
}

// Exécuter la fonction pour la suite de tests e2e
echo sprintf('=== Traitement de la suite de tests e2e ===%s', PHP_EOL);
split_phpunit_tests('e2e');

// Exécuter la fonction pour la suite de tests unit
echo sprintf('%s=== Traitement de la suite de tests unit ===%s', PHP_EOL, PHP_EOL);
split_phpunit_tests('unit');
