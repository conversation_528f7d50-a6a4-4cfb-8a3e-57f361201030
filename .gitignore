.idea

###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

*.pgr
*.pgdump
stack/dbdump/*

atoum-report.xml
pretty.out
xml
/tools

###> friendsofphp/php-cs-fixer ###
/.php_cs
/.php_cs.cache
###< friendsofphp/php-cs-fixer ###


node_modules
/stack/backup/
/stack/data/
.php-cs-fixer.cache
.php-cs-fixer
.vscode/
.composer

###> symfony/phpunit-bridge ###
.phpunit
.phpunit.result.cache
/phpunit.xml
.phpunit.cache
/coverage/
phpunit-e2e-*.xml
phpunit-unit-*.xml
###< symfony/phpunit-bridge ###
