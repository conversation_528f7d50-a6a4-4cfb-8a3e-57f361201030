{"adbario/php-dot-notation": {"version": "2.2.0"}, "atoum/atoum": {"version": "3.3.0"}, "aws/aws-crt-php": {"version": "v1.0.2"}, "aws/aws-sdk-php": {"version": "3.112.17"}, "bacon/bacon-qr-code": {"version": "2.0.0"}, "behat/behat": {"version": "v3.5.0"}, "behat/gherkin": {"version": "v4.6.0"}, "behat/mink": {"version": "1.7.x-dev"}, "behat/mink-browserkit-driver": {"version": "1.3.3"}, "behat/mink-extension": {"version": "2.3.1"}, "behat/mink-goutte-driver": {"version": "v1.2.1"}, "behat/symfony2-extension": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.1", "ref": "158be9d4b4c2127d9ec741c97e3cb1b6610b5294"}, "files": ["behat.yml.dist", "config/bootstrap.php", "features/bootstrap/FeatureContext.php", "features/bootstrap/bootstrap.php", "features/demo.feature"]}, "behat/transliterator": {"version": "v1.2.0"}, "behatch/contexts": {"version": "3.2.0"}, "clue/stream-filter": {"version": "v1.4.1"}, "dasprid/enum": {"version": "1.0.0"}, "datto/json-rpc": {"version": "4.0.4"}, "datto/json-rpc-http": {"version": "3.2.1"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "cb4152ebcadbe620ea2261da1a1c5a9b8cea7672"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.3", "ref": "13f12b59a228240cb9627eea59890edf05f2040c"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "config/packages/test/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/inflector": {"version": "v1.3.0"}, "doctrine/lexer": {"version": "1.0.2"}, "egulias/email-validator": {"version": "2.1.9"}, "elasticsearch/elasticsearch": {"version": "v7.13.1"}, "endroid/qr-code": {"version": "3.5.9"}, "ezimuel/guzzlestreams": {"version": "3.0.1"}, "ezimuel/ringphp": {"version": "1.1.2"}, "fabpot/goutte": {"version": "v3.2.3"}, "firebase/php-jwt": {"version": "v6.2.0"}, "friends-of-behat/mink-extension": {"version": "2.3.1"}, "friendsofphp/php-cs-fixer": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofsymfony/rest-bundle": {"version": "2.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.2", "ref": "258300d52be6ad59b32a888d5ddafbf9638540ff"}, "files": ["config/packages/fos_rest.yaml"]}, "guzzlehttp/guzzle": {"version": "6.3.3"}, "guzzlehttp/promises": {"version": "v1.3.1"}, "guzzlehttp/psr7": {"version": "1.6.1"}, "http-interop/http-factory-guzzle": {"version": "1.0.0"}, "jdorn/sql-formatter": {"version": "v1.2.17"}, "jean85/pretty-package-versions": {"version": "1.2"}, "justinrainbow/json-schema": {"version": "5.2.8"}, "khanamiryan/qrcode-detector-decoder": {"version": "1.0.2"}, "league/commonmark": {"version": "1.0.0"}, "league/csv": {"version": "9.2.1"}, "league/flysystem": {"version": "1.0.53"}, "league/flysystem-aws-s3-v3": {"version": "1.0.23"}, "league/mime-type-detection": {"version": "1.5.1"}, "leezy/pheanstalk-bundle": {"version": "3.4.2"}, "mailjet/mailjet-apiv3-php": {"version": "v1.5.1"}, "monolog/monolog": {"version": "1.24.0"}, "mtdowling/jmespath.php": {"version": "2.4.0"}, "myclabs/php-enum": {"version": "1.7.4"}, "nelmio/api-doc-bundle": {"version": "4.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "oneup/flysystem-bundle": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "0eb87bba411c227da027fe5f7c1dc7954b02f242"}, "files": ["config/packages/oneup_flysystem.yaml"]}, "pda/pheanstalk": {"version": "v3.2.1"}, "php-http/client-common": {"version": "2.0.0"}, "php-http/discovery": {"version": "1.7.0"}, "php-http/guzzle6-adapter": {"version": "v2.0.2"}, "php-http/httplug": {"version": "v2.0.0"}, "php-http/message": {"version": "1.7.2"}, "php-http/message-factory": {"version": "v1.0.2"}, "php-http/promise": {"version": "v1.0.0"}, "phpdocumentor/reflection-common": {"version": "1.0.1"}, "phpdocumentor/reflection-docblock": {"version": "4.3.1"}, "phpdocumentor/type-resolver": {"version": "0.4.0"}, "phpstan/phpstan": {"version": "1.5.3"}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "6a9341aa97d441627f8bd424ae85dc04c944f8b4"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "picqer/php-barcode-generator": {"version": "v0.3"}, "pomm-project/cli": {"version": "2.0.2"}, "pomm-project/foundation": {"version": "2.0.3"}, "pomm-project/model-manager": {"version": "2.0.2"}, "pomm-project/pomm-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "2.3", "ref": "7f10aab539e8268582d10713e4712e59726a7b48"}, "files": ["config/packages/pomm.yaml", "config/routes/dev/pomm.yaml"]}, "pomm-project/pomm-symfony-bridge": {"version": "2.5.1"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.0"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.0"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "react/promise": {"version": "v2.8.0"}, "sentry/sdk": {"version": "2.0.3"}, "sentry/sentry": {"version": "2.1.1"}, "sentry/sentry-symfony": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "9746f0823302d7980e5273ef7a69ef3f5ac80914"}, "files": ["config/packages/sentry.yaml"]}, "son-video/hal-middleware-bundle": {"version": "v2.0.1"}, "son-video/phcdb": {"version": "dev-master"}, "son-video/svd-rpc-bundle": {"version": "v1.1.0"}, "son-video/synapps-client": {"version": "dev-dev"}, "statamic/stringy": {"version": "3.1.1"}, "swiftmailer/swiftmailer": {"version": "v6.2.1"}, "symfony/asset": {"version": "v4.3.2"}, "symfony/browser-kit": {"version": "v4.3.4"}, "symfony/cache": {"version": "v4.3.2"}, "symfony/cache-contracts": {"version": "v1.1.5"}, "symfony/config": {"version": "v4.3.2"}, "symfony/console": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "482d233eb8de91ebd042992077bbd5838858890c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/css-selector": {"version": "v4.3.4"}, "symfony/debug": {"version": "v4.3.2"}, "symfony/debug-bundle": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "f8863cbad2f2e58c4b65fa1eac892ab189971bea"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v4.3.2"}, "symfony/deprecation-contracts": {"version": "v2.5.0"}, "symfony/dom-crawler": {"version": "v4.3.4"}, "symfony/dotenv": {"version": "v4.3.2"}, "symfony/error-handler": {"version": "v4.4.36"}, "symfony/event-dispatcher": {"version": "v4.3.2"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.5"}, "symfony/expression-language": {"version": "v4.3.2"}, "symfony/filesystem": {"version": "v4.3.2"}, "symfony/finder": {"version": "v4.3.2"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "dc3fc2e0334a4137c47cfd5a3ececc601fa61a0b"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "61ad963f28c091b8bb9449507654b9c7d8bbb53c"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.3.11"}, "symfony/http-client-contracts": {"version": "v1.1.10"}, "symfony/http-foundation": {"version": "v4.3.2"}, "symfony/http-kernel": {"version": "v4.3.2"}, "symfony/inflector": {"version": "v4.3.2"}, "symfony/lock": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "d9d21f740e47e96d5a26027ceb11ec7195af36be"}, "files": ["config/packages/lock.yaml"]}, "symfony/messenger": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "25e3c964d3aee480b3acc3114ffb7940c89edfed"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v4.3.2"}, "symfony/monolog-bridge": {"version": "v4.3.2"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6240c6d43e8237a32452f057f81816820fd56ab6"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v4.3.2"}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.1", "ref": "2f91477d6efaed3fb857db87480f7d07d31cbb3e"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-idn": {"version": "v1.11.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.22.0"}, "symfony/polyfill-mbstring": {"version": "v1.11.0"}, "symfony/polyfill-php72": {"version": "v1.11.0"}, "symfony/polyfill-php73": {"version": "v1.11.0"}, "symfony/polyfill-php80": {"version": "v1.22.0"}, "symfony/polyfill-php81": {"version": "v1.24.0"}, "symfony/property-access": {"version": "v4.3.2"}, "symfony/property-info": {"version": "v4.3.2"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "4c107a8d23a16b997178fbd4103b8d2f54f688b7"}, "files": ["config/packages/dev/routing.yaml", "config/packages/routing.yaml", "config/packages/test/routing.yaml", "config/routes.yaml"]}, "symfony/security": {"version": "v4.3.2"}, "symfony/security-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "9a2034eca6d83d9cda632014e06995b8d9d9fd09"}, "files": ["config/packages/security.yaml"]}, "symfony/serializer": {"version": "v4.3.2"}, "symfony/service-contracts": {"version": "v1.1.5"}, "symfony/stopwatch": {"version": "v4.3.2"}, "symfony/templating": {"version": "v4.3.2"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v1.1.5"}, "symfony/twig-bridge": {"version": "v4.3.2"}, "symfony/twig-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "369b5b29dc52b2c190002825ae7ec24ab6f962dd"}, "files": ["config/packages/twig.yaml", "config/routes/dev/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.3.2"}, "symfony/var-exporter": {"version": "v4.3.2"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/yaml": {"version": "v4.3.2"}, "thecodingmachine/gotenberg-php-client": {"version": "6.3.0"}, "twig/twig": {"version": "v2.11.3"}, "webmozart/assert": {"version": "1.4.0"}, "willdurand/jsonp-callback-validator": {"version": "v1.1.0"}, "willdurand/negotiation": {"version": "v2.3.1"}, "zircote/swagger-php": {"version": "4.6.2"}}