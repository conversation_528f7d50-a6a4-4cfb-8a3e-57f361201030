# ERP Server

Couche Backend API de l'ERP de Son-Vidéo.com

---

## <PERSON><PERSON><PERSON><PERSON> avec `phcstack`

Lister les commandes possibles pour ce projet :

```shell
phcstack erp-server
```

---

## Bon à savoir

1. [ Colorations syntaxiques personnalisées dans PHPStorm](./docs/code_highlighting_in_phpstorm.md)
2. [Serializer](./docs/serializer.md)
3. [Test (Unitaires et E2E)](./docs/tests.md)
4. [Synchronisation de données vers le Data-Warehouse](./docs/datawarehouse.md)

## Tailwind

Tailwind est utilisé dans le projet notamment pour la génération de PDF.   
Le script écoute toutes les modifs des fichiers twig ajoute les classes dans le fichier CSS de sorti.

Pour lancer la compilation CSS :

```bash
cd /path/to/erp-server
npx tailwindcss -i ./public/input.css -o ./public/output.css --watch
```

## Commande PHP via la crontab

Cette application fait tourner nos commandes symfony via la crontab sur nos serveurs AWS.   
Les environnements `prod` et `staging` sont en `autoscaling`, de ce fait, les crons ne doivent tourner que sur la
machine taguée en tant que `leader`.

Pour assurer ce fonctionnement, nous utilisons un script bash qui va vérifier que la machine est leader avant de jouer
la commande Symfony.

La syntaxe est la suivante:

```bash 
### Si nous devons jouer la commande symfony suivante
bin/console my_domain:my_action toto --foo=bar --env=prod

### Nous pouvons utiliser le script de proxy de la maniere suivante (pour tester)
bin/cron.sh my_domain:my_action toto --foo=bar --env=prod

### Une fois le bon fonctionnement validé, on peut alors ajouter la commande dans la crontab de la façon suivante
 * * * * * nginx /var/www/erp-server/release/bin/cron.sh my_domain:my_action toto --foo=bar --env=prod
```

> À noter : il n'est pas possible de lancer les commandes via le script bash à partir de la machine sur aws.
> Il semblerait que l'environnement PHP ne se charge pas correctement et cela bloque la connection MySQL via PDO

## Nomenclature du projet

### Conventions de code

On a mis en place un prettier pour PHP.

- Les règles sont spécifiées dans le fichier suivant: `package.json`
- La liste des règles et le bac à sable sont disponibles ici : https://github.com/prettier/plugin-php#configuration
- Pour l'installation du plugin dans PHPStorm : https://github.com/prettier/plugin-php#phpstorm--intellij--jetbrains-ide

### Organisation des endpoints

- Les API non RPC vont dans `src/App/Controller/Api`
- Les API RPC vont dans `src/App/Controller/Rpc`

### Organisation des fichiers pour chaque "Contexte"

> Le contexte (ou domaine) représente le contexte métier rattaché à la tâches en cours de réalisation
> Ex : CustomerOrder, Quote, Mailing...

La liste ci-dessous introduit `le chemin des dossier` - Suivi des noms de fichiers

- `src/SonVideo/Erp/{Domain}`
- `src/SonVideo/Erp/{Domain}/Client` - {Domain}Client
- `src/SonVideo/Erp/{Domain}/Collection` - {Domain}AbstractHandlerCollection
- `src/SonVideo/Erp/{Domain}/Contract` - {Domain}Interface, {Domain}Trait, Abstract{Domain}Xxxx
- `src/SonVideo/Erp/{Domain}/Dto` - {Domain}Dto = (DataTransferObject) Transformation et validation des données pour
  échange entre classes (une Dto en entrée et un autre potentiellement en sortie)
- `src/SonVideo/Erp/{Domain}/Entity` - {Domain}Entity = Transformation des données issue de la BDD sans Validation
- `src/SonVideo/Erp/{Domain}/Exception` - {Domain}Exception
- `src/SonVideo/Erp/{Domain}/Manager` - {Domain}Manager
- `src/SonVideo/Erp/{Domain}/Mysql`
- `src/SonVideo/Erp/{Domain}/Mysql/Repository` - {Domain}Repository

### Dossier spécial (On continue à créer des fichiers si besoin sans suivre la nomenclature précédente)

- `src/SonVideo/Erp/Referential`

### Liste des dossiers à ne plus utiliser (Plus de création de nouveau fichier)

- `src/SonVideo/Eav`
- `src/SonVideo/Erp/Client`
- `src/SonVideo/Erp/Entity`
- `src/SonVideo/Erp/Exporter`
- `src/SonVideo/Erp/Extractor`
- `src/SonVideo/Erp/Filesystem`
- `src/SonVideo/Erp/Manager`
- `src/SonVideo/Erp/Repository`

### Liste des dossiers à refactorer avant usage (Plus de création de nouveau fichier si pas de travail préalable pour utiliser la nomenclature)

- `src/SonVideo/Erp/BusinessRule`
- `src/SonVideo/Erp/Process`
- `src/SonVideo/Erp/Webhook`

## Déploiement depuis le poste de dev

### Création du package

Le package sera généré à partir des sources en **local**

```bash
phcstack erp-server exec
cd deployment
make package
chmod a+rw erp-server.tar
exit
```

### Livraison manuelle

#### Validation

```bash
cd ~/projets/erp-server/deployment
make deploy-validation
sudo make clean
```

#### Staging

```bash
cd ~/projets/erp-server/deployment
make deploy
sudo make clean
```

#### Production

Pour déployer en production il faut préalablement déployer sur staging puis lancer :

```bash 
phcstack erp-server deploy-prod
```
