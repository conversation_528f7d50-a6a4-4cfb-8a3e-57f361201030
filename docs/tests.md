## PHPUnit

> Les tests atoum doivent être portés progressivement vers PHPUnit

### Jouer tous les tests

```bash
bin/phpunit --testdox

# Jouer tous les tests d'une suite spécifique
bin/phpunit --testdox --testsuite e2e
bin/phpunit --testdox --testsuite unit
```

#### Jouer un seul test

```bash
bin/phpunit --testdox --testsuite unit --filter PricingStrategyEngine
```

> L'option filter est le nom de la classe de test, partiel ou complet
> N'hésitez pas à explorer les options de la commande `bin/phpunit --help`

## Behat tests

> Les tests behat doivent être portés progressivement vers PHPUnit

### Jouer tous les tests

```bash
### Le -vvv est important pour avoir des logs détaillés 
vendor/bin/behat tests/Acceptance/features -vvv
```

#### Jouer un seul test

```bash
### Le -vvv est important pour avoir des logs détaillés 
vendor/bin/behat tests/Acceptance/features/Api/competitor_pricing/get_cheapest_competitor_prices.feature -vvv
```

> Pour avoir le bon path vis PHPStorm, faire un clic droit sur le fichier que vous souhaitez jouer
> et sélectionnez "Copy/Path Reference" puis "Path from content root"

#### Jouer tous les tests d'un dossier

```bash
### Le -vvv est important pour avoir des logs détaillés 
vendor/bin/behat tests/Acceptance/features/Api/competitor_pricing -vvv
```

> Pour avoir le bon path vis PHPStorm, faire un clic droit sur le dossier que vous souhaitez jouer
> et sélectionnez "Copy/Path Reference" puis "Path from content root"

## Atoum tests

> Les tests atoum doivent être portés progressivement vers PHPUnit

### Jouer tous les tests

```bash
vendor/bin/atoum -c .atoum.php --no-code-coverage -d tests/Unit --debug
```

#### Jouer un seul test

```bash
vendor/bin/atoum -c .atoum.php --no-code-coverage -f tests/Unit/SonVideo/Erp/PricingStrategy/Manager/PricingStrategyManager.php --debug
```

> Pour avoir le bon path vis PHPStorm, faire un clic droit sur le fichier que vous souhaitez jouer
> et sélectionnez "Copy/Path Reference" puis "Path from content root"
