<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/11.2/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         colors="true"
         executionOrder="depends,defects"
         failOnWarning="true">
    <php>
        <ini name="display_errors" value="1"/>
        <ini name="error_reporting" value="-1"/>
        <server name="APP_ENV" value="test" force="true"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <server name="SYMFONY_PHPUNIT_REMOVE" value=""/>
        <server name="SYMFONY_PHPUNIT_VERSION" value="9.5"/>
        <server name="SYMFONY_DEPRECATIONS_HELPER" value="disabled"/>
    </php>

    <testsuites>
        <testsuite name="e2e">
            <directory>tests/PHPUnit/EndToEnd</directory>
        </testsuite>
        <testsuite name="unit">
            <directory>tests/PHPUnit/Unit</directory>
        </testsuite>
    </testsuites>
</phpunit>
