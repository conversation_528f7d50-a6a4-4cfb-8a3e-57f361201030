default:
    suites:
        default:
            contexts:
                - App\Tests\Acceptance\Context\FeatureContext:
                    kernel: '@kernel'
                - App\Tests\Acceptance\Context\JsonContext
                - App\Tests\Acceptance\Context\MailjetApiTransactionalContext
                - App\Tests\Acceptance\Context\PermissionContext:
                    pomm: '@pomm'
                - App\Tests\Acceptance\Context\RpcContext
                - App\Tests\Acceptance\Context\SynappsContext:
                    kernel: '@kernel'
                - behatch:context:rest
                - behatch:context:browser
            paths:
                - "%paths.base%/tests/Acceptance/features"
    extensions:
        Behat\Symfony2Extension:
            kernel:
                bootstrap: 'tests/Acceptance/bootstrap.php'
                class: 'App\Kernel'
        Behat\MinkExtension:
            browser_name: 'firefox'
            symfony2: ~
            files_path: "%paths.base%/tests/Acceptance/features"
        Behatch\Extension: ~
