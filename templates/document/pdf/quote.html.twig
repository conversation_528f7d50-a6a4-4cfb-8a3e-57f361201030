<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Devis</title>
    <link href="style.css" rel="stylesheet" type="text/css">

    <style>
        @page {
            size: A4;
            margin: 0.75cm 0.75cm 1cm;
        }
    </style>
</head>
<body class="text-[11px]">
<!-- Header -->
<header class="flex items-center gap-3 mb-4" id="pageHeader">
    <div class="w-2/6">
        <img src="https://dfxqtqxztmxwe.cloudfront.net/images/ui/uiV3/logo/interne/header-desktop-logo-svd.svg" alt="">
    </div>


    <div class="ml-auto inline-flex flex-col text-right text-[9px] text-gray-800">
        <span>309 Av. du Général de Gaulle- 94500 Champigny-sur-Marne - FRANCE</span>
        <span>Tél: +33(0)826 960 290 - Email: <EMAIL></span>
        <span>Siren: 432 317 980 - APE 4743Z - TVA: FR29432317980</span>
    </div>

    <div class="border border-gray-300 ml-3 px-3 py-1 uppercase text-[23px] font-semibold h-full">
        {% if type == 'quotation' %}Devis{% else %}Offre{% endif %}
    </div>
</header>

<!-- Infos -->
<div class="border border-gray-300 mb-3 grid grid-cols-5">
    <div>
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">
            Numéro de {% if type == 'quotation' %}devis{% else %}l'offre{% endif %}
        </div>
        <div class="px-2 py-1 font-semibold">{{ quote_id }}</div>
    </div>
    <div>
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Date de création</div>
        <div class="px-2 py-1">{{ created_at | formatDate }}</div>
    </div>
    <div>
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Date d'expiration</div>
        <div class="px-2 py-1">{% if expired_at is null %}-{% else %}{{ expired_at | date('d/m/Y') }}{% endif %}</div>
    </div>
    <div>
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Compte client</div>
        <div class="px-2 py-1">{{ customer_id }}</div>
    </div>
    <div>
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Conseiller</div>
        <div class="px-2 py-1">{{ created_by_name }}</div>
    </div>
</div>

<!-- Addresses & additional customer info -->
<div class="grid grid-cols-3 gap-3 mb-3">
    <div class="border border-gray-300">
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Adresse de livraison</div>
        <div class="px-2 py-2 flex flex-col">
            {% if shipping_address is not null %}
                {% if shipping_address.shipment_method_id is defined %}
                    <span>{{ shipping_address.company_name }} {{ shipping_address.city }}</span>
                {% else %}
                    <span>{{ shipping_address.company_name }}</span>
                {% endif %}
                <span>{{ shipping_address.firstname }} {{ shipping_address.lastname }}</span>
                <span>{{ shipping_address.address }}</span>
                <span>{{ shipping_address.postal_code }} {{ shipping_address.city }} - {{ shipping_address.country.name }}</span>
                <span>Tél: {{ shipping_address.phone }}</span>
                <span>Tél: {{ shipping_address.cellphone }}</span>
            {% endif %}
        </div>
    </div>

    <div class="border border-gray-300">
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Adresse de facturation</div>
        <div class="px-2 py-2 flex flex-col">
            {% if billing_address is not null %}
                <span>{{ billing_address.company_name }}</span>
                <span>{{ billing_address.firstname }} {{ billing_address.lastname }}</span>
                <span>{{ billing_address.address }}</span>
                <span>{{ billing_address.postal_code }} {{ billing_address.city }} - {{ billing_address.country.name }}</span>
                <span>Tél: {{ billing_address.phone }}</span>
                <span>Tél: {{ billing_address.cellphone }}</span>
            {% endif %}
        </div>
    </div>

    <div class="space-y-2">
        <div class="border border-gray-300">
            <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Échéance de paiement</div>
            <div class="px-2 py-2 flex flex-col">À la commande</div>
        </div>

        {% if intra_community_vat is not null %}
            <div class="border border-gray-300">
                <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">N° de TVA intracommunautaire</div>
                <div class="px-2 py-2 flex flex-col">{{ intra_community_vat | formatIntraCommunityVat }}</div>
            </div>
        {% endif %}

        <div class="border border-gray-300">
            <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Mode de règlement</div>
            <div class="px-2 py-2 flex flex-col">
                <div class="mb-1">CB ou Virement</div>
                <div class="text-[8px] text-gray-500">
                    En cas de paiement par virement, effectuez celui-ci sur le compte SON VIDEO DISTRIBUTION :
                    CIC PARIS ST AUGUSTIN 30066 10646 00010958304 74
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Products -->
<table class="min-w-full divide-y divide-gray-500 mb-3">
    <thead class="bg-gray-100">
    <tr>
        <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-left w-px whitespace-nowrap">Référence</th>
        <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-left">Désignation</th>
        <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-right w-px whitespace-nowrap">Quantité</th>
        <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-right w-px whitespace-nowrap">Prix Unitaire (HT)</th>
        {% if prices.total_discount_tax_excluded > 0 %}
            <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-right w-px whitespace-nowrap">Remise (HT)</th>
        {% endif %}
        <th class="px-3 py-2 tracking-wide uppercase text-[8px] text-gray-600 text-right w-px whitespace-nowrap">Total (HT)</th>
    </tr>
    </thead>
    <tbody>
    {% for quote_line in quote_line_aggregates %}
        {% set data = quote_line.data %}
        {% if quote_line.type == 'product' %}
            <!-- Product -->
            <tr class="border-b border-gray-300 align-top">
                <td class="px-3 py-2">
                    <div class="font-semibold text-[10px]">{{ data.product.sku }}</div>
                </td>
                <td class="px-3 py-2">
                    <div class="text-gray-600 leading-3 text-[10px]">
                        {{ data.product.short_description }}
                    </div>
                </td>
                <td class="px-3 py-2 text-right">{{ data.quantity }}</td>
                <td class="px-3 py-2 text-right whitespace-nowrap">{{ data.selling_price_tax_excluded | formatPrice }}</td>
                {% if prices.total_discount_tax_excluded > 0 %}
                    <td class="px-3 py-2 text-right whitespace-nowrap">{{ (data.unit_discount_amount_abs_tax_excluded * data.quantity) | formatPrice }}</td>
                {% endif %}
                <td class="px-3 py-2 text-right whitespace-nowrap">{{ data.total_price_tax_excluded | formatPrice }}</td>
            </tr>
        {% endif %}
        {% if quote_line.type == 'product_warranty' %}
            <!-- Product -->
            <tr class="border-b border-gray-300 align-top">
                <td class="px-3 py-2">-</td>
                <td class="px-3 py-2">
                    <div class="text-gray-600 leading-3 text-[10px]">
                        {{ data.label }}
                    </div>
                </td>
                <td class="px-3 py-2 text-right">{{ data.quantity }}</td>
                <td class="px-3 py-2 text-right whitespace-nowrap">{{ data.price_tax_excluded | formatPrice }}</td>
                {% if prices.total_discount_tax_excluded > 0 %}
                    <td class="px-3 py-2 text-right whitespace-nowrap">-</td>
                {% endif %}
                <td class="px-3 py-2 text-right whitespace-nowrap">{{ data.total_price_tax_excluded | formatPrice }}</td>
            </tr>
        {% endif %}
        {% if quote_line.type == 'section' %}
            {% set colspan = prices.total_discount_tax_excluded > 0 ? 6 : 5 %}
            <!-- Section -->
            <tr class="border-b border-gray-300 bg-gray-50">
                <td class="px-3 py-2 text-left font-semibold tracking-wide uppercase text-[8px] text-gray-600" colspan="{{ colspan }}">{{ data.label }}</td>
            </tr>
        {% endif %}
    {% endfor %}
    </tbody>
</table>

<div class="flex space-x-3 page-break">
    <!-- Comment -->
    <div class="border border-gray-300 grow">
        <div class="bg-gray-100 px-2 py-2 font-semibold tracking-wide uppercase text-[8px] text-gray-600">Commentaire</div>
        <div class="px-2 py-2 flex flex-col text-[10px]">
            {{ message | nl2br }}
        </div>
    </div>

    <!-- Total prices -->
    <div class="grow-0">
        <table>
            <tr class="border-b border-gray-300">
                <th class="py-1.5 px-3 text-left font-semibold tracking-wide uppercase text-[8px] text-gray-600 min-w-[200px]">Frais de port HT</th>
                <td class="py-1.5 px-3 text-right whitespace-nowrap">
                    {{ shipment_method.cost_tax_excluded_formatted }}
                </td>
            </tr>

            {% if prices.total_discount_tax_excluded > 0 %}
                <tr class="border-b border-gray-300">
                    <th class="py-1.5 px-3 text-left font-semibold tracking-wide uppercase text-[8px] text-gray-600">Total remise HT</th>
                    <td class="py-1.5 px-3 text-right whitespace-nowrap">{{ prices.total_discount_tax_excluded | formatPrice }}</td>
                </tr>
            {% endif %}

            <tr class="border-b border-gray-300">
                <th class="py-1.5 px-3 text-left font-semibold tracking-wide uppercase text-[8px] text-gray-600">Base HT</th>
                <td class="py-1.5 px-3 text-right whitespace-nowrap">{{ prices.total_price_tax_excluded | formatPrice }}</td>
            </tr>

            <tr class="border-b border-gray-400">
                <th class="py-1.5 px-3 text-left font-semibold tracking-wide uppercase text-[8px] text-gray-600">TVA {{ vat_rate | formatFloatToPercent }}</th>
                <td class="py-1.5 px-3 text-right whitespace-nowrap">{{ prices.total_vat | formatPrice }}</td>
            </tr>

            <tr class="border-b border-gray-400 bg-gray-100 font-semibold">
                <th class="py-1.5 px-3 text-left tracking-wide uppercase text-[8px] text-gray-600">Net à payer</th>
                <td class="py-1.5 px-3 text-right whitespace-nowrap">{{ prices.total_price_tax_included | formatPrice }}</td>
            </tr>
        </table>
    </div>
</div>

</body>
</html>
