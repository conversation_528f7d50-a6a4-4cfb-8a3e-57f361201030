<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WMS - Utilisateur(s) et Entrepôt(s)</title>
    <style>
        .qrcode {
            top: -2mm;
            left: 10mm;
            border: 2mm solid white;
        }
        .barcode {
            right: 1mm;
            top: 1mm;
            min-height: 2.1cm;
        }
        .content {
            min-height: 2.3cm;
            margin-left: 42mm;
            padding-top: 4mm;
        }
    </style>
</head>
<body class="p-3">
{% if users|length > 0 %}
    <section>
        <h3 class="mb-3 uppercase">Utilisateur(s)</h3>
        {% for user in users %}
            <div class="relative bg-sidebar mb-3">
                <section class="absolute qrcode">
                    <img src="data:image/png;base64,{{ user.qrcode }}" width="90mm" class="border-0"/>
                </section>
                <div class="uppercase text-white content">
                    <div>{{ user.name }}</div>
                    <div class="text-xs font-light">{{ user.job }}</div>
                </div>
                <div class="bg-white absolute barcode flex justify-center items-center border border-sidebar" style="min-width: 120mm">
                    <div class="flex-col text-center mt-1">
                        <img style="max-width: 100mm; height: 12mm" src="data:image/png;base64,{{ user.barcode }}"/>
                        <span class="text-xs font-light text-gray-600">{{ user.account_identifier }}</span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </section>

    {% if warehouses|length > 0 %}
        <hr class="mt-5 mb-5">
    {% endif %}
{% endif %}

{% if warehouses|length > 0 %}
    <section>
        <h3 class="mb-3 uppercase">Entrepôt(s)</h3>
        {% for warehouse in warehouses %}
            <div class="relative bg-sidebar mb-3">
                <section class="absolute qrcode">
                    <img src="data:image/png;base64,{{ warehouse.qrcode }}" width="90mm" class="border-0"/>
                </section>
                <div class="uppercase text-white content">
                    <div>{{ warehouse.name }}</div>
                </div>
                <div class="bg-white absolute barcode flex justify-center items-center border border-sidebar" style="min-width: 120mm">
                    <div class="flex-col text-center mt-1">
                        <img style="max-width: 100mm; height: 12mm" src="data:image/png;base64,{{ warehouse.barcode }}"/>
                        <span class="text-xs font-light text-gray-600">{{ warehouse.identifier }}</span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </section>
{% endif %}
</body>
</html>
