<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Php73\Rector\String_\SensitiveHereNowDocRector;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Set\ValueObject\SetList;
use <PERSON>\Symfony\Set\SymfonySetList;
use <PERSON>\Symfony\Symfony28\Rector\MethodCall\GetToConstructorInjectionRector;

return RectorConfig::configure()
    ->withPaths([__DIR__ . '/config', __DIR__ . '/public', __DIR__ . '/src'])
    ->withPHPStanConfigs([__DIR__ . '/phpstan.neon'])
    ->withSymfonyContainerXml(__DIR__ . '/var/cache/dev/AppApp_KernelDevDebugContainer.xml')
    ->withCache(__DIR__ . '/tools/rector')
    ->withImportNames(true, true, false, true)
    ->withSets([
        LevelSetList::UP_TO_PHP_74,
        SymfonySetList::SYMFONY_44,
        SymfonySetList::SYMFONY_CODE_QUALITY,
        SymfonySetList::SYMFONY_CONSTRUCTOR_INJECTION,
        SetList::DEAD_CODE,
        SetList::CODE_QUALITY,
        SetList::TYPE_DECLARATION,
    ])
    ->withSkip([
        // autogenerated
        __DIR__ . '/config/bundles.php',
        __DIR__ . '/config/bootstrap.php',
        GetToConstructorInjectionRector::class => [
            // son-video/svd-rpc-bundle in not compatible w/ DI at the moment
            __DIR__ . '/src/App/Controller/Rpc',
        ],
        // phpStorm doesn't like SQL_WRAP heredoc
        SensitiveHereNowDocRector::class,
    ]);
